# Makefile for Next.js project

.PHONY: dev build start lint test typecheck check clean deep-clean install all help

# Default target
all: install build

# Install dependencies
install:
	@echo "Installing dependencies..."
	bun install

# Run development server with turbopack
dev:
	@echo "Starting development server..."
	bun run dev

# Build the application
build:
	@echo "Building for production..."
	bun run build

# Start the production server
start:
	@echo "Starting production server..."
	bun run start

# Run linting
lint:
	@echo "Running linter..."
	bun run lint

# Run tests
test:
	@echo "Running tests..."
	bun run test

# Run type checking
typecheck:
	@echo "Running type checking..."
	bun run typecheck

# Run all checks (lint and typecheck)
check: lint typecheck
	@echo "All checks completed."

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf .next
	rm -rf out
	rm -rf node_modules/.cache

# Deep clean (including node_modules)
deep-clean: clean
	@echo "Removing node_modules..."
	rm -rf node_modules

# Help command
help:
	@echo "Available commands:"
	@echo "  make install     - Install dependencies"
	@echo "  make dev         - Start development server"
	@echo "  make build       - Build for production"
	@echo "  make start       - Start production server"
	@echo "  make lint        - Run linter"
	@echo "  make test        - Run tests"
	@echo "  make typecheck   - Run TypeScript type checking"
	@echo "  make check       - Run all checks (lint and typecheck)"
	@echo "  make clean       - Clean build artifacts"
	@echo "  make deep-clean  - Clean build artifacts and node_modules"
	@echo "  make all         - Install dependencies and build (default)"
	@echo "  make help        - Show this help message"
