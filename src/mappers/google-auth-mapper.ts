import { GoogleProfile } from "@mug/models/user";

import { GoogleLoginRequestDto } from "@mug/services/dtos";

interface GoogleUserData {
  id: string;
  email: string;
  name?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  locale?: string;
  verified_email?: boolean;
  [key: string]: unknown;
}

function mapToGoogleProfile(data: GoogleUserData): GoogleProfile {
  const name =
    data.name ||
    `${data.given_name || ""} ${data.family_name || ""}`.trim() ||
    data.email.split("@")[0];

  return {
    id: data.id,
    email: data.email,
    name: name,
    picture: data.picture,
    locale: data.locale,
  };
}

function mapToGoogleLoginRequest(data: GoogleUserData): GoogleLoginRequestDto {
  const name =
    data.name ||
    `${data.given_name || ""} ${data.family_name || ""}`.trim() ||
    data.email.split("@")[0];

  return {
    googleId: data.id,
    email: data.email,
    name: name,
    picture: data.picture,
    locale: data.locale,
  };
}

function validateGoogleUserData(data: GoogleUserData): void {
  if (!data.id) {
    throw new Error("Google user ID is missing from the response");
  }
  if (!data.email) {
    throw new Error("Google user email is missing from the response");
  }
}

const googleAuthMapper = {
  mapToGoogleProfile,
  mapToGoogleLoginRequest,
  validateGoogleUserData,
};

export type { GoogleUserData };
export default googleAuthMapper;
