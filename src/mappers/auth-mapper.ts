import { Language, Theme } from "@mug/models/core";
import { TwoFactorMethod } from "@mug/models/auth";
import {
  User,
  NotificationPreferences,
  SecuritySettings,
  GoogleProfile,
} from "@mug/models/user";

import type { JwtPayload } from "@mug/services/auth";

import { AuthSessionDto } from "@mug/services/dtos";
interface UserJwtPayload extends JwtPayload {
  user_id: string;
  name?: string;
  email?: string;
  username?: string;
  avatar?: string;
  roles?: string[];
  phone?: string;
  timezone?: string;
  language?: Language;
  theme?: Theme;
  last_login?: string;
  is_email_verified?: boolean;
  is_phone_verified?: boolean;
  two_factor_enabled?: boolean;
  two_factor_method?: TwoFactorMethod;
  two_factor_verified?: boolean;
  google_connected?: boolean;
  google_id?: string;
  google_locale?: string;
  notification_preferences?: {
    sms?: boolean;
    whatsapp?: boolean;
    telegram?: boolean;
    email?: boolean;
    push?: boolean;
    in_app?: boolean;
    marketing?: boolean;
  };
  security_settings?: {
    login_notifications?: boolean;
    unusual_activity_alerts?: boolean;
    session_timeout?: number;
  };
  status?: boolean;
  tenant_id?: string;
  created_at?: string;
  updated_at?: string;
  ip?: string;
  user_agent?: string;
  device?: string;
  exp: number;
}

function mapTokenToUser(tokenData: UserJwtPayload): User {
  return {
    id: tokenData.user_id,
    name: tokenData.name || "",
    email: tokenData.email || "",
    username: tokenData.username,
    avatar: tokenData.avatar,
    role:
      tokenData.roles && tokenData.roles.length > 0
        ? tokenData.roles[0]
        : undefined,
    roles: tokenData.roles,
    phone: tokenData.phone,
    timezone: tokenData.timezone,
    language: tokenData.language,
    theme: tokenData.theme,
    lastLogin: tokenData.last_login,
    isEmailVerified: tokenData.is_email_verified,
    isPhoneVerified: tokenData.is_phone_verified,
    twoFactorEnabled: tokenData.two_factor_enabled,
    twoFactorMethod: tokenData.two_factor_method,
    googleConnected: tokenData.google_connected,
    googleProfile: tokenData.google_connected
      ? ({
          id: tokenData.google_id,
          email: tokenData.email,
          name: tokenData.name,
          picture: tokenData.avatar,
          locale: tokenData.google_locale,
        } as GoogleProfile)
      : undefined,
    notificationPreferences: tokenData.notification_preferences
      ? ({
          sms: tokenData.notification_preferences.sms,
          whatsapp: tokenData.notification_preferences.whatsapp,
          telegram: tokenData.notification_preferences.telegram,
          email: tokenData.notification_preferences.email,
          push: tokenData.notification_preferences.push,
          inApp: tokenData.notification_preferences.in_app,
          marketing: tokenData.notification_preferences.marketing,
        } as NotificationPreferences)
      : undefined,
    securitySettings: tokenData.security_settings
      ? ({
          loginNotifications: tokenData.security_settings.login_notifications,
          unusualActivityAlerts:
            tokenData.security_settings.unusual_activity_alerts,
          sessionTimeout: tokenData.security_settings.session_timeout,
        } as SecuritySettings)
      : undefined,
    status: tokenData.status,
    tenantId: tokenData.tenant_id,
    createdAt: tokenData.created_at || new Date().toISOString(),
    updatedAt: tokenData.updated_at || new Date().toISOString(),
  };
}

function mapTokenToSession(
  tokenData: UserJwtPayload,
  accessToken: string,
  refreshToken: string
): AuthSessionDto {
  return {
    id: tokenData.user_id,
    userId: tokenData.user_id,
    token: accessToken,
    refreshToken: refreshToken,
    expiresAt: new Date(tokenData.exp * 1000).toISOString(),
    createdAt: new Date().toISOString(),
    lastActiveAt: new Date().toISOString(),
    ip: tokenData.ip,
    userAgent: tokenData.user_agent,
    device: tokenData.device,
    requiresTwoFactor:
      Boolean(tokenData.two_factor_enabled) &&
      !Boolean(tokenData.two_factor_verified),
    twoFactorVerified: Boolean(tokenData.two_factor_verified),
    tenantId: tokenData.tenant_id,
  };
}

const authMapper = {
  mapTokenToUser,
  mapTokenToSession,
};

export type { UserJwtPayload };
export default authMapper;
