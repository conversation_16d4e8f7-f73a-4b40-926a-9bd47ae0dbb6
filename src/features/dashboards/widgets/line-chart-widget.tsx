"use client";

import { useEffect, useRef } from "react";
import { DashboardWidget, ChartData } from "@mug/models/dashboard";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@mug/components/ui/card";
import { useTheme } from "next-themes";
import Chart from "chart.js/auto";

interface LineChartWidgetProps {
  widget: DashboardWidget;
}

export function LineChartWidget({ widget }: LineChartWidgetProps) {
  const { title, config, data } = widget;
  const chartData = data as ChartData;
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);
  const { theme } = useTheme();
  
  useEffect(() => {
    if (!chartRef.current) return;
    
    // Destroy existing chart
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }
    
    const ctx = chartRef.current.getContext("2d");
    if (!ctx) return;
    
    // Get colors based on theme
    const textColor = theme === "dark" ? "rgba(255, 255, 255, 0.8)" : "rgba(0, 0, 0, 0.7)";
    const gridColor = theme === "dark" ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)";
    
    // Create new chart
    chartInstance.current = new Chart(ctx, {
      type: "line",
      data: {
        labels: chartData.labels,
        datasets: chartData.datasets.map(dataset => ({
          ...dataset,
          tension: config.tension || 0,
          fill: config.fill || false,
        })),
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: config.showLegend,
            position: "top",
            labels: {
              color: textColor,
              font: {
                size: 12,
              },
            },
          },
          tooltip: {
            enabled: true,
          },
        },
        scales: {
          x: {
            grid: {
              display: config.showGrid,
              color: gridColor,
            },
            ticks: {
              color: textColor,
            },
          },
          y: {
            grid: {
              display: config.showGrid,
              color: gridColor,
            },
            ticks: {
              color: textColor,
            },
          },
        },
      },
    });
    
    // Cleanup on unmount
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [chartData, config, theme]);
  
  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-2 h-[calc(100%-3rem)]">
        <div className="h-full w-full">
          <canvas ref={chartRef} />
        </div>
      </CardContent>
    </Card>
  );
}
