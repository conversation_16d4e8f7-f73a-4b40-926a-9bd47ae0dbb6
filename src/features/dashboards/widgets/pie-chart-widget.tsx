"use client";

import { useEffect, useRef } from "react";
import { DashboardWidget, ChartData } from "@mug/models/dashboard";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@mug/components/ui/card";
import { useTheme } from "next-themes";
import Chart from "chart.js/auto";

interface PieChartWidgetProps {
  widget: DashboardWidget;
}

export function PieChartWidget({ widget }: PieChartWidgetProps) {
  const { title, config, data, type } = widget;
  const chartData = data as ChartData;
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);
  const { theme } = useTheme();
  
  useEffect(() => {
    if (!chartRef.current) return;
    
    // Destroy existing chart
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }
    
    const ctx = chartRef.current.getContext("2d");
    if (!ctx) return;
    
    // Get colors based on theme
    const textColor = theme === "dark" ? "rgba(255, 255, 255, 0.8)" : "rgba(0, 0, 0, 0.7)";
    
    // Create new chart
    chartInstance.current = new Chart(ctx, {
      type: type === "donut_chart" ? "doughnut" : "pie",
      data: {
        labels: chartData.labels,
        datasets: chartData.datasets,
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: config.showLegend,
            position: "right",
            labels: {
              color: textColor,
              font: {
                size: 12,
              },
            },
          },
          tooltip: {
            enabled: true,
          },
        },
      },
    });
    
    // Cleanup on unmount
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [chartData, config, theme, type]);
  
  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-2 h-[calc(100%-3rem)]">
        <div className="h-full w-full">
          <canvas ref={chartRef} />
        </div>
      </CardContent>
    </Card>
  );
}
