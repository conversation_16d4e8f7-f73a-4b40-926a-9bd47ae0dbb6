"use client";

import { DashboardWidget } from "@mug/models/dashboard";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@mug/components/ui/card";
import { ArrowDown, ArrowUp, Minus } from "lucide-react";
import { LucideIcon } from "lucide-react";
import * as Icons from "lucide-react";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";

interface CounterWidgetProps {
  widget: DashboardWidget;
}

export function CounterWidget({ widget }: CounterWidgetProps) {
  const { title, config, data } = widget;
  
  // Format the value based on the config
  const formatValue = (value: number) => {
    if (!value) return "0";
    
    const format = config.format as string;
    
    if (format === "currency") {
      const currency = data.currency || "BRL";
      return new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency,
      }).format(value);
    }
    
    if (format === "percentage") {
      return `${value.toLocaleString("pt-BR", { maximumFractionDigits: 1 })}%`;
    }
    
    return value.toLocaleString("pt-BR");
  };
  
  // Get the icon component
  const getIconComponent = (iconName: string): LucideIcon => {
    const icon = Icons[iconName as keyof typeof Icons] as LucideIcon;
    return icon || Icons.Activity;
  };
  
  const IconComponent = config.icon ? getIconComponent(config.icon as string) : null;
  
  // Get the color class
  const getColorClass = (color: string) => {
    const colorMap: Record<string, string> = {
      blue: "text-blue-500",
      green: "text-green-500",
      red: "text-red-500",
      yellow: "text-yellow-500",
      purple: "text-purple-500",
      pink: "text-pink-500",
      indigo: "text-indigo-500",
      gray: "text-gray-500",
    };
    
    return colorMap[color] || "text-primary";
  };
  
  const iconColorClass = getColorClass(config.color as string);
  
  // Get the change indicator
  const getChangeIndicator = () => {
    if (!data.change) return null;
    
    const changeType = data.changeType || (data.change > 0 ? "positive" : data.change < 0 ? "negative" : "neutral");
    
    const colorClass = changeType === "positive" 
      ? "text-green-500" 
      : changeType === "negative" 
        ? "text-red-500" 
        : "text-gray-500";
    
    const Icon = changeType === "positive" 
      ? ArrowUp 
      : changeType === "negative" 
        ? ArrowDown 
        : Minus;
    
    return (
      <div className={classNameBuilder("flex items-center text-xs font-medium", colorClass)}>
        <Icon className="mr-1 h-3 w-3" />
        <span>{Math.abs(data.change).toLocaleString("pt-BR", { maximumFractionDigits: 1 })}%</span>
      </div>
    );
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {IconComponent && (
          <IconComponent className={classNameBuilder("h-4 w-4", iconColorClass)} />
        )}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue(data.value)}</div>
        <div className="flex items-center justify-between mt-1">
          <p className="text-xs text-muted-foreground">
            {data.period && `vs. período anterior (${data.period})`}
          </p>
          {getChangeIndicator()}
        </div>
      </CardContent>
    </Card>
  );
}
