"use client";

import { DashboardWidget, FunnelData } from "@mug/models/dashboard";
import { Card, CardContent, CardHeader, CardTitle } from "@mug/components/ui/card";
import { cn } from "@mug/lib/utils/class-name-builder";

interface FunnelWidgetProps {
  widget: DashboardWidget;
}

export function FunnelWidget({ widget }: FunnelWidgetProps) {
  const { title, config, data } = widget;
  const funnelData = data as FunnelData;
  
  // Calculate percentages if not provided
  const stages = funnelData.stages.map((stage, index, array) => {
    if (stage.percentage === undefined) {
      const maxValue = array[0].value;
      const percentage = maxValue > 0 ? (stage.value / maxValue) * 100 : 0;
      return { ...stage, percentage };
    }
    return stage;
  });
  
  // Get colors from config or use defaults
  const colors = (config.colors as string[]) || [
    "#4f46e5", "#0ea5e9", "#10b981", "#f59e0b", "#ef4444"
  ];
  
  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-4 h-[calc(100%-3rem)]">
        <div className="space-y-2">
          {stages.map((stage, index) => (
            <div key={index} className="space-y-1">
              <div className="flex justify-between items-center text-xs">
                <span>{stage.name}</span>
                <div className="flex items-center gap-2">
                  <span>{stage.value.toLocaleString()}</span>
                  {config.showPercentages && (
                    <span className="text-muted-foreground">
                      ({Math.round(stage.percentage || 0)}%)
                    </span>
                  )}
                </div>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full rounded-full transition-all"
                  style={{
                    width: `${stage.percentage}%`,
                    backgroundColor: colors[index % colors.length],
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
