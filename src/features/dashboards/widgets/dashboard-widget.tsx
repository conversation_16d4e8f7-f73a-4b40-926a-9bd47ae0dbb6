"use client";

import { DashboardWidget as DashboardWidgetType } from "@mug/models/dashboard";
import { CounterWidget } from "./counter-widget";
import { BarChartWidget } from "./bar-chart-widget";
import { LineChartWidget } from "./line-chart-widget";
import { PieChartWidget } from "./pie-chart-widget";
import { TableWidget } from "./table-widget";
import { FunnelWidget } from "./funnel-widget";
import { GaugeWidget } from "./gauge-widget";
import { Card, CardContent, CardHeader, CardTitle } from "@mug/components/ui/card";

interface DashboardWidgetProps {
  widget: DashboardWidgetType;
}

export function DashboardWidget({ widget }: DashboardWidgetProps) {
  // Render the appropriate widget based on type
  const renderWidget = () => {
    switch (widget.type) {
      case "counter":
        return <CounterWidget widget={widget} />;
      case "bar_chart":
        return <BarChartWidget widget={widget} />;
      case "line_chart":
        return <LineChartWidget widget={widget} />;
      case "pie_chart":
      case "donut_chart":
        return <PieChartWidget widget={widget} />;
      case "table":
        return <TableWidget widget={widget} />;
      case "funnel":
        return <FunnelWidget widget={widget} />;
      case "gauge":
        return <GaugeWidget widget={widget} />;
      default:
        return (
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="text-sm font-medium">{widget.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Widget type not supported: {widget.type}
              </p>
            </CardContent>
          </Card>
        );
    }
  };
  
  return renderWidget();
}
