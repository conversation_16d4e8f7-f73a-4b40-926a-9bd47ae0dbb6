"use client";

import { useEffect, useRef } from "react";
import { DashboardWidget } from "@mug/models/dashboard";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@mug/components/ui/card";
import { useTheme } from "next-themes";
import { <PERSON>D<PERSON>, <PERSON>U<PERSON>, Minus } from "lucide-react";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";

interface GaugeWidgetProps {
  widget: DashboardWidget;
}

export function GaugeWidget({ widget }: GaugeWidgetProps) {
  const { title, config, data } = widget;
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { theme } = useTheme();
  
  // Get the min and max values from config
  const min = (config.min as number) || 0;
  const max = (config.max as number) || 100;
  
  // Get the thresholds from config
  const thresholds = (config.thresholds as { value: number; color: string }[]) || [
    { value: 33, color: "#10b981" },
    { value: 66, color: "#f59e0b" },
    { value: 100, color: "#ef4444" }
  ];
  
  // Calculate the percentage
  const percentage = ((data.value - min) / (max - min)) * 100;
  
  // Get the color based on the value
  const getColor = (value: number) => {
    const threshold = thresholds.find(t => value <= t.value);
    return threshold ? threshold.color : thresholds[thresholds.length - 1].color;
  };
  
  const color = getColor(percentage);
  
  // Draw the gauge
  useEffect(() => {
    if (!canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;
    
    // Clear the canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Set dimensions
    const width = canvas.width;
    const height = canvas.height;
    const radius = Math.min(width, height) / 2 * 0.8;
    const centerX = width / 2;
    const centerY = height / 2;
    
    // Draw the background arc
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, Math.PI, 2 * Math.PI, false);
    ctx.lineWidth = 20;
    ctx.strokeStyle = theme === "dark" ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)";
    ctx.stroke();
    
    // Draw the value arc
    const angle = (percentage / 100) * Math.PI + Math.PI;
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, Math.PI, angle, false);
    ctx.lineWidth = 20;
    ctx.strokeStyle = color;
    ctx.stroke();
    
    // Draw the value text
    ctx.font = "bold 24px sans-serif";
    ctx.fillStyle = theme === "dark" ? "rgba(255, 255, 255, 0.9)" : "rgba(0, 0, 0, 0.9)";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillText(`${Math.round(data.value)}%`, centerX, centerY);
    
  }, [data.value, percentage, color, theme, min, max]);
  
  // Get the change indicator
  const getChangeIndicator = () => {
    if (!data.change) return null;
    
    const changeType = data.changeType || (data.change > 0 ? "positive" : data.change < 0 ? "negative" : "neutral");
    
    // For gauge, positive change might mean negative value (e.g., decrease in error rate)
    const isPositive = changeType === "positive";
    
    const colorClass = isPositive
      ? "text-green-500" 
      : changeType === "negative" 
        ? "text-red-500" 
        : "text-gray-500";
    
    const Icon = isPositive
      ? ArrowDown
      : changeType === "negative" 
        ? ArrowUp 
        : Minus;
    
    return (
      <div className={classNameBuilder("flex items-center text-xs font-medium", colorClass)}>
        <Icon className="mr-1 h-3 w-3" />
        <span>{Math.abs(data.change).toLocaleString("pt-BR", { maximumFractionDigits: 1 })}%</span>
      </div>
    );
  };
  
  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-4 h-[calc(100%-3rem)] flex flex-col items-center">
        <div className="relative w-full h-[120px]">
          <canvas ref={canvasRef} width={200} height={120} className="w-full h-full" />
        </div>
        {data.previousValue && (
          <div className="flex items-center justify-center mt-2 text-xs text-muted-foreground">
            <span>vs. período anterior ({data.previousValue}%)</span>
            {getChangeIndicator()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
