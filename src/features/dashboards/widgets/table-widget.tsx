"use client";

import { DashboardWidget, TableData } from "@mug/models/dashboard";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@mug/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@mug/components/ui/table";

interface TableWidgetProps {
  widget: DashboardWidget;
}

export function TableWidget({ widget }: TableWidgetProps) {
  const { title, config, data } = widget;
  const tableData = data as TableData;
  
  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-0 h-[calc(100%-3rem)] overflow-auto">
        <Table>
          {config.showHeader && (
            <TableHeader>
              <TableRow>
                {tableData.headers.map((header, index) => (
                  <TableHead key={index} className="text-xs">
                    {header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
          )}
          <TableBody>
            {tableData.rows.map((row, rowIndex) => (
              <TableRow key={rowIndex}>
                {row.map((cell, cellIndex) => (
                  <TableCell key={cellIndex} className="text-xs py-2">
                    {cell}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
