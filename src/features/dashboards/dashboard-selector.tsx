"use client";

import { useState, useEffect } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { Dashboard } from "@mug/models/dashboard";
import { readDashboards } from "@mug/services/dashboard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";
import { LayoutDashboard } from "lucide-react";

interface DashboardSelectorProps {
  selectedDashboardId: string;
  onDashboardChange: (dashboardId: string) => void;
}

export function DashboardSelector({
  selectedDashboardId,
  onDashboardChange,
}: DashboardSelectorProps) {
  const { t } = useTranslate();
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Load dashboards
  useEffect(() => {
    const loadDashboards = async () => {
      try {
        const data = await readDashboards();
        setDashboards(data);
      } catch (error) {
        console.error("Error loading dashboards:", error);
      } finally {
        setLoading(false);
      }
    };
    
    loadDashboards();
  }, []);
  
  // Handle dashboard change
  const handleDashboardChange = (value: string) => {
    onDashboardChange(value);
  };
  
  return (
    <div className="flex items-center gap-2">
      <LayoutDashboard className="h-4 w-4 text-muted-foreground" />
      <Select
        value={selectedDashboardId}
        onValueChange={handleDashboardChange}
        disabled={loading}
      >
        <SelectTrigger className="w-[240px]">
          <SelectValue placeholder={t("dashboards.selectDashboard")} />
        </SelectTrigger>
        <SelectContent>
          {dashboards.map((dashboard) => (
            <SelectItem key={dashboard.id} value={dashboard.id}>
              {dashboard.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
