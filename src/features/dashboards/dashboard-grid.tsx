"use client";

import { Dashboard } from "@mug/models/dashboard";
import { DashboardWidget } from "./widgets/dashboard-widget";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";

interface DashboardGridProps {
  dashboard: Dashboard;
}

export function DashboardGrid({ dashboard }: DashboardGridProps) {
  // Get the size class for a widget
  const getSizeClass = (size: string) => {
    switch (size) {
      case "small":
        return "col-span-1 row-span-1";
      case "medium":
        return "col-span-2 row-span-1";
      case "large":
        return "col-span-2 row-span-2";
      case "wide":
        return "col-span-3 row-span-1";
      case "tall":
        return "col-span-1 row-span-2";
      case "full":
        return "col-span-3 row-span-2";
      default:
        return "col-span-1 row-span-1";
    }
  };
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-[minmax(120px,auto)]">
      {dashboard.widgets.map((widget) => (
        <div
          key={widget.id}
          className={classNameBuilder(
            "h-full",
            getSizeClass(widget.size)
          )}
        >
          <DashboardWidget widget={widget} />
        </div>
      ))}
    </div>
  );
}
