"use client";

// React and hooks
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslate } from "@mug/contexts/translate";

// Form validation
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// Models and types
import type {
  FlowType,
  FlowVisibility,
  FlowWithMembers,
} from "@mug/models/flow";
import type { UpdateFlowDTO } from "@mug/services/flow";
import { mockUsers } from "@mug/constants/mock-lanes";

// Icons
import {
  Check,
  Globe,
  Lock,
  Users,
  Search,
  X,
  Plus,
  HelpCircle,
  Trash2,
  Archive,
} from "lucide-react";

// UI Components - Dialog
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";

// UI Components - Form
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@mug/components/ui/form";

// UI Components - Select
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";

// UI Components - Tooltip
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@mug/components/ui/tooltip";

// UI Components - Command
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";

// UI Components - Popover
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";

// UI Components - Other
import { Input } from "@mug/components/ui/input";
import { Button } from "@mug/components/ui/button";
import { Textarea } from "@mug/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import { Badge } from "@mug/components/ui/badge";
import { ScrollArea } from "@mug/components/ui/scroll-area";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";

interface EditFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateFlow: (flowId: string, flowData: UpdateFlowDTO) => void;
  onDeleteFlow?: (flowId: string) => void;
  onArchiveFlow?: (flowId: string, archive: boolean) => void;
  flow: FlowWithMembers;
}

// Validation schema
const formSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().optional(),
  type: z.string().optional() as z.ZodType<FlowType | undefined>, // Type is read-only, not validated
  visibility: z.enum([
    "public",
    "private",
    "shared",
  ]) as z.ZodType<FlowVisibility>,
  members: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
});

export function EditFlowModal({
  isOpen,
  onClose,
  onUpdateFlow,
  onDeleteFlow,
  onArchiveFlow,
  flow,
}: EditFlowModalProps) {
  const { t } = useTranslate();
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [showMembersSelect, setShowMembersSelect] = useState(false);
  const [userFilter, setUserFilter] = useState("");
  const [tagInput, setTagInput] = useState("");

  // State for delete confirmation
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Suggested tags (in a real app, these would come from an API)
  const suggestedTags = [
    "important",
    "urgent",
    "high-priority",
    "low-priority",
    "feature",
    "bug",
    "enhancement",
    "documentation",
    "frontend",
    "backend",
    "design",
    "testing",
  ];

  // Convert flow mode to expected type
  const getFlowType = (mode: string): FlowType => {
    const normalizedMode = mode.toLowerCase();
    if (
      [
        "sales",
        "marketing",
        "support",
        "development",
        "onboarding",
        "recruitment",
      ].includes(normalizedMode)
    ) {
      return normalizedMode as FlowType;
    }
    return "other";
  };

  // Initialize form with existing flow values
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: flow.name || "",
      description: flow.description || "",
      type: getFlowType(flow.mode),
      visibility: (flow.isArchived ? "private" : "public") as FlowVisibility,
      members: flow.members?.map((member) => member.id) || [],
      tags: flow.tags || [],
    },
  });

  // Initialize selected members state
  useEffect(() => {
    if (flow.members && flow.members.length > 0) {
      setSelectedMembers(
        flow.members.map((member: { id: string }) => member.id)
      );
      setShowMembersSelect(true);
      form.setValue("visibility", "shared");
    } else {
      setShowMembersSelect(form.getValues("visibility") === "shared");
    }
  }, [flow, form]);

  // Reset form when modal is closed or flow changes
  useEffect(() => {
    if (isOpen) {
      form.reset({
        title: flow.name || "",
        description: flow.description || "",
        type: getFlowType(flow.mode),
        visibility: (flow.isArchived ? "private" : "public") as FlowVisibility,
        members: flow.members?.map((member: { id: string }) => member.id) || [],
        tags: flow.tags || [],
      });

      if (flow.members && flow.members.length > 0) {
        setSelectedMembers(
          flow.members.map((member: { id: string }) => member.id)
        );
        setShowMembersSelect(true);
        form.setValue("visibility", "shared");
      } else {
        setShowMembersSelect(form.getValues("visibility") === "shared");
      }
    }
  }, [isOpen, flow, form]);

  // Update member selector visibility based on selected visibility
  const handleVisibilityChange = (value: FlowVisibility) => {
    form.setValue("visibility", value);
    setShowMembersSelect(value === "shared");

    // If not "shared", clear selected members
    if (value !== "shared") {
      form.setValue("members", []);
      setSelectedMembers([]);
    }
  };

  // Add or remove a member from selection
  const toggleMember = (userId: string) => {
    const isSelected = selectedMembers.includes(userId);
    let newSelectedMembers: string[];

    if (isSelected) {
      newSelectedMembers = selectedMembers.filter((id) => id !== userId);
    } else {
      newSelectedMembers = [...selectedMembers, userId];
    }

    setSelectedMembers(newSelectedMembers);
    form.setValue("members", newSelectedMembers);
  };

  // Tag handling functions
  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (!trimmedTag) return;

    // Validate tag
    if (trimmedTag.length < 2) {
      // Too short
      return;
    }
    if (trimmedTag.length > 20) {
      // Too long
      return;
    }
    if (!/^[a-zA-Z0-9\-_]+$/.test(trimmedTag)) {
      // Invalid characters
      return;
    }

    const currentTags = form.getValues("tags") || [];
    if (currentTags.includes(trimmedTag)) {
      // Tag already exists
      return;
    }

    form.setValue("tags", [...currentTags, trimmedTag]);
    setTagInput("");
  };

  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue(
      "tags",
      currentTags.filter((tag: string) => tag !== tagToRemove)
    );
  };

  // Function to handle form submission
  const onSubmit = (values: Record<string, unknown>) => {
    const flowData: UpdateFlowDTO = {
      title: values.title as string,
      description: values.description as string | undefined,
      type: flow.mode ? getFlowType(flow.mode) : "other", // Use original flow type, not from form
      visibility: values.visibility as FlowVisibility,
      members: (values.members as string[]) || [],
      tags: (values.tags as string[]) || [],
    };

    onUpdateFlow(flow.id, flowData);
    form.reset();
    onClose();
  };

  // Function to handle flow deletion
  const handleDelete = () => {
    if (onDeleteFlow) {
      onDeleteFlow(flow.id);
      onClose();
    }
  };

  // Function to handle flow archiving/unarchiving
  const handleArchive = () => {
    if (onArchiveFlow) {
      // Toggle the archived state
      onArchiveFlow(flow.id, !flow.isArchived);
      onClose();
    }
  };

  return (
    <>
      <Form {...form}>
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
          <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] gap-0 flex flex-col p-0">
            <DialogHeader className="px-6 py-4 border-b">
              <DialogTitle>{t("flows.editFlow")}</DialogTitle>
              <DialogDescription>
                {t("flows.editFlowDescription")}
              </DialogDescription>
            </DialogHeader>
          <div className="flex-1 flex flex-col overflow-auto py-4">
            <form
              id="edit-flow-form"
              onSubmit={form.handleSubmit((data) =>
                onSubmit(data as Record<string, unknown>)
              )}
              className="grid grid-cols-8 gap-4 px-6 flex-1"
            >
              {/* Title */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center">
                      <FormLabel className="gap-0">
                        {t("common.title")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                    </div>
                    <FormControl>
                      <Input
                        placeholder={t("flows.flowTitlePlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <FormLabel>{t("common.description")}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t("flows.flowDescriptionPlaceholder")}
                        className="resize-none min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Flow Mode (Read-only) */}
              <FormItem className="col-span-2">
                <div className="flex items-center h-6">
                  <FormLabel className="gap-0">
                    {t("flows.flowMode")}
                  </FormLabel>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                      </TooltipTrigger>
                      <TooltipContent className="p-3">
                        <p>{t("flows.modeCannotBeChanged")}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="flex h-9 w-full rounded-md border border-input bg-muted px-3 py-1 text-sm text-muted-foreground items-center">
                  {flow.mode
                    ? t(`flows.modes.${flow.mode.toLowerCase()}`)
                    : t("common.notSet")}
                </div>
              </FormItem>

              {/* Flow Type (Read-only) */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("flows.flowType")}
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("flows.typeCannotBeChanged")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <FormControl>
                      <div className="flex h-9 w-full rounded-md border border-input bg-muted px-3 py-1 text-sm text-muted-foreground items-center">
                        {t(`flows.types.${field.value}`)}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Template (Read-only) */}
              <FormItem className="col-span-2">
                <div className="flex items-center h-6">
                  <FormLabel className="gap-0">
                    {t("flows.initialTemplate")}
                  </FormLabel>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                      </TooltipTrigger>
                      <TooltipContent className="p-3">
                        <p>{t("flows.templateCannotBeChanged")}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="flex h-9 w-full rounded-md border border-input bg-muted px-3 py-1 text-sm text-muted-foreground items-center">
                  {t("common.notAvailable")}
                </div>
              </FormItem>

              {/* Visibility */}
              <FormField
                control={form.control}
                name="visibility"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("flows.visibility")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("flows.visibilityTooltip")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select
                      onValueChange={(value) =>
                        handleVisibilityChange(value as FlowVisibility)
                      }
                      value={field.value}
                    >
                      <FormControl className="w-full">
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t("flows.selectVisibility")}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="public">
                          {t("flows.visibility.public")}
                        </SelectItem>
                        <SelectItem value="private">
                          {t("flows.visibility.private")}
                        </SelectItem>
                        <SelectItem value="shared">
                          {t("flows.visibility.shared")}
                        </SelectItem>
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Tags */}
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("flows.tags")}
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("flows.tagsDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    <div className="space-y-4">
                      {/* Selected tags */}
                      <div className="flex flex-wrap gap-2 mb-2">
                        {field.value?.map((tag: string) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="px-3 py-1"
                          >
                            {tag}
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="h-4 w-4 ml-1 -mr-1"
                              onClick={() => removeTag(tag)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>

                      {/* Tag input with autocomplete */}
                      <div className="flex gap-2">
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer">
                                {tagInput || t("flows.addTag")}
                              </div>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent
                            className="p-0 w-[300px]"
                            align="start"
                            side="bottom"
                            sideOffset={5}
                          >
                            <div className="border-b px-3 py-2 text-xs text-muted-foreground">
                              {t("flows.tagsHelpText")}
                            </div>
                            <Command>
                              <CommandInput
                                placeholder={t("flows.searchTags")}
                                value={tagInput}
                                onValueChange={setTagInput}
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {tagInput.trim() !== "" ? (
                                    <div className="py-3 px-4 flex flex-col items-center">
                                      <p className="text-center mb-2">
                                        {t("flows.noTagsFound")}
                                      </p>
                                      <Button
                                        variant="default"
                                        size="sm"
                                        className="w-full"
                                        onClick={() => {
                                          addTag(tagInput);
                                          document
                                            .querySelector(
                                              '[data-state="open"]'
                                            )
                                            ?.dispatchEvent(
                                              new KeyboardEvent("keydown", {
                                                key: "Escape",
                                              })
                                            );
                                        }}
                                      >
                                        <Plus className="mr-2 h-4 w-4" />
                                        {t("flows.createTag")} &quot;{tagInput}
                                        &quot;
                                      </Button>
                                    </div>
                                  ) : (
                                    <p className="py-2 px-4">
                                      {t("flows.typeToSearch")}
                                    </p>
                                  )}
                                </CommandEmpty>

                                {/* Create new tag option always visible */}
                                {tagInput.trim() !== "" && (
                                  <CommandGroup heading={t("flows.createNew")}>
                                    <CommandItem
                                      onSelect={() => {
                                        addTag(tagInput);
                                        document
                                          .querySelector('[data-state="open"]')
                                          ?.dispatchEvent(
                                            new KeyboardEvent("keydown", {
                                              key: "Escape",
                                            })
                                          );
                                      }}
                                    >
                                      <Plus className="mr-2 h-4 w-4" />
                                      <span className="font-medium">
                                        {t("flows.createTag")} &quot;{tagInput}
                                        &quot;
                                      </span>
                                    </CommandItem>
                                  </CommandGroup>
                                )}

                                <CommandGroup
                                  heading={t("flows.suggestedTags")}
                                >
                                  {suggestedTags
                                    .filter(
                                      (tag) =>
                                        !(field.value || []).includes(tag) &&
                                        tag
                                          .toLowerCase()
                                          .includes(tagInput.toLowerCase())
                                    )
                                    .map((tag) => (
                                      <CommandItem
                                        key={tag}
                                        value={tag}
                                        onSelect={(value) => {
                                          addTag(value);
                                          document
                                            .querySelector(
                                              '[data-state="open"]'
                                            )
                                            ?.dispatchEvent(
                                              new KeyboardEvent("keydown", {
                                                key: "Escape",
                                              })
                                            );
                                        }}
                                      >
                                        <span>{tag}</span>
                                      </CommandItem>
                                    ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>

                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Members (only visible when "shared" is selected) */}
              {showMembersSelect && (
                <FormField
                  control={form.control}
                  name="members"
                  render={() => (
                    <FormItem className="col-span-8">
                      <div className="flex items-center h-6">
                        <FormLabel className="gap-0">
                          {t("flows.members")}
                        </FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3">
                              <p>{t("flows.membersDescription")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="relative mb-2">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder={t("flows.searchMembers")}
                          className="pl-8"
                          value={userFilter}
                          onChange={(e) => setUserFilter(e.target.value)}
                        />
                      </div>
                      <ScrollArea className="h-[200px] border rounded-md p-4">
                        <div className="space-y-2">
                          {mockUsers
                            .filter((user) =>
                              user.name
                                .toLowerCase()
                                .includes(userFilter.toLowerCase())
                            )
                            .map((user) => (
                              <div
                                key={user.id}
                                className={`flex items-center justify-between p-2 rounded-md cursor-pointer hover:bg-muted/50 ${
                                  selectedMembers.includes(user.id)
                                    ? "bg-muted"
                                    : ""
                                }`}
                                onClick={() => toggleMember(user.id)}
                              >
                                <div className="flex items-center gap-3">
                                  <Avatar className="h-8 w-8">
                                    <AvatarImage
                                      src={user.avatar}
                                      alt={user.name}
                                    />
                                    <AvatarFallback>
                                      {user.name
                                        .split(" ")
                                        .map((n) => n[0])
                                        .join("")}
                                    </AvatarFallback>
                                  </Avatar>
                                  <span>{user.name}</span>
                                </div>
                                {selectedMembers.includes(user.id) && (
                                  <Badge variant="outline" className="ml-auto">
                                    {t("flows.selected")}
                                  </Badge>
                                )}
                              </div>
                            ))}
                        </div>
                      </ScrollArea>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Visibility Alert */}
              <div className="col-span-8">
                {form.watch("visibility") === "public" && (
                  <Alert className="flex items-start gap-4">
                    <Globe className="h-5 w-5 mt-0.5" />
                    <div>
                      <AlertTitle>{t("flows.visibility.public")}</AlertTitle>
                      <AlertDescription>
                        {t("flows.visibility.publicDescription")}
                      </AlertDescription>
                    </div>
                  </Alert>
                )}
                {form.watch("visibility") === "private" && (
                  <Alert className="flex items-start gap-4">
                    <Lock className="h-5 w-5 mt-0.5" />
                    <div>
                      <AlertTitle>{t("flows.visibility.private")}</AlertTitle>
                      <AlertDescription>
                        {t("flows.visibility.privateDescription")}
                      </AlertDescription>
                    </div>
                  </Alert>
                )}
                {form.watch("visibility") === "shared" && (
                  <Alert className="flex items-start gap-4">
                    <Users className="h-5 w-5 mt-0.5" />
                    <div>
                      <AlertTitle>{t("flows.visibility.shared")}</AlertTitle>
                      <AlertDescription>
                        {t("flows.visibility.sharedDescription")}
                      </AlertDescription>
                    </div>
                  </Alert>
                )}
              </div>
            </form>
          </div>
          <DialogFooter className="px-6 py-4 border-t">
            <div className="flex justify-between w-full">
              <div className="flex gap-2">
                {onDeleteFlow && (
                  <Button
                    type="button"
                    variant="destructive"
                    onClick={() => setShowDeleteConfirm(true)}
                    className="flex items-center gap-1"
                  >
                    <Trash2 className="h-4 w-4" />
                    {t("common.delete")}
                  </Button>
                )}
                {onArchiveFlow && (
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={handleArchive}
                    className="flex items-center gap-1"
                  >
                    <Archive className="h-4 w-4" />
                    {flow.isArchived ? t("common.unarchive") : t("common.archive")}
                  </Button>
                )}
              </div>
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  {t("common.cancel")}
                </Button>
                <Button type="submit" form="edit-flow-form">
                  <Check className="h-4 w-4 mr-1" />
                  {t("common.save")}
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Form>

    {/* Delete confirmation dialog */}
    <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t("flows.deleteFlowConfirmTitle")}</AlertDialogTitle>
          <AlertDialogDescription>
            {t("flows.deleteFlowConfirmDescription")}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {t("common.confirmDelete")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
    </>
  );
}
