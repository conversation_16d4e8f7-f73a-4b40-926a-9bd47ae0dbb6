"use client";

// React and hooks
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslate } from "@mug/contexts/translate";

// Form validation
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// Models and types
import { FlowMode, FlowTemplate, FlowVisibility, FlowType } from "@mug/models/flow";
import type { CreateFlowDTO } from "@mug/services/flow";
import { mockUsers } from "@mug/constants/mock-lanes";

// Icons
import { Check, Globe, Lock, Users, Search, X, Plus, HelpCircle } from "lucide-react";

// UI Components - Dialog
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";

// UI Components - Form
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@mug/components/ui/form";

// UI Components - Select
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";

// UI Components - Tooltip
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@mug/components/ui/tooltip";

// UI Components - Command
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";

// UI Components - Popover
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";

// UI Components - Other
import { Input } from "@mug/components/ui/input";
import { Button } from "@mug/components/ui/button";
import { Textarea } from "@mug/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import { Badge } from "@mug/components/ui/badge";
import { ScrollArea } from "@mug/components/ui/scroll-area";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";

interface CreateFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateFlow: (flowData: CreateFlowDTO) => void;
}

// Validation schema
const formSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().optional(),
  type: z.enum(["sales", "marketing", "support", "development", "onboarding", "recruitment", "other"]) as z.ZodType<FlowType>,
  template: z.enum(["empty", "basic", "sales", "support", "marketing"]) as z.ZodType<FlowTemplate>,
  mode: z.nativeEnum(FlowMode),
  visibility: z.enum(["public", "private", "shared"]) as z.ZodType<FlowVisibility>,
  members: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
});



export function CreateFlowModal(props: CreateFlowModalProps) {
  const { isOpen, onClose, onCreateFlow } = props;
  const { t } = useTranslate();
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [showMembersSelect, setShowMembersSelect] = useState(false);
  const [userFilter, setUserFilter] = useState("");
  const [tagInput, setTagInput] = useState("");

  // Suggested tags (in a real app, these would come from an API)
  const suggestedTags = [
    "important", "urgent", "high-priority", "low-priority",
    "feature", "bug", "enhancement", "documentation",
    "frontend", "backend", "design", "testing"
  ];

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      type: "sales",
      template: "basic",
      mode: FlowMode.ATTRACT,
      visibility: "public",
      members: [],
      tags: [],
    },
  });

  useEffect(() => {
    if (!isOpen) {
      form.reset();
      setSelectedMembers([]);
      setShowMembersSelect(false);
    }
  }, [isOpen, form]);

  const handleVisibilityChange = (value: string) => {
    const visibilityValue = value as FlowVisibility;
    form.setValue("visibility", visibilityValue);
    setShowMembersSelect(visibilityValue === "shared");

    if (visibilityValue !== "shared") {
      form.setValue("members", []);
      setSelectedMembers([]);
    }
  };

  const toggleMember = (userId: string) => {
    const isSelected = selectedMembers.includes(userId);
    let newSelectedMembers: string[];

    if (isSelected) {
      newSelectedMembers = selectedMembers.filter((id) => id !== userId);
    } else {
      newSelectedMembers = [...selectedMembers, userId];
    }

    setSelectedMembers(newSelectedMembers);
    form.setValue("members", newSelectedMembers);
  };

  // Tag handling functions
  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (!trimmedTag) return;

    // Validate tag
    if (trimmedTag.length < 2) {
      // Too short
      return;
    }
    if (trimmedTag.length > 20) {
      // Too long
      return;
    }
    if (!/^[a-zA-Z0-9\-_]+$/.test(trimmedTag)) {
      // Invalid characters
      return;
    }

    const currentTags = form.getValues("tags") || [];
    if (currentTags.includes(trimmedTag)) {
      // Tag already exists
      return;
    }

    form.setValue("tags", [...currentTags, trimmedTag]);
    setTagInput("");
  };

  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue("tags", currentTags.filter(tag => tag !== tagToRemove));
  };



  const onSubmit = (values: Record<string, unknown>) => {
    const flowData: CreateFlowDTO = {
      title: values.title as string,
      description: values.description as string | undefined,
      type: values.type as FlowType,
      template: values.template as FlowTemplate,
      mode: values.mode as FlowMode,
      visibility: values.visibility as FlowVisibility,
      members: (values.members as string[]) || [],
      tags: (values.tags as string[]) || [],
    };

    onCreateFlow(flowData);
    form.reset();
    onClose();
  };

  const renderTemplatePreview = (template: string) => {
    switch (template) {
      case "empty":
        return (
          <div className="flex flex-col items-center justify-center h-32 bg-muted/30 rounded-md p-2 text-center">
            <p className="text-sm text-muted-foreground">
              {t("flows.templates.emptyDescription")}
            </p>
          </div>
        );
      case "basic":
        return (
          <div className="flex space-x-2 h-32 bg-muted/30 rounded-md p-2 overflow-x-auto">
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.backlog")}
              </div>
              <div className="w-full h-3 bg-muted rounded mb-1"></div>
              <div className="w-full h-3 bg-muted rounded mb-1"></div>
              <div className="w-full h-3 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.inProgress")}
              </div>
              <div className="w-full h-3 bg-muted rounded mb-1"></div>
              <div className="w-full h-3 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.done")}
              </div>
              <div className="w-full h-3 bg-muted rounded mb-1"></div>
              <div className="w-full h-3 bg-muted rounded"></div>
            </div>
          </div>
        );
      case "sales":
        return (
          <div className="flex space-x-2 h-32 bg-muted/30 rounded-md p-2 overflow-x-auto">
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.leads")}
              </div>
              <div className="w-full h-2 bg-muted rounded mb-1"></div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.qualification")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.proposal")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.negotiation")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.closed")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
          </div>
        );
      case "marketing":
        return (
          <div className="flex space-x-2 h-32 bg-muted/30 rounded-md p-2 overflow-x-auto">
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.ideas")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.planning")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.production")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.review")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.published")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
          </div>
        );
      case "support":
        return (
          <div className="flex space-x-2 h-32 bg-muted/30 rounded-md p-2 overflow-x-auto">
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.new")}
              </div>
              <div className="w-full h-2 bg-muted rounded mb-1"></div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.analyzing")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.inProgress")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.waitingClient")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
            <div className="flex-shrink-0 w-32 h-full bg-background rounded border border-border p-2">
              <div className="text-xs font-medium mb-2 truncate">
                {t("flows.templates.resolved")}
              </div>
              <div className="w-full h-2 bg-muted rounded"></div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Form {...form}>
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] gap-0 flex flex-col p-0">
          <DialogHeader className="px-6 py-4 border-b">
            <DialogTitle>{t("flows.newFlow")}</DialogTitle>
            <DialogDescription>
              {t("flows.createFlowDescription")}
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 flex flex-col overflow-auto py-4">
            <form
              id="create-flow-form"
              onSubmit={form.handleSubmit((data) => onSubmit(data as Record<string, unknown>))}
              className="grid grid-cols-8 gap-4 px-6 flex-1"
            >

              {/* Title */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("common.title")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                    </div>
                    <FormControl>
                      <Input
                        placeholder={t("flows.flowTitlePlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("common.description")}
                      </FormLabel>
                    </div>
                    <FormControl>
                      <Textarea
                        placeholder={t("flows.flowDescriptionPlaceholder")}
                        className="resize-none min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Flow Mode */}
              <FormField
                control={form.control}
                name="mode"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("flows.flowMode")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("flows.modeDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select
                      onValueChange={(value) => field.onChange(value)}
                      value={field.value}
                    >
                      <FormControl className="w-full">
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t("flows.selectFlowMode")}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={FlowMode.ATTRACT}>
                          {t("flows.modes.attract")}
                        </SelectItem>
                        <SelectItem value={FlowMode.CONVERT}>
                          {t("flows.modes.convert")}
                        </SelectItem>
                        <SelectItem value={FlowMode.ENGAGE}>
                          {t("flows.modes.engage")}
                        </SelectItem>
                        <SelectItem value={FlowMode.CLOSE}>
                          {t("flows.modes.close")}
                        </SelectItem>
                        <SelectItem value={FlowMode.DELIGHT}>
                          {t("flows.modes.delight")}
                        </SelectItem>
                        <SelectItem value={FlowMode.ANALYZE}>
                          {t("flows.modes.analyze")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Flow Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("flows.flowType")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("flows.typeDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl className="w-full">
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t("flows.selectFlowType")}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="sales">
                          {t("flows.types.sales")}
                        </SelectItem>
                        <SelectItem value="marketing">
                          {t("flows.types.marketing")}
                        </SelectItem>
                        <SelectItem value="support">
                          {t("flows.types.support")}
                        </SelectItem>
                        <SelectItem value="development">
                          {t("flows.types.development")}
                        </SelectItem>
                        <SelectItem value="onboarding">
                          {t("flows.types.onboarding")}
                        </SelectItem>
                        <SelectItem value="recruitment">
                          {t("flows.types.recruitment")}
                        </SelectItem>
                        <SelectItem value="other">
                          {t("flows.types.other")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Template */}
              <FormField
                control={form.control}
                name="template"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("flows.initialTemplate")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("flows.templateDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl className="w-full">
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t("flows.selectTemplate")}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="empty">
                          {t("flows.templates.empty")}
                        </SelectItem>
                        <SelectItem value="basic">
                          {t("flows.templates.basic")}
                        </SelectItem>
                        <SelectItem value="sales">
                          {t("flows.templates.sales")}
                        </SelectItem>
                        <SelectItem value="marketing">
                          {t("flows.templates.marketing")}
                        </SelectItem>
                        <SelectItem value="support">
                          {t("flows.templates.support")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Visibility */}
              <FormField
                control={form.control}
                name="visibility"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("flows.visibility")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("flows.visibilityTooltip")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select
                      onValueChange={(value) =>
                        handleVisibilityChange(value as FlowVisibility)
                      }
                      value={field.value}
                    >
                      <FormControl className="w-full">
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t("flows.selectVisibility")}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="public">
                          {t("flows.visibility.public")}
                        </SelectItem>
                        <SelectItem value="private">
                          {t("flows.visibility.private")}
                        </SelectItem>
                        <SelectItem value="shared">
                          {t("flows.visibility.shared")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Tags */}
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("flows.tags")}
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("flows.tagsDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    <div className="space-y-4">
                      {/* Selected tags */}
                      <div className="flex flex-wrap gap-2 mb-2">
                        {field.value?.map((tag) => (
                          <Badge key={tag} variant="secondary" className="px-3 py-1">
                            {tag}
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="h-4 w-4 ml-1 -mr-1"
                              onClick={() => removeTag(tag)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>

                      {/* Tag input with autocomplete */}
                      <div className="flex gap-2">
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer">
                                {tagInput || t("flows.addTag")}
                              </div>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="p-0 w-[300px]" align="start" side="bottom" sideOffset={5}>
                            <div className="border-b px-3 py-2 text-xs text-muted-foreground">
                              {t("flows.tagsHelpText")}
                            </div>
                            <Command>
                              <CommandInput
                                placeholder={t("flows.searchTags")}
                                value={tagInput}
                                onValueChange={setTagInput}
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {tagInput.trim() !== "" ? (
                                    <div className="py-3 px-4 flex flex-col items-center">
                                      <p className="text-center mb-2">{t("flows.noTagsFound")}</p>
                                      <Button
                                        variant="default"
                                        size="sm"
                                        className="w-full"
                                        onClick={() => {
                                          addTag(tagInput);
                                          document.querySelector('[data-state="open"]')?.dispatchEvent(
                                            new KeyboardEvent('keydown', { key: 'Escape' })
                                          );
                                        }}
                                      >
                                        <Plus className="mr-2 h-4 w-4" />
                                        {t("flows.createTag")} &quot;{tagInput}&quot;
                                      </Button>
                                    </div>
                                  ) : (
                                    <p className="py-2 px-4">{t("flows.typeToSearch")}</p>
                                  )}
                                </CommandEmpty>

                                {/* Create new tag option always visible */}
                                {tagInput.trim() !== "" && (
                                  <CommandGroup heading={t("flows.createNew")}>
                                    <CommandItem
                                      onSelect={() => {
                                        addTag(tagInput);
                                        document.querySelector('[data-state="open"]')?.dispatchEvent(
                                          new KeyboardEvent('keydown', { key: 'Escape' })
                                        );
                                      }}
                                    >
                                      <Plus className="mr-2 h-4 w-4" />
                                      <span className="font-medium">{t("flows.createTag")} &quot;{tagInput}&quot;</span>
                                    </CommandItem>
                                  </CommandGroup>
                                )}

                                <CommandGroup heading={t("flows.suggestedTags")}>
                                  {suggestedTags
                                    .filter(tag =>
                                      !(field.value || []).includes(tag) &&
                                      tag.toLowerCase().includes(tagInput.toLowerCase())
                                    )
                                    .map((tag) => (
                                      <CommandItem
                                        key={tag}
                                        value={tag}
                                        onSelect={(value) => {
                                          addTag(value);
                                          document.querySelector('[data-state="open"]')?.dispatchEvent(
                                            new KeyboardEvent('keydown', { key: 'Escape' })
                                          );
                                        }}
                                      >
                                        <span>{tag}</span>
                                      </CommandItem>
                                    ))
                                  }
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>

                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Template Preview */}
              <div className="col-span-8">
                <div className="border rounded-md overflow-hidden">
                  <div className="bg-muted/30 px-4 py-2 border-b flex items-center">
                    <h4 className="font-medium text-sm">
                      {t("flows.templatePreview")}
                    </h4>
                  </div>
                  <div className="bg-background">
                    {form.watch("template") &&
                      renderTemplatePreview(form.watch("template"))}
                  </div>
                </div>
              </div>

              {/* Members (only visible when "shared" is selected) */}
              {showMembersSelect && (
                <FormField
                  control={form.control}
                  name="members"
                  render={() => (
                    <FormItem className="col-span-8">
                      <div className="flex items-center h-6">
                        <FormLabel className="gap-0">
                          {t("flows.members")}
                        </FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3">
                              <p>{t("flows.membersDescription")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="relative mb-2">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder={t("flows.searchMembers")}
                          className="pl-8"
                          value={userFilter}
                          onChange={(e) => setUserFilter(e.target.value)}
                        />
                      </div>
                      <ScrollArea className="h-[200px] border rounded-md p-4">
                        <div className="space-y-2">
                          {mockUsers
                            .filter((user) =>
                              user.name
                                .toLowerCase()
                                .includes(userFilter.toLowerCase())
                            )
                            .map((user) => (
                              <div
                                key={user.id}
                                className={`flex items-center justify-between p-2 rounded-md cursor-pointer hover:bg-muted/50 ${
                                  selectedMembers.includes(user.id)
                                    ? "bg-muted"
                                    : ""
                                }`}
                                onClick={() => toggleMember(user.id)}
                              >
                                <div className="flex items-center gap-3">
                                  <Avatar className="h-8 w-8">
                                    <AvatarImage
                                      src={user.avatar}
                                      alt={user.name}
                                    />
                                    <AvatarFallback>
                                      {user.name
                                        .split(" ")
                                        .map((n) => n[0])
                                        .join("")}
                                    </AvatarFallback>
                                  </Avatar>
                                  <span>{user.name}</span>
                                </div>
                                {selectedMembers.includes(user.id) && (
                                  <Badge variant="outline" className="ml-auto">
                                    {t("flows.selected")}
                                  </Badge>
                                )}
                              </div>
                            ))}
                        </div>
                      </ScrollArea>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Visibility Alert */}
              <div className="col-span-8">
                {form.watch("visibility") === "public" && (
                  <Alert className="flex items-start gap-4">
                    <Globe className="h-5 w-5 mt-0.5" />
                    <div>
                      <AlertTitle>{t("flows.visibility.public")}</AlertTitle>
                      <AlertDescription>
                        {t("flows.visibility.publicDescription")}
                      </AlertDescription>
                    </div>
                  </Alert>
                )}
                {form.watch("visibility") === "private" && (
                  <Alert className="flex items-start gap-4">
                    <Lock className="h-5 w-5 mt-0.5" />
                    <div>
                      <AlertTitle>{t("flows.visibility.private")}</AlertTitle>
                      <AlertDescription>
                        {t("flows.visibility.privateDescription")}
                      </AlertDescription>
                    </div>
                  </Alert>
                )}
                {form.watch("visibility") === "shared" && (
                  <Alert className="flex items-start gap-4">
                    <Users className="h-5 w-5 mt-0.5" />
                    <div>
                      <AlertTitle>{t("flows.visibility.shared")}</AlertTitle>
                      <AlertDescription>
                        {t("flows.visibility.sharedDescription")}
                      </AlertDescription>
                    </div>
                  </Alert>
                )}
              </div>
            </form>
          </div>
          <DialogFooter className="px-6 py-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              {t("common.cancel")}
            </Button>
            <Button type="submit" form="create-flow-form">
              <Check className="h-4 w-4" />
              {t("common.create")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Form>
  );
}
