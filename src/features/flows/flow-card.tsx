"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { Flow, FlowMode } from "@mug/models/flow";
import {
  Card,
  CardContent,
  CardFooter,
  Card<PERSON>eader,
  CardTitle,
} from "@mug/components/ui/card";
import { Badge } from "@mug/components/ui/badge";
import { Button } from "@mug/components/ui/button";
import { EditFlowModal, UpdateFlowData } from "./components";
import { toast } from "sonner";
import {
  LayoutGrid,
  List,
  MoreHorizontal,
  Archive,
  Edit,
  Trash,
  ExternalLink,
  Tag,
} from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";

interface FlowCardProps {
  flow: Flow;
  onArchive?: (id: string) => void;
  onDelete?: (id: string) => void;
}
export function FlowCard(props: FlowCardProps) {
  const { flow, onArchive, onDelete } = props;
  const { t } = useTranslate();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handleUpdateFlow = (flowId: string, flowData: UpdateFlowData) => {
    toast.success(t("flows.flowUpdated"));
    setIsEditModalOpen(false);

    if (flowData.visibility === "private" && !flow.isArchived) {
      onArchive?.(flowId);
    } else if (flowData.visibility !== "private" && flow.isArchived) {
      onArchive?.(flowId);
    }
  };

  // Get icon for flow mode
  const getFlowModeIcon = (mode: string) => {
    const flowMode = mode.toLowerCase() as FlowMode;
    switch (flowMode) {
      case FlowMode.ATTRACT:
        return (
          <Badge variant="outline" className="bg-blue-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.CONVERT:
        return (
          <Badge variant="outline" className="bg-green-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.ENGAGE:
        return (
          <Badge variant="outline" className="bg-purple-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.CLOSE:
        return (
          <Badge variant="outline" className="bg-orange-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.DELIGHT:
        return (
          <Badge variant="outline" className="bg-pink-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.ANALYZE:
        return (
          <Badge variant="outline" className="bg-cyan-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      default:
        return <Badge variant="outline">{t(`flows.modes.${mode}`)}</Badge>;
    }
  };

  return (
    <div className="relative h-full">
      <Link href={`/flows/${flow.id}`} className="absolute inset-0 z-10">
        <span className="sr-only">
          {t("common.view")} {flow.name}
        </span>
      </Link>
      <Card className="h-full flex flex-col transition-colors">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <CardTitle className="text-lg">{flow.name}</CardTitle>
            <div className="z-20 relative">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">{t("common.actions")}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>{t("common.actions")}</DropdownMenuLabel>
                  <DropdownMenuItem asChild>
                    <Link href={`/flows/${flow.id}`}>
                      <ExternalLink className="mr-2 h-4 w-4" />
                      {t("common.view")}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsEditModalOpen(true);
                    }}
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    {t("common.edit")}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      onArchive?.(flow.id);
                    }}
                  >
                    <Archive className="mr-2 h-4 w-4" />
                    {flow.isArchived
                      ? t("flows.unarchive")
                      : t("flows.archive")}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete?.(flow.id);
                    }}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    {t("common.delete")}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <div className="flex items-center gap-2 mt-1">
            {flow.isArchived && (
              <Badge variant="secondary">{t("flows.archived")}</Badge>
            )}
            {getFlowModeIcon(flow.mode)}
            {flow.tags &&
              flow.tags.length > 0 &&
              flow.tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="flex items-center gap-1 text-xs"
                >
                  <Tag className="h-3 w-3" />
                  {tag}
                </Badge>
              ))}
          </div>
        </CardHeader>
        <CardContent className="flex-1">
          {flow.description && (
            <p className="text-sm text-muted-foreground mb-4">
              {flow.description}
            </p>
          )}
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <div className="text-muted-foreground">
                  {t("flows.totalItems")}
                </div>
                <div className="font-medium">{flow.stats?.totalItems || 0}</div>
              </div>
              <div>
                <div className="text-muted-foreground">
                  {t("flows.completedItems")}
                </div>
                <div className="font-medium">
                  {flow.stats?.completedItems || 0}
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">
                  {t("flows.conversionRate")}
                </div>
                <div className="font-medium">
                  {flow.stats?.conversionRate || 0}%
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">
                  {t("flows.avgCycleTime")}
                </div>
                <div className="font-medium">
                  {flow.stats?.avgCycleTime || 0} {t("common.days")}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-2 border-t flex justify-between items-center">
          <div className="text-xs text-muted-foreground">
            {t("common.updated")}: {format(new Date(flow.updatedAt), "PPp")}
          </div>
          <div className="flex items-center">
            {flow.viewMode === "kanban" ? (
              <LayoutGrid className="h-4 w-4 text-muted-foreground" />
            ) : (
              <List className="h-4 w-4 text-muted-foreground" />
            )}
          </div>
        </CardFooter>
      </Card>

      <EditFlowModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onUpdateFlow={handleUpdateFlow}
        onDeleteFlow={onDelete}
        onArchiveFlow={onArchive}
        flow={flow}
      />
    </div>
  );
}
