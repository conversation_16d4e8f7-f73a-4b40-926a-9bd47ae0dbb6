"use client";

// React imports
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

// Models and types
import { MockLane } from "@mug/constants/mock-lanes";
import { LanePosition, FlowMode } from "@mug/models/flow";
import { CreateLaneDTO } from "@mug/services/lane";
import { getAllConversionModels } from "@mug/constants/mock-conversions";
import { getAllActions } from "@mug/constants/mock-actions";
import { laneColorPalette } from "@mug/constants/color-palette";

// Re-export LanePosition for use in other components
export type { LanePosition };

// UI Components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@mug/components/ui/form";
import { Input } from "@mug/components/ui/input";
import { Button } from "@mug/components/ui/button";
import { Textarea } from "@mug/components/ui/textarea";
import { Checkbox } from "@mug/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@mug/components/ui/tooltip";
import { MultiSelect } from "@mug/components/ui/multi-select";
import { ColorPicker } from "@mug/components/ui/color-picker";

// Contexts and utilities
import { useTranslate } from "@mug/contexts/translate";

// Icons
import { Check, HelpCircle } from "lucide-react";

interface CreateLaneModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateLane: (laneData: Partial<MockLane>, position: LanePosition) => void;
  lanes: MockLane[];
  flowMode?: FlowMode; // Add flow mode to determine which fields to show
}

// Validation schema for the form
const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  wipLimit: z.coerce.number().int().min(0).optional(),
  position: z.string().min(1, "Position is required"),
  afterLaneId: z.string().optional(),
  allowCreate: z.boolean().default(true),
  backgroundColor: z.string().optional(),
  restriction: z.enum([
    "none",
    "only-left",
    "only-right",
  ], {
    required_error: "Movement restriction is required"
  }),
  model: z.enum([
    "default",
    "backlog",
    "new",
    "nurture",
    "potential",
    "opportunity",
    "done",
    "lost",
    "won",
    "pending",
    "attention",
  ], {
    required_error: "Lane model is required"
  }),
  // New fields for conversion models and actions
  conversionModelIds: z.array(z.string()).default([]),
  actionIds: z.array(z.string()).default([]),
});

// We could define a FormValues type from the schema for type checking
// but we'll use the inferred types from the schema directly

export function CreateLaneModal({
  isOpen,
  onClose,
  onCreateLane,
  lanes,
  flowMode = FlowMode.ATTRACT, // Default to ATTRACT if not provided
}: CreateLaneModalProps) {
  const { t } = useTranslate();
  const [showConversionOptions, setShowConversionOptions] = useState(flowMode === FlowMode.CONVERT);
  // Always show action options for now, might be conditional in the future
  const showActionOptions = true;

  // We'll use getAllConversionModels and getAllActions directly in the render

  // Initialize form with default values
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "", // Even though it's required, we start with empty string for UX
      wipLimit: undefined,
      position: "end",
      afterLaneId: "",
      allowCreate: true,
      backgroundColor: "transparent", // Default color (transparent)
      restriction: "none", // Default value for required field
      model: "default", // Default value for required field
      // New fields
      conversionModelIds: [],
      actionIds: [],
    },
  });

  // Reset form when modal is closed
  useEffect(() => {
    if (!isOpen) {
      form.reset();
    } else {
      // Update visibility of conversion options based on flow mode
      setShowConversionOptions(flowMode === FlowMode.CONVERT);
    }
  }, [isOpen, form, flowMode]);

  // Handle position selection from the combined dropdown
  const handlePositionChange = (value: string) => {
    // Check if the value is a combined "after:laneId" format
    if (value.startsWith("after:")) {
      const laneId = value.replace("after:", "");
      form.setValue("position", "after");
      form.setValue("afterLaneId", laneId);
    } else {
      // For "start" or "end" positions
      form.setValue("position", value);
      form.setValue("afterLaneId", "");
    }
  };

  // Function to handle form submission
  const handleSubmit = form.handleSubmit((values) => {
    // Manual validation for afterLaneId when position is "after"
    if (values.position === "after" && !values.afterLaneId) {
      form.setError("afterLaneId", {
        type: "manual",
        message: "Please select a column",
      });
      return;
    }

    // Determine the position of the new lane
    let position: LanePosition;
    if (values.position === "start") {
      position = "start";
    } else if (values.position === "end") {
      position = "end";
    } else if (values.position === "after" && values.afterLaneId) {
      position = { afterLaneId: values.afterLaneId };
    } else {
      // Fallback to the end if something goes wrong
      position = "end";
    }

    // Create lane data using the DTO structure
    const laneDTO: Partial<CreateLaneDTO> = {
      title: values.title,
      description: values.description || "",
      wipLimit: values.wipLimit,
      allowCreate: values.allowCreate,
      restriction: values.restriction,
      model: values.model,
      backgroundColor: values.backgroundColor,
      position: position,
      // Include new fields conditionally
      ...(values.conversionModelIds.length > 0 && { conversionModelIds: values.conversionModelIds }),
      ...(values.actionIds.length > 0 && { actionIds: values.actionIds }),
    };

    // Convert DTO to MockLane for current implementation
    const newLane: Partial<MockLane> = {
      title: laneDTO.title,
      description: laneDTO.description || "",
      wipLimit: laneDTO.wipLimit,
      items: [],
      allowCreate: laneDTO.allowCreate,
      restriction: laneDTO.restriction,
      model: laneDTO.model,
      backgroundColor: laneDTO.backgroundColor,
      // Include new fields in the MockLane object
      ...(laneDTO.conversionModelIds && { conversionModelIds: laneDTO.conversionModelIds }),
      ...(laneDTO.actionIds && { actionIds: laneDTO.actionIds }),
    };

    // Pass the lane data and position to the parent component
    onCreateLane(newLane, position);
    form.reset();
    onClose();
  });

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] gap-0 flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>{t("common.newColumn")}</DialogTitle>
          <DialogDescription>
            {t("common.createColumnDescription")}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 flex flex-col overflow-auto py-4">
        <Form {...form}>
            <form
              id="create-lane-form"
              onSubmit={handleSubmit}
              className="grid grid-cols-8 gap-4 px-6 flex-1"
            >
                {/* Title */}
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem className="col-span-6">
                      <div className="flex items-center h-6">
                        <FormLabel className="gap-0">
                          {t("common.title")}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                      </div>
                      <FormControl>
                        <Input
                          placeholder={t("common.columnTitlePlaceholder")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

              {/* Background Color */}
              <FormField
                control={form.control}
                name="backgroundColor"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("common.backgroundColor")}
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="max-w-[300px]">
                            <p>{t("common.backgroundColorDescription") || "Choose a background color for the lane header"}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <FormControl>
                      <ColorPicker
                        options={laneColorPalette}
                        value={field.value || "transparent"}
                        onChange={field.onChange}
                        placeholder={t("common.selectColor") || "Select color..."}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="col-span-8">
                      <div className="flex items-center h-6">
                        <FormLabel className="gap-0">
                          {t("common.description")}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                      </div>
                      <FormControl>
                        <Textarea
                          placeholder={t("common.columnDescriptionPlaceholder")}
                          className="resize-none min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Position with integrated lane selection */}
                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => {
                    // Determine the current value for the combined dropdown
                    let combinedValue = field.value;
                    if (field.value === "after" && form.getValues("afterLaneId")) {
                      combinedValue = `after:${form.getValues("afterLaneId")}`;
                    }

                    return (
                      <FormItem className="col-span-2">
                        <div className="flex items-center h-6">
                          <FormLabel className="gap-0">
                            {t("common.position")}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                        </div>
                        <Select
                          onValueChange={handlePositionChange}
                          value={combinedValue}
                        >
                          <FormControl className="w-full">
                            <SelectTrigger>
                              <SelectValue
                                placeholder={t("common.selectPosition")}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {/* Default positions section */}
                            <SelectItem value="start">
                              {t("common.positionStart")}
                            </SelectItem>
                            <SelectItem value="end">
                              {t("common.positionEnd")}
                            </SelectItem>

                            {/* After specific lane section */}
                            {lanes.length > 0 && (
                              <>
                                <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">
                                  {t("common.positionAfterSpecific")}
                                </div>
                                {lanes.map((lane) => (
                                  <SelectItem key={lane.id} value={`after:${lane.id}`}>
                                    {lane.title}
                                  </SelectItem>
                                ))}
                              </>
                            )}
                          </SelectContent>
                        </Select>

                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                {/* Hidden field for afterLaneId - not visible but still part of the form */}
                <FormField
                  control={form.control}
                  name="afterLaneId"
                  render={({ field }) => (
                    <input type="hidden" {...field} />
                  )}
                />

              {/* Work in Progress (WIP) Limit */}
              <FormField
                control={form.control}
                name="wipLimit"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("common.wipLimit")}
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="max-w-[300px]">
                            <p>{t("common.wipLimitDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder={t("common.wipLimitPlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Lane model */}
              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("common.laneModel")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="max-w-[300px]">
                            <p>{t("common.laneModelDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl className="w-full">
                        <SelectTrigger>
                          <SelectValue placeholder={t("common.selectModel")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="default">
                          {t("common.modelDefault")}
                        </SelectItem>
                        <SelectItem value="backlog">
                          {t("common.modelBacklog")}
                        </SelectItem>
                        <SelectItem value="new">
                          {t("common.modelNew")}
                        </SelectItem>
                        <SelectItem value="nurture">
                          {t("common.modelNurture")}
                        </SelectItem>
                        <SelectItem value="potential">
                          {t("common.modelPotential")}
                        </SelectItem>
                        <SelectItem value="opportunity">
                          {t("common.modelOpportunity")}
                        </SelectItem>
                        <SelectItem value="done">
                          {t("common.modelDone")}
                        </SelectItem>
                        <SelectItem value="lost">
                          {t("common.modelLost")}
                        </SelectItem>
                        <SelectItem value="won">
                          {t("common.modelWon")}
                        </SelectItem>
                        <SelectItem value="pending">
                          {t("common.modelPending")}
                        </SelectItem>
                        <SelectItem value="attention">
                          {t("common.modelAttention")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />


              {/* Movement restriction */}
              <FormField
                control={form.control}
                name="restriction"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("common.movementRestriction")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="max-w-[300px]">
                            <p>{t("common.movementRestrictionDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl className="w-full">
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t("common.selectRestriction")}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">
                          {t("common.restrictionNone")}
                        </SelectItem>
                        <SelectItem value="only-left">
                          {t("common.restrictionOnlyLeft")}
                        </SelectItem>
                        <SelectItem value="only-right">
                          {t("common.restrictionOnlyRight")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Conversion Models - Only visible for CONVERT flow mode */}
              {showConversionOptions && (
                <FormField
                  control={form.control}
                  name="conversionModelIds"
                  render={({ field }) => (
                    <FormItem className="col-span-4">
                      <div className="flex items-center h-6">
                        <FormLabel className="gap-0">
                          {t("common.conversionModels")}
                        </FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                            </TooltipTrigger>
                            <TooltipContent className="max-w-[300px]">
                              <p>{t("common.conversionModelsDescription")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <FormControl>
                        <MultiSelect
                          options={getAllConversionModels().map((model) => ({
                            value: model.id,
                            label: model.name,
                            description: model.description,
                            group: model.type
                          }))}
                          selected={field.value || []}
                          onChange={field.onChange}
                          placeholder={t("common.selectConversionModels")}
                          emptyText={t("common.noConversionModelsFound")}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Associated Actions */}
              {showActionOptions && (
                <FormField
                  control={form.control}
                  name="actionIds"
                  render={({ field }) => (
                    <FormItem className="col-span-4">
                      <div className="flex items-center h-6">
                        <FormLabel className="gap-0">
                          {t("common.associatedActions")}
                        </FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                            </TooltipTrigger>
                            <TooltipContent className="max-w-[300px]">
                              <p>{t("common.associatedActionsDescription")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <FormControl>
                        <MultiSelect
                          options={getAllActions().map((action) => ({
                            value: action.id,
                            label: action.name,
                            description: action.description,
                            group: action.type
                          }))}
                          selected={field.value || []}
                          onChange={field.onChange}
                          placeholder={t("common.selectActions")}
                          emptyText={t("common.noActionsFound")}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Full width section for the checkbox */}
              <FormField
                control={form.control}
                name="allowCreate"
                render={({ field }) => (
                  <FormItem className="flex col-span-8 flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="gap-0">
                        {t("common.allowItemCreation")}
                      </FormLabel>
                      <FormDescription>
                        {t("common.allowItemCreationDescription")}
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>

        <DialogFooter className="px-6 py-4 border-t">
          <Button type="button" variant="outline" onClick={onClose}>
            {t("common.cancel")}
          </Button>
          <Button type="submit" form="create-lane-form">
            <Check className="h-4 w-4" />
            {t("common.create")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
