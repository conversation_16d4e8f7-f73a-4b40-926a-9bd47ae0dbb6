"use client";

// React and hooks
import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";

// Third-party libraries
import { format } from "date-fns";
import { enUS } from "date-fns/locale";

// UI Components
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";
import { Button } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { Progress } from "@mug/components/ui/progress";
import { Slider } from "@mug/components/ui/slider";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import { Popover, PopoverContent, PopoverTrigger } from "@mug/components/ui/popover";
import { Calendar as CalendarComponent } from "@mug/components/ui/calendar";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@mug/components/ui/tabs";

// Icons
import {
  CalendarClock,
  Clock,
  BarChart,
  MessageSquare,
  ArrowRight,
  Inbox,
  Paperclip,
  Calendar,
  X,
  Plus,
} from "lucide-react";

// Utils and types
import { MockItem, MockLane, Priority } from "@mug/constants/mock-lanes";
import { toast } from "sonner";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";

/**
 * Props for the ItemDetailsModal component
 */
interface ItemDetailsModalProps {
  /** The item to display details for (null when no item is selected) */
  item: MockItem | null;
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** The current lane containing the item (if any) */
  currentLane?: MockLane | null;
  /** All available lanes in the flow */
  allLanes: MockLane[];
  /** Function to move an item to a different lane */
  onMoveLane?: (itemId: string, targetLaneId: string) => void;
  /** Function to update an item */
  onUpdateItem?: (itemId: string, updates: Partial<MockItem>) => void;
}

/**
 * Modal component to display item details and allow moving items between lanes
 */
export function ItemDetailsModal({
  item,
  isOpen,
  onClose,
  currentLane,
  allLanes,
  onMoveLane,
  onUpdateItem,
}: ItemDetailsModalProps) {
  const { t } = useTranslate();

  /**
   * State to track the selected target lane for moving the item
   */
  const [targetLaneId, setTargetLaneId] = useState<string>(
    currentLane?.id || ""
  );

  /**
   * State to track the active tab in the right column
   */
  const [activeTab, setActiveTab] = useState<string>("activities");

  /**
   * State to track the tag input value
   */
  const [tagInput, setTagInput] = useState<string>("");

  // Return null if no item is provided
  if (!item) return null;

  /**
   * Handle moving an item to a different lane
   */
  const handleMoveLane = () => {
    if (onMoveLane && targetLaneId && targetLaneId !== currentLane?.id) {
      onMoveLane(item.id, targetLaneId);
      onClose();
    }
  };

  /**
   * Get the CSS class for a priority badge based on priority level
   */
  const getPriorityBadgeClass = (priority?: Priority) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "low":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  /**
   * Handle updating the progress
   */
  const handleProgressUpdate = (value: number) => {
    if (!item || !onUpdateItem) return;

    const originalValue = item.progress;

    try {
      // Update the item
      onUpdateItem(item.id, { progress: value });

      // Show success toast
      toast.success(t("common.itemUpdated"));
    } catch {
      // Show error toast
      toast.error(t("common.updateFailed"));

      // Revert to original value
      if (originalValue !== undefined) {
        onUpdateItem(item.id, { progress: originalValue });
      }
    }
  };

  /**
   * Handle updating the priority
   */
  const handlePriorityUpdate = (value: Priority) => {
    if (!item || !onUpdateItem) return;

    const originalValue = item.priority;

    try {
      // Update the item
      onUpdateItem(item.id, { priority: value });

      // Show success toast
      toast.success(t("common.itemUpdated"));
    } catch {
      // Show error toast
      toast.error(t("common.updateFailed"));

      // Revert to original value
      if (originalValue) {
        onUpdateItem(item.id, { priority: originalValue });
      }
    }
  };

  /**
   * Handle updating the assignee
   */
  const handleAssigneeUpdate = (assigneeId: string) => {
    if (!item || !onUpdateItem) return;

    const originalAssignee = item.assignee;

    try {
      // Find the assignee
      const assignee = assigneeId === "unassigned"
        ? undefined
        : allLanes.flatMap(lane => lane.items)
            .flatMap(item => item.assignee)
            .find(a => a?.id === assigneeId);

      // Update the item
      onUpdateItem(item.id, { assignee });

      // Show success toast
      toast.success(t("common.itemUpdated"));
    } catch {
      // Show error toast
      toast.error(t("common.updateFailed"));

      // Revert to original value
      onUpdateItem(item.id, { assignee: originalAssignee });
    }
  };

  /**
   * Handle updating the person
   */
  const handlePersonUpdate = (personId: string) => {
    if (!item || !onUpdateItem) return;

    const originalAssignee = item.assignee; // In a real app, this would be a different field

    try {
      // Find the person (in a real app, this would be different from assignee)
      const person = personId === "unassigned"
        ? undefined
        : allLanes.flatMap(lane => lane.items)
            .flatMap(item => item.assignee)
            .find(a => a?.id === personId);

      // Update the item (in a real app, this would update a different field)
      // For this example, we're using the same assignee field
      onUpdateItem(item.id, { assignee: person });

      // Show success toast
      toast.success(t("common.itemUpdated"));
    } catch {
      // Show error toast
      toast.error(t("common.updateFailed"));

      // Revert to original value
      onUpdateItem(item.id, { assignee: originalAssignee });
    }
  };

  /**
   * Handle updating the due date
   */
  const handleDueDateUpdate = (date: Date | undefined) => {
    if (!item || !onUpdateItem) return;

    const originalValue = item.dueDate;

    try {
      // Update the item
      onUpdateItem(item.id, {
        dueDate: date ? date.toISOString() : undefined
      });

      // Show success toast
      toast.success(t("common.itemUpdated"));
    } catch {
      // Show error toast
      toast.error(t("common.updateFailed"));

      // Revert to original value
      onUpdateItem(item.id, { dueDate: originalValue });
    }
  };

  /**
   * Add a new tag to the item
   */
  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (!trimmedTag) return;

    // Validate tag
    if (trimmedTag.length < 2) {
      // Too short
      return;
    }
    if (trimmedTag.length > 20) {
      // Too long
      return;
    }
    if (!/^[a-zA-Z0-9\-_]+$/.test(trimmedTag)) {
      // Invalid characters
      return;
    }

    const currentTags = item.tags || [];
    if (currentTags.includes(trimmedTag)) {
      // Tag already exists
      return;
    }

    handleTagsUpdate([...currentTags, trimmedTag]);
    setTagInput("");
  };

  /**
   * Remove a tag from the item
   */
  const removeTag = (tag: string) => {
    const currentTags = item.tags || [];
    handleTagsUpdate(currentTags.filter((t) => t !== tag));
  };

  /**
   * Handle updating tags
   */
  const handleTagsUpdate = (tags: string[]) => {
    if (!item || !onUpdateItem) return;

    const originalTags = item.tags;

    try {
      // Update the item
      onUpdateItem(item.id, { tags });

      // Show success toast
      toast.success(t("common.itemUpdated"));
    } catch {
      // Show error toast
      toast.error(t("common.updateFailed"));

      // Revert to original value
      if (originalTags) {
        onUpdateItem(item.id, { tags: originalTags });
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[90vw] w-full h-full max-h-[90vh] gap-0 flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle className="text-xl">{item.title}</DialogTitle>
          <DialogDescription className="flex items-center gap-2 text-sm">
            {currentLane && (
              <>
                <span>{currentLane.title}</span>
                {item.createdAt && (
                  <>
                    <span>•</span>
                    <span>
                      {t("common.createdOn")}{" "}
                      {format(new Date(item.createdAt), "PPP", {
                        locale: enUS,
                      })}
                    </span>
                  </>
                )}
                {item.updatedAt && (
                  <>
                    <span>•</span>
                    <span>
                      {t("common.lastUpdated")}:{" "}
                      {format(new Date(item.updatedAt), "PPp", {
                        locale: enUS,
                      })}
                    </span>
                  </>
                )}
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 grid grid-cols-5 gap-4">
          {/* Item metadata */}
          <div className="grid px-6 py-4 col-span-2 grid-cols-2 gap-4">
            {/* Priority */}
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                {t("common.priority")}
              </div>
              <div>
                <Select
                  defaultValue={item.priority || ""}
                  onValueChange={(value) => {
                    if (value) {
                      handlePriorityUpdate(value as Priority);
                    }
                  }}
                >
                  <SelectTrigger className="h-9 w-full bg-muted">
                    <SelectValue placeholder={t("common.selectPriority")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className={getPriorityBadgeClass("high")}
                        >
                          {t("common.high")}
                        </Badge>
                      </div>
                    </SelectItem>
                    <SelectItem value="medium">
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className={getPriorityBadgeClass("medium")}
                        >
                          {t("common.medium")}
                        </Badge>
                      </div>
                    </SelectItem>
                    <SelectItem value="low">
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className={getPriorityBadgeClass("low")}
                        >
                          {t("common.low")}
                        </Badge>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Assignee */}
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                {t("common.assignee")}
              </div>
              <div>
                <Select
                  defaultValue={item.assignee?.id || "unassigned"}
                  onValueChange={(value) => {
                    handleAssigneeUpdate(value);
                  }}
                >
                  <SelectTrigger className="h-9 w-full bg-muted">
                    <SelectValue>
                      {item.assignee ? (
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage
                              src={item.assignee.avatar}
                              alt={item.assignee.name}
                            />
                            <AvatarFallback>
                              {item.assignee.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <span>{item.assignee.name}</span>
                        </div>
                      ) : (
                        <span>{t("common.unassigned")}</span>
                      )}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">
                      <span className="text-muted-foreground">
                        {t("common.unassigned")}
                      </span>
                    </SelectItem>
                    {allLanes
                      .flatMap((lane) => lane.items)
                      .flatMap((item) => item.assignee)
                      .filter((assignee, index, self) =>
                        assignee && self.findIndex(a => a?.id === assignee?.id) === index
                      )
                      .map((assignee) =>
                        assignee && (
                          <SelectItem key={assignee.id} value={assignee.id}>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage
                                  src={assignee.avatar}
                                  alt={assignee.name}
                                />
                                <AvatarFallback>
                                  {assignee.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <span>{assignee.name}</span>
                            </div>
                          </SelectItem>
                        )
                      )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Person */}
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                {t("common.person")}
              </div>
              <div>
                <Select
                  defaultValue={item.assignee?.id || "unassigned"}
                  /* In a real app, this would be a different field */
                  onValueChange={(value) => {
                    handlePersonUpdate(value);
                  }}
                >
                  <SelectTrigger className="h-9 w-full bg-muted">
                    <SelectValue>
                      {item.assignee ? (
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage
                              src={item.assignee.avatar}
                              alt={item.assignee.name}
                            />
                            <AvatarFallback>
                              {item.assignee.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <span>{item.assignee.name}</span>
                        </div>
                      ) : (
                        <span>{t("common.unassigned")}</span>
                      )}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">
                      <span className="text-muted-foreground">
                        {t("common.unassigned")}
                      </span>
                    </SelectItem>
                    {allLanes
                      .flatMap((lane) => lane.items)
                      .flatMap((item) => item.assignee)
                      .filter((assignee, index, self) =>
                        assignee && self.findIndex(a => a?.id === assignee?.id) === index
                      )
                      .map((assignee) =>
                        assignee && (
                          <SelectItem key={assignee.id} value={assignee.id}>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage
                                  src={assignee.avatar}
                                  alt={assignee.name}
                                />
                                <AvatarFallback>
                                  {assignee.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <span>{assignee.name}</span>
                            </div>
                          </SelectItem>
                        )
                      )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Due Date */}
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                {t("common.dueDate")}
              </div>
              <div>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={classNameBuilder(
                        "h-9 w-full justify-start text-left font-normal bg-muted",
                        !item.dueDate && "text-muted-foreground"
                      )}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {item.dueDate ? (
                        format(new Date(item.dueDate), "PPP", { locale: enUS })
                      ) : (
                        <span>{t("common.selectDate")}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={item.dueDate ? new Date(item.dueDate) : undefined}
                      onSelect={(date) => handleDueDateUpdate(date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Progress */}
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                {t("common.progress")}
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <BarChart className="h-4 w-4 text-muted-foreground" />
                  <Progress
                    value={item.progress}
                    className="h-2 flex-1"
                    aria-label={`${item.progress || 0}% complete`}
                  />
                  <span className="text-sm">{item.progress || 0}%</span>
                </div>
                <Slider
                  defaultValue={[item.progress || 0]}
                  max={100}
                  step={1}
                  className="w-full"
                  onValueCommit={(value) => handleProgressUpdate(value[0])}
                />
              </div>
            </div>

            {/* Description */}
            {item.description && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-muted-foreground">
                  {t("common.description")}
                </h4>
                <div className="text-sm whitespace-pre-wrap">
                  {item.description}
                </div>
              </div>
            )}

            {/* Tags */}
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">
                {t("common.tags")}
              </div>
              <div className="space-y-4">
                {/* Selected tags */}
                <div className="flex flex-wrap gap-2 mb-2">
                  {(item.tags || []).map((tag) => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="px-3 py-1"
                    >
                      {tag}
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 -mr-1"
                        onClick={() => removeTag(tag)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>

                {/* Tag input with autocomplete */}
                <div className="flex gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer">
                        {tagInput || t("common.addTag")}
                      </div>
                    </PopoverTrigger>
                    <PopoverContent
                      className="p-0 w-[300px]"
                      align="start"
                      side="bottom"
                      sideOffset={5}
                    >
                      <div className="border-b px-3 py-2 text-xs text-muted-foreground">
                        {t("common.tagsHelpText") || "Type to search or create a new tag"}
                      </div>
                      <Command>
                        <CommandInput
                          placeholder={t("common.searchTags") || "Search tags..."}
                          value={tagInput}
                          onValueChange={setTagInput}
                        />
                        <CommandList>
                          <CommandEmpty>
                            {tagInput.trim() !== "" ? (
                              <div className="py-3 px-4 flex flex-col items-center">
                                <p className="text-center mb-2">{t("common.noTagsFound") || "No tags found"}</p>
                                <Button
                                  variant="default"
                                  size="sm"
                                  className="w-full"
                                  onClick={() => {
                                    addTag(tagInput);
                                    document.querySelector('[data-state="open"]')?.dispatchEvent(
                                      new KeyboardEvent('keydown', { key: 'Escape' })
                                    );
                                  }}
                                >
                                  <Plus className="mr-2 h-4 w-4" />
                                  {t("common.createTag") || "Create tag"} &quot;{tagInput}&quot;
                                </Button>
                              </div>
                            ) : (
                              <p className="py-2 px-4">{t("common.typeToSearch") || "Type to search..."}</p>
                            )}
                          </CommandEmpty>

                          {/* Create new tag option always visible */}
                          {tagInput.trim() !== "" && (
                            <CommandGroup heading={t("common.createNew") || "Create new"}>
                              <CommandItem
                                onSelect={() => {
                                  addTag(tagInput);
                                  document.querySelector('[data-state="open"]')?.dispatchEvent(
                                    new KeyboardEvent('keydown', { key: 'Escape' })
                                  );
                                }}
                              >
                                <Plus className="mr-2 h-4 w-4" />
                                <span className="font-medium">{t("common.createTag") || "Create tag"} &quot;{tagInput}&quot;</span>
                              </CommandItem>
                            </CommandGroup>
                          )}

                          <CommandGroup heading={t("common.suggestedTags") || "Suggested tags"}>
                            {/* Get unique tags from all items in all lanes */}
                            {allLanes
                              .flatMap(lane => lane.items)
                              .flatMap(item => item.tags || [])
                              .filter((tag, index, self) =>
                                tag && self.indexOf(tag) === index &&
                                !(item.tags || []).includes(tag) &&
                                tag.toLowerCase().includes(tagInput.toLowerCase())
                              )
                              .map((tag) => (
                                <CommandItem
                                  key={tag}
                                  value={tag}
                                  onSelect={(value) => {
                                    addTag(value);
                                    document.querySelector('[data-state="open"]')?.dispatchEvent(
                                      new KeyboardEvent('keydown', { key: 'Escape' })
                                    );
                                  }}
                                >
                                  <span>{tag}</span>
                                </CommandItem>
                              ))
                            }
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>

            {/* Comments (placeholder for future implementation) */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                {t("common.comments")}
              </h4>
              <div className="text-sm text-muted-foreground italic">
                {t("common.noComments")}
              </div>
            </div>

            {/* Activity history (placeholder for future implementation) */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {t("common.activity")}
              </h4>
              <div className="text-sm">
                <div className="flex items-start gap-2">
                  <CalendarClock className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <span className="font-medium">
                      {item.createdAt
                        ? format(new Date(item.createdAt), "PPP", {
                            locale: enUS,
                          })
                        : t("common.unknown")}
                    </span>
                    <span className="text-muted-foreground">
                      {" "}
                      • {t("common.itemCreated")}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="border-l pl-6 pt-4 col-span-3 h-full">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="mr-6">
              <TabsList className="grid grid-cols-5 mb-2 w-full">
                <TabsTrigger value="activities" className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {t("common.activities")}
                </TabsTrigger>
                <TabsTrigger value="comments" className="flex items-center gap-1">
                  <MessageSquare className="h-4 w-4" />
                  {t("common.comments")}
                </TabsTrigger>
                <TabsTrigger value="inbox" className="flex items-center gap-1 relative">
                  <Inbox className="h-4 w-4" />
                  {t("common.inbox")}
                  <Badge className="absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center p-0 text-[10px]">
                    3
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="documents" className="flex items-center gap-1">
                  <Paperclip className="h-4 w-4" />
                  {t("common.documents")}
                </TabsTrigger>
                <TabsTrigger value="history" className="flex items-center gap-1">
                  <CalendarClock className="h-4 w-4" />
                  {t("common.history")}
                </TabsTrigger>
              </TabsList>
              </div>

              {/* Comments Tab */}
              <TabsContent value="comments" className="flex flex-col h-[400px]">
                {/* Comments List - Scrollable Area */}
                <div className="flex-1 overflow-y-auto pr-6 space-y-4">
                  <div className="space-y-4">
                    {/* Comments List */}
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="/avatars/02.png" alt="User" />
                          <AvatarFallback>JS</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-sm">Jane Smith</span>
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(Date.now() - 86400000), "PPp", { locale: enUS })}
                            </span>
                          </div>
                          <div className="text-sm bg-muted p-3 rounded-md">
                            {t("comments.sampleComment1")}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="/avatars/03.png" alt="User" />
                          <AvatarFallback>RJ</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-sm">Robert Johnson</span>
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(Date.now() - 172800000), "PPp", { locale: enUS })}
                            </span>
                          </div>
                          <div className="text-sm bg-muted p-3 rounded-md">
                            {t("comments.sampleComment2")}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Comment Input - Fixed at Bottom */}
                <div className="mt-4 border-t pt-4 bg-background">
                  <div className="flex items-start gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/avatars/01.png" alt="User" />
                      <AvatarFallback>JD</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-2">
                      <div className="border rounded-md overflow-hidden">
                        <textarea
                          className="w-full p-2 text-sm resize-none min-h-[60px] focus:outline-none"
                          placeholder={t("common.addComment")}
                        />
                      </div>
                      <div className="flex justify-end">
                        <Button size="sm">
                          {t("common.send")}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Documents Tab */}
              <TabsContent value="documents" className="flex flex-col h-[400px]">
                {/* Documents List - Scrollable Area */}
                <div className="flex-1 overflow-y-auto pr-6 space-y-4">
                  <div className="space-y-4">
                    {/* Documents List */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 border rounded-md bg-card/50">
                        <div className="flex items-center gap-3">
                          <div className="bg-muted h-10 w-10 rounded-md flex items-center justify-center text-muted-foreground">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="15"
                              height="15"
                              viewBox="0 0 15 15"
                              fill="none"
                              className="h-5 w-5"
                            >
                              <path
                                d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V6H8.5C8.22386 6 8 5.77614 8 5.5V2H3.5ZM9 2.70711L11.2929 5H9V2.70711ZM2 2.5C2 1.67157 2.67157 1 3.5 1H8.5C8.63261 1 8.75979 1.05268 8.85355 1.14645L12.8536 5.14645C12.9473 5.24021 13 5.36739 13 5.5V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5Z"
                                fill="currentColor"
                                fillRule="evenodd"
                                clipRule="evenodd"
                              ></path>
                            </svg>
                          </div>
                          <div>
                            <div className="font-medium text-sm">document.pdf</div>
                            <div className="text-xs text-muted-foreground">
                              {format(new Date(Date.now() - 86400000), "PPp", { locale: enUS })} • 2.4 MB
                            </div>
                          </div>
                        </div>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="15"
                            height="15"
                            viewBox="0 0 15 15"
                            fill="none"
                            className="h-4 w-4"
                          >
                            <path
                              d="M7.5 1C7.22386 1 7 1.22386 7 1.5C7 1.77614 7.22386 2 7.5 2C7.77614 2 8 1.77614 8 1.5C8 1.22386 7.77614 1 7.5 1ZM7.5 7C7.22386 7 7 7.22386 7 7.5C7 7.77614 7.22386 8 7.5 8C7.77614 8 8 7.77614 8 7.5C8 7.22386 7.77614 7 7.5 7ZM7 13.5C7 13.2239 7.22386 13 7.5 13C7.77614 13 8 13.2239 8 13.5C8 13.7761 7.77614 14 7.5 14C7.22386 14 7 13.7761 7 13.5Z"
                              fill="currentColor"
                              fillRule="evenodd"
                              clipRule="evenodd"
                            ></path>
                          </svg>
                        </Button>
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-md bg-card/50">
                        <div className="flex items-center gap-3">
                          <div className="bg-muted h-10 w-10 rounded-md flex items-center justify-center text-muted-foreground">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="15"
                              height="15"
                              viewBox="0 0 15 15"
                              fill="none"
                              className="h-5 w-5"
                            >
                              <path
                                d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V6H8.5C8.22386 6 8 5.77614 8 5.5V2H3.5ZM9 2.70711L11.2929 5H9V2.70711ZM2 2.5C2 1.67157 2.67157 1 3.5 1H8.5C8.63261 1 8.75979 1.05268 8.85355 1.14645L12.8536 5.14645C12.9473 5.24021 13 5.36739 13 5.5V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5Z"
                                fill="currentColor"
                                fillRule="evenodd"
                                clipRule="evenodd"
                              ></path>
                            </svg>
                          </div>
                          <div>
                            <div className="font-medium text-sm">presentation.pptx</div>
                            <div className="text-xs text-muted-foreground">
                              {format(new Date(Date.now() - 172800000), "PPp", { locale: enUS })} • 4.8 MB
                            </div>
                          </div>
                        </div>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="15"
                            height="15"
                            viewBox="0 0 15 15"
                            fill="none"
                            className="h-4 w-4"
                          >
                            <path
                              d="M7.5 1C7.22386 1 7 1.22386 7 1.5C7 1.77614 7.22386 2 7.5 2C7.77614 2 8 1.77614 8 1.5C8 1.22386 7.77614 1 7.5 1ZM7.5 7C7.22386 7 7 7.22386 7 7.5C7 7.77614 7.22386 8 7.5 8C7.77614 8 8 7.77614 8 7.5C8 7.22386 7.77614 7 7.5 7ZM7 13.5C7 13.2239 7.22386 13 7.5 13C7.77614 13 8 13.2239 8 13.5C8 13.7761 7.77614 14 7.5 14C7.22386 14 7 13.7761 7 13.5Z"
                              fill="currentColor"
                              fillRule="evenodd"
                              clipRule="evenodd"
                            ></path>
                          </svg>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Document Upload - Fixed at Bottom */}
                <div className="mt-4 border-t pt-4 bg-background">
                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <div className="border border-dashed rounded-md p-4 text-center cursor-pointer hover:bg-muted/50 transition-colors">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="15"
                          height="15"
                          viewBox="0 0 15 15"
                          fill="none"
                          className="h-5 w-5 mx-auto mb-2 text-muted-foreground"
                        >
                          <path
                            d="M7.81825 1.18188C7.64251 1.00615 7.35759 1.00615 7.18185 1.18188L4.18185 4.18188C4.00611 4.35762 4.00611 4.64254 4.18185 4.81828C4.35759 4.99401 4.64251 4.99401 4.81825 4.81828L7.05005 2.58648V9.49996C7.05005 9.74849 7.25152 9.94996 7.50005 9.94996C7.74858 9.94996 7.95005 9.74849 7.95005 9.49996V2.58648L10.1819 4.81828C10.3576 4.99401 10.6425 4.99401 10.8182 4.81828C10.994 4.64254 10.994 4.35762 10.8182 4.18188L7.81825 1.18188ZM2.5 9.99997C2.77614 9.99997 3 10.2238 3 10.5V12C3 12.5523 3.44772 13 4 13H11C11.5523 13 12 12.5523 12 12V10.5C12 10.2238 12.2239 9.99997 12.5 9.99997C12.7761 9.99997 13 10.2238 13 10.5V12C13 13.1046 12.1046 14 11 14H4C2.89543 14 2 13.1046 2 12V10.5C2 10.2238 2.22386 9.99997 2.5 9.99997Z"
                            fill="currentColor"
                            fillRule="evenodd"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                        <p className="text-sm text-muted-foreground">{t("documents.dragAndDrop")}</p>
                        <p className="text-xs text-muted-foreground">{t("documents.orClickToUpload")}</p>
                        <input type="file" className="hidden" />
                      </div>
                    </div>
                    <Button size="sm" className="self-end">
                      {t("common.upload")}
                    </Button>
                  </div>
                </div>
              </TabsContent>

              {/* Activities Tab */}
              <TabsContent value="activities" className="space-y-4 overflow-y-auto max-h-[400px] pr-6">
                <div className="space-y-4">
                  {/* Activity List */}
                  <div className="space-y-3">
                    <div className="flex items-start gap-3 p-3 rounded-md border bg-card/50">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="/avatars/01.png" alt="User" />
                        <AvatarFallback>JD</AvatarFallback>
                      </Avatar>
                      <div className="space-y-1">
                        <p className="text-sm">
                          <span className="font-medium">John Doe</span> {t("activities.addedComment")}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(Date.now() - 3600000), "PPp", { locale: enUS })}
                        </p>
                        <div className="text-sm bg-muted p-2 rounded-md mt-2">
                          {t("activities.sampleComment")}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 rounded-md border bg-card/50">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="/avatars/02.png" alt="User" />
                        <AvatarFallback>JS</AvatarFallback>
                      </Avatar>
                      <div className="space-y-1">
                        <p className="text-sm">
                          <span className="font-medium">Jane Smith</span> {t("activities.changedStatus")}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(Date.now() - 86400000), "PPp", { locale: enUS })}
                        </p>
                        <div className="flex items-center gap-2 text-sm mt-1">
                          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                            {t("common.inProgress")}
                          </Badge>
                          <ArrowRight className="h-3 w-3" />
                          <Badge variant="outline" className="bg-green-100 text-green-800">
                            {t("common.completed")}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 p-3 rounded-md border bg-card/50">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="/avatars/03.png" alt="User" />
                        <AvatarFallback>RJ</AvatarFallback>
                      </Avatar>
                      <div className="space-y-1">
                        <p className="text-sm">
                          <span className="font-medium">Robert Johnson</span> {t("activities.attachedFile")}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(Date.now() - 172800000), "PPp", { locale: enUS })}
                        </p>
                        <div className="flex items-center gap-2 text-sm mt-1 p-2 bg-muted rounded-md">
                          <Button variant="ghost" size="sm" className="h-8 gap-2">
                            <MessageSquare className="h-4 w-4" />
                            document.pdf
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* History Tab */}
              <TabsContent value="history" className="space-y-4 overflow-y-auto max-h-[400px] pr-6">
                <div className="space-y-4">
                  {/* History Timeline */}
                  <div className="space-y-4 relative before:absolute before:inset-y-0 before:left-3.5 before:w-px before:bg-muted-foreground/20">
                    <div className="relative pl-8">
                      <div className="absolute left-0 top-1 h-7 w-7 rounded-full bg-muted flex items-center justify-center">
                        <CalendarClock className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">
                          {format(new Date(Date.now() - 3600000), "PPp", { locale: enUS })}
                        </p>
                        <div className="text-sm">
                          <span className="font-medium">John Doe</span> {t("history.changedPriority")}
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                            {t("common.medium")}
                          </Badge>
                          <ArrowRight className="h-3 w-3" />
                          <Badge variant="outline" className="bg-red-100 text-red-800">
                            {t("common.high")}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="relative pl-8">
                      <div className="absolute left-0 top-1 h-7 w-7 rounded-full bg-muted flex items-center justify-center">
                        <CalendarClock className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">
                          {format(new Date(Date.now() - 86400000), "PPp", { locale: enUS })}
                        </p>
                        <div className="text-sm">
                          <span className="font-medium">Jane Smith</span> {t("history.updatedDescription")}
                        </div>
                        <div className="text-xs text-muted-foreground bg-muted p-2 rounded-md">
                          <p className="mb-1">{t("history.previousValue")}:</p>
                          <p className="italic">&ldquo;{t("history.oldDescription")}&rdquo;</p>
                          <p className="mt-2 mb-1">{t("history.newValue")}:</p>
                          <p className="italic">&ldquo;{t("history.newDescription")}&rdquo;</p>
                        </div>
                      </div>
                    </div>

                    <div className="relative pl-8">
                      <div className="absolute left-0 top-1 h-7 w-7 rounded-full bg-muted flex items-center justify-center">
                        <CalendarClock className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">
                          {format(new Date(Date.now() - 172800000), "PPp", { locale: enUS })}
                        </p>
                        <div className="text-sm">
                          <span className="font-medium">Robert Johnson</span> {t("history.createdItem")}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Inbox Tab */}
              <TabsContent value="inbox" className="space-y-4 overflow-y-auto max-h-[400px] pr-6">
                <div className="flex flex-col items-center justify-center h-[300px] text-center p-4">
                  <Inbox className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">{t("common.inboxEmpty")}</h3>
                  <p className="text-sm text-muted-foreground max-w-[250px]">
                    {t("common.inboxDescription")}
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <DialogFooter className="px-6 py-4 border-t flex justify-between items-center">
          {/* Move to another column */}
          {allLanes.length > 1 && (
            <div className="flex flex-1 items-center gap-2">
              <Select
                value={targetLaneId}
                onValueChange={setTargetLaneId}
                disabled={allLanes.length <= 1}
              >
                <SelectTrigger className="w-[240px]">
                  <SelectValue placeholder={t("common.selectColumn")} />
                </SelectTrigger>
                <SelectContent>
                  {allLanes.map((lane) => (
                    <SelectItem
                      key={lane.id}
                      value={lane.id}
                      disabled={lane.id === currentLane?.id}
                    >
                      {t("common.moveTo")} {lane.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                onClick={handleMoveLane}
                disabled={
                  !targetLaneId ||
                  targetLaneId === currentLane?.id ||
                  allLanes.length <= 1
                }
                className="gap-1"
              >
                <ArrowRight className="h-4 w-4" />
                {t("common.move")}
              </Button>
            </div>
          )}
          <Button onClick={onClose}>{t("common.close")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
