"use client";

// React and hooks
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslate } from "@mug/contexts/translate";

// Form validation
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

// Date formatting
import { format } from "date-fns";
import { enUS } from "date-fns/locale";

// Models and types
import { MockLane, MockItem, Priority } from "@mug/constants/mock-lanes";
import { CreateItemDTO } from "@mug/services/item";

// Icons
import { CalendarIcon, Check, Plus, X } from "lucide-react";

// UI Components - Dialog
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";

// UI Components - Form
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@mug/components/ui/form";

// UI Components - Inputs
import { Input } from "@mug/components/ui/input";
import { Textarea } from "@mug/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";

// UI Components - Other
import { Button } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { Calendar } from "@mug/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@mug/components/ui/popover";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";

// Utilities
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";

interface CreateItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateItem: (item: Partial<MockItem>, laneId: string) => void;
  lanes: MockLane[];
  initialLaneId?: string;
  availableTags?: string[];
  availableAssignees?: { id: string; name: string; avatar: string }[];
}

/**
 * Validation schema for the form
 */
const formSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().optional(),
  priority: z.enum(["high", "medium", "low"]).optional(),
  laneId: z.string().min(1, { message: "Column is required" }),
  assigneeId: z.string().optional(),
  personId: z.string().optional(),
  dueDate: z.date().optional(),
  tags: z.array(z.string()),
});

/**
 * Type for form values
 */
type FormValues = z.infer<typeof formSchema>;

export function CreateItemModal({
  isOpen,
  onClose,
  onCreateItem,
  lanes,
  initialLaneId,
  availableTags = [],
  availableAssignees = [],
}: CreateItemModalProps) {
  const { t } = useTranslate();
  const [tagInput, setTagInput] = useState("");

  /**
   * Initialize form with default values
   */
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      priority: undefined,
      laneId: initialLaneId || (lanes.length > 0 ? lanes[0].id : ""),
      assigneeId: "",
      personId: "",
      tags: [],
    },
  });

  /**
   * Update laneId value when initialLaneId changes
   */
  useEffect(() => {
    if (initialLaneId) {
      form.setValue("laneId", initialLaneId);
    }
  }, [initialLaneId, form]);

  /**
   * Add a new tag to the form
   */
  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (!trimmedTag) return;

    // Validate tag
    if (trimmedTag.length < 2) {
      // Too short
      return;
    }
    if (trimmedTag.length > 20) {
      // Too long
      return;
    }
    if (!/^[a-zA-Z0-9\-_]+$/.test(trimmedTag)) {
      // Invalid characters
      return;
    }

    const currentTags = form.getValues("tags");
    if (currentTags.includes(trimmedTag)) {
      // Tag already exists
      return;
    }

    form.setValue("tags", [...currentTags, trimmedTag]);
    setTagInput("");
  };

  /**
   * Remove a tag from the form
   */
  const removeTag = (tag: string) => {
    const currentTags = form.getValues("tags");
    form.setValue(
      "tags",
      currentTags.filter((t) => t !== tag)
    );
  };

  /**
   * Handle form submission
   */
  const onSubmit = (values: FormValues): void => {
    // Create DTO from form values
    const itemDTO: CreateItemDTO = {
      title: values.title,
      description: values.description,
      priority: values.priority,
      laneId: values.laneId,
      tags: values.tags,
      dueDate: values.dueDate ? values.dueDate.toISOString() : undefined,
    };

    // Add assignee if selected and not "unassigned"
    if (values.assigneeId && values.assigneeId !== "unassigned") {
      itemDTO.assigneeId = values.assigneeId;
    }

    // Add person if selected and not "unassigned"
    if (values.personId && values.personId !== "unassigned") {
      itemDTO.personId = values.personId;
    }

    // Create a partial MockItem for the UI (in a real app, this would come from the API)
    const newItem: Partial<MockItem> = {
      title: values.title,
      description: values.description || "",
      priority: values.priority as Priority,
      tags: values.tags,
      dueDate: itemDTO.dueDate,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Add assignee if selected
    if (values.assigneeId && values.assigneeId !== "unassigned") {
      const assignee = availableAssignees.find((a) => a.id === values.assigneeId);
      if (assignee) {
        newItem.assignee = assignee;
      }
    }

    onCreateItem(newItem, values.laneId);
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] gap-0 flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>{t("common.newItem")}</DialogTitle>
          <DialogDescription>
            {t("common.createItemDescription")}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            id="create-item-form"
            onSubmit={form.handleSubmit((data) => onSubmit(data as FormValues))}
            className="flex-1 flex flex-col overflow-auto py-4 px-6 space-y-6"
          >
            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="gap-0">
                    {t("common.title")}
                    <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder={t("common.titlePlaceholder")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("common.description")}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("common.descriptionPlaceholder")}
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Column and Priority */}
            <div className="grid grid-cols-2 gap-4">
              {/* Column */}
              <FormField
                control={form.control}
                name="laneId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="gap-0">
                      {t("common.column")}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("common.selectColumn")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {lanes.map((lane) => (
                          <SelectItem key={lane.id} value={lane.id}>
                            {lane.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority */}
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("common.priority")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ""}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("common.selectPriority")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="high">{t("common.high")}</SelectItem>
                        <SelectItem value="medium">{t("common.medium")}</SelectItem>
                        <SelectItem value="low">{t("common.low")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Assignee and Person */}
            <div className="grid grid-cols-2 gap-4">
              {/* Assignee */}
              <FormField
                control={form.control}
                name="assigneeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("common.assignee")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ""}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("common.selectAssignee")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="unassigned">{t("common.unassigned")}</SelectItem>
                        {availableAssignees.map((assignee) => (
                          <SelectItem
                            key={assignee.id}
                            value={assignee.id}
                            className="flex items-center gap-2"
                          >
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage
                                  src={assignee.avatar}
                                  alt={assignee.name}
                                />
                                <AvatarFallback>
                                  {assignee.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              {assignee.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Person */}
              <FormField
                control={form.control}
                name="personId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("common.person")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ""}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("common.selectPerson")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="unassigned">{t("common.unassigned")}</SelectItem>
                        {availableAssignees.map((person) => (
                          <SelectItem
                            key={person.id}
                            value={person.id}
                            className="flex items-center gap-2"
                          >
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage
                                  src={person.avatar}
                                  alt={person.name}
                                />
                                <AvatarFallback>
                                  {person.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              {person.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Due Date */}
            <div className="mt-4">
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>{t("common.dueDate")}</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={classNameBuilder(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP", { locale: enUS })
                            ) : (
                              <span>{t("common.selectDate")}</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                          initialFocus
                          locale={enUS}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>



            {/* Tags */}
            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("common.tags")}</FormLabel>
                  <div className="space-y-4">
                    {/* Selected tags */}
                    <div className="flex flex-wrap gap-2 mb-2">
                      {field.value.map((tag: string) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="px-3 py-1"
                        >
                          {tag}
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 ml-1 -mr-1"
                            onClick={() => removeTag(tag)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>

                    {/* Tag input with autocomplete */}
                    <div className="flex gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer">
                              {tagInput || t("common.addTag")}
                            </div>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent
                          className="p-0 w-[300px]"
                          align="start"
                          side="bottom"
                          sideOffset={5}
                        >
                          <div className="border-b px-3 py-2 text-xs text-muted-foreground">
                            {t("common.tagsHelpText")}
                          </div>
                          <Command>
                            <CommandInput
                              placeholder={t("common.searchTags")}
                              value={tagInput}
                              onValueChange={setTagInput}
                            />
                            <CommandList>
                              <CommandEmpty>
                                {tagInput.trim() !== "" ? (
                                  <div className="py-3 px-4 flex flex-col items-center">
                                    <p className="text-center mb-2">{t("common.noTagsFound")}</p>
                                    <Button
                                      variant="default"
                                      size="sm"
                                      className="w-full"
                                      onClick={() => {
                                        addTag(tagInput);
                                        document.querySelector('[data-state="open"]')?.dispatchEvent(
                                          new KeyboardEvent('keydown', { key: 'Escape' })
                                        );
                                      }}
                                    >
                                      <Plus className="mr-2 h-4 w-4" />
                                      {t("common.createTag")} &quot;{tagInput}&quot;
                                    </Button>
                                  </div>
                                ) : (
                                  <p className="py-2 px-4">{t("common.typeToSearch")}</p>
                                )}
                              </CommandEmpty>

                              {/* Create new tag option always visible */}
                              {tagInput.trim() !== "" && (
                                <CommandGroup heading={t("common.createNew")}>
                                  <CommandItem
                                    onSelect={() => {
                                      addTag(tagInput);
                                      document.querySelector('[data-state="open"]')?.dispatchEvent(
                                        new KeyboardEvent('keydown', { key: 'Escape' })
                                      );
                                    }}
                                  >
                                    <Plus className="mr-2 h-4 w-4" />
                                    <span className="font-medium">{t("common.createTag")} &quot;{tagInput}&quot;</span>
                                  </CommandItem>
                                </CommandGroup>
                              )}

                              <CommandGroup heading={t("common.suggestedTags")}>
                                {availableTags
                                  .filter(tag =>
                                    !(field.value || []).includes(tag) &&
                                    tag.toLowerCase().includes(tagInput.toLowerCase())
                                  )
                                  .map((tag) => (
                                    <CommandItem
                                      key={tag}
                                      value={tag}
                                      onSelect={(value) => {
                                        addTag(value);
                                        document.querySelector('[data-state="open"]')?.dispatchEvent(
                                          new KeyboardEvent('keydown', { key: 'Escape' })
                                        );
                                      }}
                                    >
                                      <span>{tag}</span>
                                    </CommandItem>
                                  ))
                                }
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

          </form>
        </Form>

        <DialogFooter className="px-6 py-4 border-t">
          <Button type="button" variant="outline" onClick={onClose}>
            {t("common.cancel")}
          </Button>
          <Button type="submit" form="create-item-form">
            <Check className="mr-2 h-4 w-4" />
            {t("common.create")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
