"use client";

// React and hooks
import { useTranslate } from "@mug/contexts/translate";

// Third-party libraries
import { Droppable, Draggable } from "react-beautiful-dnd";

// UI Components
import { Button } from "@mug/components/ui/button";
import { FlowCard } from "./flow-card";

// Icons
import { ChevronLeft, ChevronRight, SquarePlus, Edit } from "lucide-react";

// Utils and types
import { MockLane, MockItem } from "@mug/constants/mock-lanes";

/**
 * Direction type for lane movement
 */
type MoveDirection = "left" | "right";

/**
 * Props for the FlowLane component
 */
interface FlowLaneProps {
  /** The lane data */
  lane: MockLane;
  /** The index of the lane in the lanes array */
  index: number;
  /** Whether the lane is in edit mode */
  isEditMode: boolean;
  /** Function to add a new item to the lane */
  onAddItem: (laneId: string) => void;
  /** Function to move the lane left or right */
  onMoveLane: (laneId: string, direction: MoveDirection) => void;
  /** Function to edit the lane */
  onEditLane?: (lane: MockLane) => void;
  /** Total number of lanes */
  lanesCount: number;
  /** Function to handle item click */
  onItemClick?: (item: MockItem, lane?: MockLane) => void;
}

/**
 * FlowLane component displays a single lane in the kanban board
 */
export function FlowLane({
  lane,
  index,
  isEditMode,
  onAddItem,
  onMoveLane,
  onEditLane,
  lanesCount,
  onItemClick,
}: FlowLaneProps) {
  const { t } = useTranslate();

  return (
    <div className="rounded-lg shadow-md min-h-[500px] min-w-[300px] flex-shrink-0 border border-border hover:shadow-lg hover:border-border/80 hover:bg-muted/25 transition-all flex flex-col">
      <div
        className="flex justify-between items-center py-2 border-b mb-2 rounded-t-md"
        style={{
          backgroundColor: lane.backgroundColor
            ? `${lane.backgroundColor}80`
            : "var(--bg-muted)",
        }}
      >
        {isEditMode && (
          <Button
            size="icon"
            variant="ghost"
            onClick={() => onMoveLane(lane.id, "left")}
            disabled={index === 0}
            className={index === 0 ? "cursor-not-allowed" : "cursor-pointer"}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}
        <h2
          className={`text-xl font-semibold ${isEditMode ? "flex-1" : "w-full text-center"}`}
        >
          {lane.title} ({lane.items.length})
          {lane.description && !isEditMode && (
            <small className="px-4 block text-xs text-muted-foreground">
              {lane.description}
            </small>
          )}
        </h2>
        {isEditMode && (
          <Button
            size="icon"
            variant="ghost"
            onClick={() => onEditLane?.(lane)}
          >
            <Edit className="h-4 w-4" />
          </Button>
        )}
        {isEditMode && (
          <Button
            size="icon"
            variant="ghost"
            onClick={() => onMoveLane(lane.id, "right")}
            disabled={index === lanesCount - 1}
            className={
              index === lanesCount - 1 ? "cursor-not-allowed" : "cursor-pointer"
            }
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* WIP Limit indicator */}
      {lane.wipLimit && (
        <div className="px-4 pb-2 flex justify-between items-center text-xs">
          <span className="text-muted-foreground">{t("flows.wipLimit")}</span>
          <span
            className={`font-medium ${
              lane.items.length > lane.wipLimit
                ? "text-destructive"
                : "text-muted-foreground"
            }`}
          >
            {lane.items.length}/{lane.wipLimit}
          </span>
        </div>
      )}

      <Droppable
        droppableId={lane.id}
        direction="vertical"
        isDropDisabled={false}
        isCombineEnabled={false}
      >
        {(provided) => (
          <div className="flex flex-col flex-1">
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className="space-y-2 px-2 flex-1 overflow-y-auto"
            >
              {lane.items.map((item: MockItem, itemIndex: number) => (
                <Draggable
                  key={item.id}
                  draggableId={item.id}
                  index={itemIndex}
                >
                  {(provided) => (
                    <FlowCard
                      item={item}
                      index={itemIndex}
                      provided={provided}
                      lane={lane}
                      onItemClick={onItemClick}
                    />
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
            <div className="p-3 pt-0">
              <Button
                variant="ghost"
                className="w-full border border-dashed border-border/50 hover:border-border"
                onClick={() => onAddItem(lane.id)}
              >
                <SquarePlus className="mr-2 h-4 w-4" />
                {t("common.addItem")}
              </Button>
            </div>
          </div>
        )}
      </Droppable>
    </div>
  );
}
