"use client";

import { useState } from "react";
import { Search, Filter, X, Check, Calendar, Columns, PlusCircle } from "lucide-react";
import { Priority } from "@mug/constants/mock-lanes";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandList,
  CommandSeparator,
} from "@mug/components/ui/command";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@mug/components/ui/accordion";
import { Badge } from "@mug/components/ui/badge";
import { Checkbox } from "@mug/components/ui/checkbox";
import { Slider } from "@mug/components/ui/slider";
import { useTranslate } from "@mug/contexts/translate";
import { format, subDays, startOfMonth, endOfMonth, startOfYear, endOfYear } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";
import {
  Popover as DatePopover,
  PopoverContent as DatePopoverContent,
  PopoverTrigger as DatePopoverTrigger,
} from "@mug/components/ui/popover";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";
import { Calendar as CalendarComponent } from "@mug/components/ui/calendar";

export type DateFilterType =
  | "all"
  | "today"
  | "last7days"
  | "last30days"
  | "thisMonth"
  | "thisYear"
  | "custom";

export interface DateFilter {
  type: DateFilterType;
  startDate: Date | null;
  endDate: Date | null;
}

export interface FiltersState {
  search: string;
  priority: Priority[];
  assignee: string[];
  tags: string[];
  progress: { min: number; max: number };
  dateFilter: DateFilter;
  selectedLane: string; // "all" ou o ID da lane selecionada
}

interface FlowFiltersProps {
  filters: FiltersState;
  onFilterChange: (filters: FiltersState) => void;
  availableTags: string[];
  availableAssignees: { id: string; name: string }[];
  availableLanes: { id: string; title: string }[];
  onClearFilters: () => void;
  onAddItem?: (laneId: string) => void;
}

export function FlowFilters({
  filters,
  onFilterChange,
  availableTags,
  availableAssignees,
  availableLanes,
  onClearFilters,
  onAddItem,
}: FlowFiltersProps) {
  const { t } = useTranslate();
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<FiltersState>(filters);

  // Contagem de filtros ativos (excluindo a busca)
  const activeFiltersCount =
    localFilters.priority.length +
    localFilters.assignee.length +
    localFilters.tags.length +
    (localFilters.progress.min > 0 || localFilters.progress.max < 100 ? 1 : 0) +
    (localFilters.dateFilter.type !== "all" ? 1 : 0) +
    (localFilters.selectedLane !== "all" ? 1 : 0);

  const handleSearchChange = (value: string) => {
    onFilterChange({ ...filters, search: value });
  };

  const handleApplyFilters = () => {
    onFilterChange(localFilters);
    setIsOpen(false);
  };

  const handleResetFilters = () => {
    const resetFilters: FiltersState = {
      ...filters,
      priority: [],
      assignee: [],
      tags: [],
      progress: { min: 0, max: 100 },
      dateFilter: {
        type: "all" as DateFilterType,
        startDate: null,
        endDate: null
      },
      selectedLane: "all"
    };
    setLocalFilters(resetFilters);
    onFilterChange(resetFilters);
  };

  const togglePriority = (priority: Priority) => {
    setLocalFilters((prev) => {
      const newPriorities = prev.priority.includes(priority)
        ? prev.priority.filter((p) => p !== priority)
        : [...prev.priority, priority];
      return { ...prev, priority: newPriorities };
    });
  };

  const toggleAssignee = (assigneeId: string) => {
    setLocalFilters((prev) => {
      const newAssignees = prev.assignee.includes(assigneeId)
        ? prev.assignee.filter((a) => a !== assigneeId)
        : [...prev.assignee, assigneeId];
      return { ...prev, assignee: newAssignees };
    });
  };

  const toggleTag = (tag: string) => {
    setLocalFilters((prev) => {
      const newTags = prev.tags.includes(tag)
        ? prev.tags.filter((t) => t !== tag)
        : [...prev.tags, tag];
      return { ...prev, tags: newTags };
    });
  };

  const handleProgressChange = (values: number[]) => {
    setLocalFilters((prev) => ({
      ...prev,
      progress: { min: values[0], max: values[1] },
    }));
  };

  // Função para atualizar a lane selecionada
  const handleLaneChange = (laneId: string) => {
    setLocalFilters(prev => ({
      ...prev,
      selectedLane: laneId
    }));
    onFilterChange({
      ...filters,
      selectedLane: laneId
    });
  };

  // Função para obter a primeira lane disponível (para o botão de adicionar)
  const getFirstAvailableLane = (): string => {
    if (availableLanes.length > 0) {
      return availableLanes[0].id;
    }
    return ""; // Fallback se não houver lanes disponíveis
  };

  // Função para obter o intervalo de datas com base no tipo de filtro
  const getDateRangeFromType = (type: DateFilterType): { start: Date | null; end: Date | null } => {
    const today = new Date();

    switch (type) {
      case "today":
        return { start: today, end: today };
      case "last7days":
        return { start: subDays(today, 7), end: today };
      case "last30days":
        return { start: subDays(today, 30), end: today };
      case "thisMonth":
        return { start: startOfMonth(today), end: endOfMonth(today) };
      case "thisYear":
        return { start: startOfYear(today), end: endOfYear(today) };
      case "custom":
        return {
          start: localFilters.dateFilter.startDate,
          end: localFilters.dateFilter.endDate
        };
      default:
        return { start: null, end: null };
    }
  };

  // Função para atualizar o filtro de data
  const handleDateFilterChange = (type: DateFilterType) => {
    const { start, end } = getDateRangeFromType(type);

    setLocalFilters(prev => ({
      ...prev,
      dateFilter: {
        type,
        startDate: start,
        endDate: end
      }
    }));
  };

  // Função para atualizar as datas personalizadas
  const handleCustomDateChange = (field: 'startDate' | 'endDate', date: Date | null) => {
    setLocalFilters(prev => ({
      ...prev,
      dateFilter: {
        ...prev.dateFilter,
        type: 'custom',
        [field]: date
      }
    }));
  };



  return (
    <div className="flex items-center gap-2">
      {/* Botão de Adicionar Item */}
      {onAddItem && (
        <Button
          variant="outline"
          size="sm"
          className="h-9"
          onClick={() => {
            // Se "all" estiver selecionado, usa a primeira lane disponível
            const laneId = localFilters.selectedLane === "all"
              ? getFirstAvailableLane()
              : localFilters.selectedLane;

            if (laneId) {
              onAddItem(laneId);
            }
          }}
          disabled={availableLanes.length === 0}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          {t("common.addItem")}
        </Button>
      )}

      {/* Campo de busca */}
      <div className="relative w-[200px]">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder={t("common.search")}
          value={filters.search}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pl-8"
        />
        {filters.search && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-2"
            onClick={() => handleSearchChange("")}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Filtro de Coluna */}
      <div className="flex items-center">
        <Select
          value={localFilters.selectedLane}
          onValueChange={handleLaneChange}
        >
          <SelectTrigger className="w-[180px] h-9">
            <Columns className="mr-2 h-4 w-4" />
            <SelectValue placeholder={t("common.selectColumn")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("common.allColumns")}</SelectItem>
            {availableLanes.map(lane => (
              <SelectItem key={lane.id} value={lane.id}>
                {lane.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Filtro de Data */}
      <div className="flex items-center">
        <Select
          value={localFilters.dateFilter.type}
          onValueChange={(value: DateFilterType) => handleDateFilterChange(value)}
        >
          <SelectTrigger className="w-[180px] h-9">
            <Calendar className="mr-2 h-4 w-4" />
            <SelectValue placeholder={t("common.selectDate")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("common.allDates")}</SelectItem>
            <SelectItem value="today">{t("common.today")}</SelectItem>
            <SelectItem value="last7days">{t("common.last7Days")}</SelectItem>
            <SelectItem value="last30days">{t("common.last30Days")}</SelectItem>
            <SelectItem value="thisMonth">{t("common.thisMonth")}</SelectItem>
            <SelectItem value="thisYear">{t("common.thisYear")}</SelectItem>
            <SelectItem value="custom">{t("common.customPeriod")}</SelectItem>
          </SelectContent>
        </Select>

        {/* Seletor de período personalizado */}
        {localFilters.dateFilter.type === "custom" && (
          <div className="flex items-center ml-2 gap-2">
            <DatePopover>
              <DatePopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={classNameBuilder(
                    "w-[130px] justify-start text-left font-normal h-9",
                    !localFilters.dateFilter.startDate && "text-muted-foreground"
                  )}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {localFilters.dateFilter.startDate ? (
                    format(localFilters.dateFilter.startDate, "dd/MM/yyyy")
                  ) : (
                    <span>{t("common.startDate")}</span>
                  )}
                </Button>
              </DatePopoverTrigger>
              <DatePopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  mode="single"
                  selected={localFilters.dateFilter.startDate || undefined}
                  onSelect={(date) => handleCustomDateChange("startDate", date as Date | null)}
                  initialFocus
                  locale={ptBR}
                />
              </DatePopoverContent>
            </DatePopover>

            <span>-</span>

            <DatePopover>
              <DatePopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={classNameBuilder(
                    "w-[130px] justify-start text-left font-normal h-9",
                    !localFilters.dateFilter.endDate && "text-muted-foreground"
                  )}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {localFilters.dateFilter.endDate ? (
                    format(localFilters.dateFilter.endDate, "dd/MM/yyyy")
                  ) : (
                    <span>{t("common.endDate")}</span>
                  )}
                </Button>
              </DatePopoverTrigger>
              <DatePopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  mode="single"
                  selected={localFilters.dateFilter.endDate || undefined}
                  onSelect={(date) => handleCustomDateChange("endDate", date as Date | null)}
                  initialFocus
                  locale={ptBR}
                />
              </DatePopoverContent>
            </DatePopover>
          </div>
        )}
      </div>

      {/* Popover de filtros */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            aria-label="Filter"
          >
            <Filter className="h-4 w-4" />
            {t("common.filters")}
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1 px-1 py-0 h-5">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0" align="end">
          <Command>
            <CommandInput placeholder={t("common.filterItems")} />
            <CommandList>
              <CommandEmpty>{t("common.noResults")}</CommandEmpty>
              <CommandGroup>
                <Accordion type="multiple" className="w-full">
                  {/* Filtro de Prioridade */}
                  <AccordionItem value="priority">
                    <AccordionTrigger className="px-2">
                      {t("common.priority")}
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="flex flex-col gap-2 px-2">
                        {["high", "medium", "low"].map((priority) => (
                          <div
                            key={priority}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`priority-${priority}`}
                              checked={localFilters.priority.includes(
                                priority as Priority
                              )}
                              onCheckedChange={() =>
                                togglePriority(priority as Priority)
                              }
                            />
                            <label
                              htmlFor={`priority-${priority}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 capitalize"
                            >
                              {t(`common.${priority}`)}
                            </label>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  {/* Filtro de Responsável */}
                  <AccordionItem value="assignee">
                    <AccordionTrigger className="px-2">
                      {t("common.assignee")}
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="flex flex-col gap-2 px-2">
                        {availableAssignees.map((assignee) => (
                          <div
                            key={assignee.id}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`assignee-${assignee.id}`}
                              checked={localFilters.assignee.includes(
                                assignee.id
                              )}
                              onCheckedChange={() =>
                                toggleAssignee(assignee.id)
                              }
                            />
                            <label
                              htmlFor={`assignee-${assignee.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {assignee.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  {/* Filtro de Tags */}
                  <AccordionItem value="tags">
                    <AccordionTrigger className="px-2">
                      {t("common.tags")}
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="flex flex-wrap gap-2 px-2 py-2">
                        {availableTags.map((tag) => (
                          <Badge
                            key={tag}
                            variant={
                              localFilters.tags.includes(tag)
                                ? "default"
                                : "outline"
                            }
                            className="cursor-pointer"
                            onClick={() => toggleTag(tag)}
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  {/* Filtro de Progresso */}
                  <AccordionItem value="progress">
                    <AccordionTrigger className="px-2">
                      {t("common.progress")}
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="px-2 py-4">
                        <Slider
                          defaultValue={[
                            localFilters.progress.min,
                            localFilters.progress.max,
                          ]}
                          max={100}
                          step={5}
                          onValueChange={handleProgressChange}
                        />
                        <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                          <span>{localFilters.progress.min}%</span>
                          <span>{localFilters.progress.max}%</span>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup>
                <div className="flex justify-between p-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleResetFilters}
                  >
                    {t("common.reset")}
                  </Button>
                  <Button size="sm" onClick={handleApplyFilters}>
                    <Check className="mr-2 h-4 w-4" />
                    {t("common.apply")}
                  </Button>
                </div>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Botão para limpar todos os filtros */}
      {(filters.search || activeFiltersCount > 0) && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearFilters}
          className="gap-1"
        >
          <X className="h-4 w-4" />
          {t("common.clearAll")}
        </Button>
      )}
    </div>
  );
}
