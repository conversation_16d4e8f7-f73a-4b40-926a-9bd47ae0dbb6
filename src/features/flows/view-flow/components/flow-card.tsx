"use client";

// React and hooks

// Third-party libraries
import { DraggableProvided } from "react-beautiful-dnd";

// Icons
import {
  AlertTriangle,
  AlertCircle,
  ArrowDown,
  Calendar,
  Tag,
  MessageSquare,
  Paperclip,
} from "lucide-react";

// Utils and types
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";
import { MockItem, MockLane } from "@mug/constants/mock-lanes";

/**
 * Props for the FlowCard component
 */
interface FlowCardProps {
  /** The item data to display in the card */
  item: MockItem;
  /** The index of the item in the lane */
  index: number;
  /** The provided object from react-beautiful-dnd */
  provided: DraggableProvided;
  /** The parent lane containing this item (optional) */
  lane?: MockLane;
  /** Callback function when the item is clicked */
  onItemClick?: (item: MockItem, lane?: MockLane) => void;
}

/**
 * FlowCard component displays a card in the kanban board
 * representing a single item with its details
 */
export function FlowCard({ item, provided, lane, onItemClick }: FlowCardProps) {
  /**
   * <PERSON>le click on the card
   */
  const handleClick = () => {
    if (onItemClick) {
      onItemClick(item, lane);
    }
  };
  return (
    <div
      ref={provided.innerRef}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
    >
      <div
        className={classNameBuilder(
          "p-4 rounded-md bg-card border border-border hover:border-border/80 transition-all cursor-pointer hover:shadow-lg"
        )}
        onClick={handleClick}
      >
        {/* Card header with title and priority */}
        <div className="flex justify-between items-start mb-2">
          <div className="font-medium">{item.title}</div>
          {item.priority && (
            <div className={classNameBuilder(
              "text-xs px-2 py-1 rounded-full",
              item.priority === "high" ? "bg-red-100 text-red-800" :
              item.priority === "medium" ? "bg-yellow-100 text-yellow-800" :
              "bg-blue-100 text-blue-800"
            )}>
              {item.priority === "high" ? (
                <AlertTriangle className="h-3 w-3 inline mr-1" />
              ) : item.priority === "medium" ? (
                <AlertCircle className="h-3 w-3 inline mr-1" />
              ) : (
                <ArrowDown className="h-3 w-3 inline mr-1" />
              )}
              {item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}
            </div>
          )}
        </div>

        {/* Description - limited to 2 lines with ellipsis */}
        {item.description && (
          <div className="text-sm text-muted-foreground mb-3 line-clamp-2 overflow-hidden">
            {item.description}
          </div>
        )}

        {/* Progress bar */}
        {item.progress !== undefined && (
          <div className="mb-3">
            <div className="h-1.5 w-full bg-muted rounded-full overflow-hidden">
              <div
                className={classNameBuilder(
                  "h-full",
                  item.progress >= 70 ? "bg-green-500" :
                  item.progress >= 30 ? "bg-yellow-500" :
                  "bg-red-500"
                )}
                style={{ width: `${item.progress}%` }}
              />
            </div>
            <div className="text-xs text-muted-foreground mt-1 text-right">
              {item.progress}%
            </div>
          </div>
        )}

        {/* Tags - limited to 3 with counter for the rest */}
        {item.tags && item.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {item.tags.slice(0, 3).map((tag, i) => (
              <span key={i} className="text-xs bg-muted px-2 py-0.5 rounded-full flex items-center">
                <Tag className="h-2.5 w-2.5 mr-1" />
                {tag}
              </span>
            ))}
            {item.tags.length > 3 && (
              <span className="text-xs bg-muted px-2 py-0.5 rounded-full flex items-center">
                +{item.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Footer with metadata */}
        <div className="flex justify-between items-center mt-2 pt-2 border-t border-border/50 text-xs text-muted-foreground">
          {/* Assignee */}
          <div className="flex items-center">
            {item.assignee && (
              <div className="flex items-center">
                <img
                  src={item.assignee.avatar}
                  alt={item.assignee.name}
                  className="h-5 w-5 rounded-full mr-1"
                  title={item.assignee.name}
                />
              </div>
            )}

            {/* Collaborators */}
            {item.collaborators && item.collaborators.length > 0 && (
              <div className="flex -space-x-1 ml-1">
                {item.collaborators.map((user, i) => (
                  <img
                    key={i}
                    src={user.avatar}
                    alt={user.name}
                    className="h-4 w-4 rounded-full border border-background"
                    title={user.name}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Metadata icons */}
          <div className="flex items-center gap-2">
            {item.dueDate && (
              <span className="flex items-center" title={`Due date: ${item.dueDate}`}>
                <Calendar className="h-3 w-3 mr-1" />
                {new Date(item.dueDate).toLocaleDateString('en-US', { day: '2-digit', month: '2-digit' })}
              </span>
            )}

            {item.comments && item.comments > 0 && (
              <span className="flex items-center" title={`${item.comments} comments`}>
                <MessageSquare className="h-3 w-3 mr-1" />
                {item.comments}
              </span>
            )}

            {item.attachments && item.attachments > 0 && (
              <span className="flex items-center" title={`${item.attachments} attachments`}>
                <Paperclip className="h-3 w-3 mr-1" />
                {item.attachments}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
