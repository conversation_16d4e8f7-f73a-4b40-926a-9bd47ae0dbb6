"use client";

import { useState } from "react";
import { Mock<PERSON>ane, MockItem } from "@mug/constants/mock-lanes";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@mug/components/ui/table";


import { ArrowUpDown } from "lucide-react";

interface ListViewProps {
  lanes: MockLane[];
  onAddItem: (laneId: string) => void;
  onItemClick?: (item: MockItem, lane?: MockLane) => void;
}

export function ListView({ lanes, onAddItem, onItemClick }: ListViewProps) {
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // Flatten all items from all lanes
  const allItems = lanes.flatMap(lane =>
    lane.items.map(item => ({
      ...item,
      lane: lane.title,
      laneId: lane.id
    }))
  );

  // Sort items
  const sortedItems = [...allItems].sort((a, b) => {
    const comparison = a.title.localeCompare(b.title);
    return sortDirection === "asc" ? comparison : -comparison;
  });

  const toggleSortDirection = () => {
    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
  };

  return (
    <div className="space-y-4">
      {/* O seletor de coluna foi movido para o componente de filtros */}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[300px]">
                <div className="flex items-center cursor-pointer" onClick={toggleSortDirection}>
                  Título
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>Descrição</TableHead>
              <TableHead>Coluna</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedItems.length > 0 ? (
              sortedItems.map(item => (
                <TableRow
                  key={item.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => {
                    if (onItemClick) {
                      // Encontrar a lane original do item
                      const lane = lanes.find(l => l.id === item.laneId);
                      onItemClick(item, lane);
                    }
                  }}
                >
                  <TableCell className="font-medium">{item.title}</TableCell>
                  <TableCell>{item.description || "—"}</TableCell>
                  <TableCell>{item.lane}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center">
                  Nenhum item encontrado.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
