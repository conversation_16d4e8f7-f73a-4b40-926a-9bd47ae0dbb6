"use client";

import { Di<PERSON>atch, Fragment, SetStateAction, useState } from "react";
import { Drag<PERSON>rop<PERSON>ontext, DropResult } from "react-beautiful-dnd";
import { SquarePlus } from "lucide-react";
import { toast } from "sonner";
import { MockLane, MockItem } from "@mug/constants/mock-lanes";
import { FlowLane, CreateLaneModal, LanePosition } from "./components";
import { EditLaneModal } from "./components/edit-lane-modal";
import { useTranslate } from "@mug/contexts/translate";
import { FlowMode } from "@mug/models/flow";

interface KanbanViewProps {
  lanes: MockLane[];
  onDragEnd: (result: DropResult) => void;
  onAddItem: (laneId: string) => void;
  onAddLane: () => MockLane;
  setLanes: Dispatch<SetStateAction<MockLane[]>>;
  isEditMode?: boolean;
  onToggleEditMode?: () => void;
  onItemClick?: (item: MockItem, lane?: MockLane) => void;
  flowMode?: FlowMode; // Add flow mode to determine which fields to show in the create lane modal
}

export function KanbanView({
  lanes,
  onDragEnd,
  onAddItem,
  onAddLane,
  setLanes,
  isEditMode: externalEditMode,
  onToggleEditMode: _onToggleEditMode,
  onItemClick,
  flowMode = FlowMode.ATTRACT // Default to ATTRACT if not provided
}: KanbanViewProps) {
  const [internalEditMode] = useState(false);
  const [isCreateLaneModalOpen, setIsCreateLaneModalOpen] = useState(false);
  const [isEditLaneModalOpen, setIsEditLaneModalOpen] = useState(false);
  const [selectedLane, setSelectedLane] = useState<MockLane | null>(null);
  const { t } = useTranslate();

  // Use external state if provided, otherwise use internal state
  const isEditMode = externalEditMode !== undefined ? externalEditMode : internalEditMode;

  const addNewLaneHandler = () => {
    setIsCreateLaneModalOpen(true);
  };

  // Função para criar uma nova lane com os dados do modal
  const handleCreateLane = (laneData: Partial<MockLane>, position: LanePosition) => {
    // Criar a nova lane usando a função onAddLane
    const newLane = onAddLane();

    // Atualizar os dados da lane com os valores do formulário
    const updatedLane: MockLane = {
      ...newLane,
      title: laneData.title || newLane.title,
      description: laneData.description || newLane.description || "",
      wipLimit: laneData.wipLimit,
      items: []
    };

    // Determinar a posição da nova lane no array
    let updatedLanes: MockLane[];

    if (position === "start") {
      // Adicionar no início
      updatedLanes = [updatedLane, ...lanes];
    } else if (position === "end") {
      // Adicionar no final
      updatedLanes = [...lanes, updatedLane];
    } else {
      // Adicionar após uma lane específica
      const afterIndex = lanes.findIndex(lane => lane.id === position.afterLaneId);
      if (afterIndex >= 0) {
        updatedLanes = [
          ...lanes.slice(0, afterIndex + 1),
          updatedLane,
          ...lanes.slice(afterIndex + 1)
        ];
      } else {
        // Fallback para o final se a lane não for encontrada
        updatedLanes = [...lanes, updatedLane];
      }
    }

    setLanes(updatedLanes);
    toast.success(t("common.columnCreated"));
  };

  const removeLaneHandler = (laneId: string) => {
    if (confirm("Tem certeza que deseja remover esta coluna?")) {
      setLanes(lanes.filter(lane => lane.id !== laneId));
      toast.success("Coluna removida com sucesso!");
    }
  };

  const moveLaneHandler = (laneId: string, direction: "left" | "right") => {
    const index = lanes.findIndex(lane => lane.id === laneId);
    if (index < 0) return;

    const newIndex = direction === "left" ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= lanes.length) return;

    const updatedLanes = [...lanes];
    const [movedLane] = updatedLanes.splice(index, 1);
    updatedLanes.splice(newIndex, 0, movedLane);

    setLanes(updatedLanes);
  };

  // Function to handle editing a lane
  const handleEditLane = (lane: MockLane) => {
    setSelectedLane(lane);
    setIsEditLaneModalOpen(true);
  };

  // Function to update a lane
  const handleUpdateLane = (laneId: string, laneData: Partial<MockLane>, position?: LanePosition) => {
    // Find the lane index
    const laneIndex = lanes.findIndex(lane => lane.id === laneId);
    if (laneIndex < 0) return;

    // Create updated lane object
    const updatedLane = {
      ...lanes[laneIndex],
      ...laneData
    };

    // If position is not changing, just update the lane in place
    if (!position) {
      const updatedLanes = [...lanes];
      updatedLanes[laneIndex] = updatedLane;
      setLanes(updatedLanes);
      toast.success(t("common.columnUpdated"));
      return;
    }

    // If position is changing, remove the lane and insert it at the new position
    const lanesWithoutCurrent = lanes.filter(lane => lane.id !== laneId);
    let updatedLanes: MockLane[];

    if (position === "start") {
      updatedLanes = [updatedLane, ...lanesWithoutCurrent];
    } else if (position === "end") {
      updatedLanes = [...lanesWithoutCurrent, updatedLane];
    } else {
      // Position after a specific lane
      const afterIndex = lanesWithoutCurrent.findIndex(lane => lane.id === position.afterLaneId);
      if (afterIndex >= 0) {
        updatedLanes = [
          ...lanesWithoutCurrent.slice(0, afterIndex + 1),
          updatedLane,
          ...lanesWithoutCurrent.slice(afterIndex + 1)
        ];
      } else {
        // Fallback to the end if the lane is not found
        updatedLanes = [...lanesWithoutCurrent, updatedLane];
      }
    }

    setLanes(updatedLanes);
    toast.success(t("common.columnUpdated"));
  };

  // Function to delete a lane and migrate items if needed
  const handleDeleteLane = (laneId: string, targetLaneId?: string) => {
    // Find the lane to delete
    const laneToDelete = lanes.find(lane => lane.id === laneId);
    if (!laneToDelete) return;

    // Check if the lane has items that need to be migrated
    if (laneToDelete.items.length > 0 && targetLaneId) {
      // Find the target lane
      const targetLaneIndex = lanes.findIndex(lane => lane.id === targetLaneId);
      if (targetLaneIndex < 0) {
        toast.error(t("common.targetLaneNotFound"));
        return;
      }

      // Migrate items to the target lane
      const updatedLanes = [...lanes];
      updatedLanes[targetLaneIndex] = {
        ...updatedLanes[targetLaneIndex],
        items: [...updatedLanes[targetLaneIndex].items, ...laneToDelete.items]
      };

      // Remove the lane
      setLanes(updatedLanes.filter(lane => lane.id !== laneId));
      toast.success(t("common.columnDeletedWithMigration"));
    } else {
      // Just remove the lane without migration
      setLanes(lanes.filter(lane => lane.id !== laneId));
      toast.success(t("common.columnDeleted"));
    }
  };

  return (
    <div className="relative select-none">

      {/* Container com largura fixa e scroll horizontal */}
      <div className="overflow-x-auto pb-4 border rounded-md">
        <DragDropContext onDragEnd={onDragEnd}>
          {/* Container interno com largura mínima para garantir que o scroll apareça quando necessário */}
          <div className="space-x-4 flex p-4" style={{ minWidth: 'max-content' }}>
            {lanes.length > 0 ? (
              <Fragment>
                {lanes.map((lane, index) => (
                  <FlowLane
                    key={lane.id}
                    lane={lane}
                    index={index}
                    isEditMode={isEditMode}
                    onAddItem={onAddItem}
                    onRemoveLane={removeLaneHandler}
                    onMoveLane={moveLaneHandler}
                    onEditLane={handleEditLane}
                    lanesCount={lanes.length}
                    onItemClick={onItemClick}
                  />
                ))}
                {isEditMode && (
                  <button
                    className="p-2 rounded-lg flex flex-col justify-center items-center border-dashed shadow-md min-h-[500px] min-w-[280px] flex-shrink-0 text-muted-foreground border-border/50 border hover:shadow-lg hover:text-muted-foreground/80 hover:border-border/80 hover:bg-muted/25 transition-all cursor-pointer"
                    onClick={addNewLaneHandler}
                  >
                    <SquarePlus className="h-8 w-8 mb-2" />
                    <p>Adicionar Nova Coluna</p>
                  </button>
                )}
              </Fragment>
            ) : (
              <div className="w-full flex">
                {isEditMode ? (
                  <button
                    className="p-4 rounded-lg flex flex-col justify-center items-center border-dashed shadow-md min-h-[500px] min-w-[280px] flex-shrink-0 text-muted-foreground border-border/50 border hover:shadow-lg hover:text-muted-foreground/80 hover:border-border/80 hover:bg-muted/25 transition-all cursor-pointer"
                    onClick={addNewLaneHandler}
                  >
                    <SquarePlus className="h-8 w-8 mb-2" />
                    <p>Adicionar Nova Coluna</p>
                  </button>
                ) : (
                  <div className="p-4 rounded-lg flex flex-col justify-center items-center min-h-[500px] w-full text-muted-foreground">
                    <p>Não há colunas neste fluxo.</p>
                    <p>Ative o modo de edição para adicionar colunas.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </DragDropContext>
      </div>

      {/* Modal de criação de lane */}
      <CreateLaneModal
        isOpen={isCreateLaneModalOpen}
        onClose={() => setIsCreateLaneModalOpen(false)}
        onCreateLane={handleCreateLane}
        lanes={lanes}
        flowMode={flowMode}
      />

      {/* Modal de edição de lane */}
      {selectedLane && (
        <EditLaneModal
          isOpen={isEditLaneModalOpen}
          onClose={() => {
            setIsEditLaneModalOpen(false);
            setSelectedLane(null);
          }}
          onUpdateLane={handleUpdateLane}
          onDeleteLane={handleDeleteLane}
          lane={selectedLane}
          lanes={lanes.filter(lane => lane.id !== selectedLane.id)}

          flowMode={flowMode}
        />
      )}
    </div>
  );
}
