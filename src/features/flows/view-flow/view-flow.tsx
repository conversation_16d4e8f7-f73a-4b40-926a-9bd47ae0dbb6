"use client";

// React and Next.js imports
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

// Third-party libraries
import { DropResult } from "react-beautiful-dnd";
import { format } from "date-fns";
import { toast } from "sonner";

// Icons
import {
  ArrowLeft,
  Archive,
  BarChart,
  Calendar,
  Check,
  Columns,
  Edit,
  FileText,
  Info,
  LayoutGrid,
  List,
  MoreHorizontal,
  Tag,
  Trash,
  Users,
} from "lucide-react";

// Context
import { useTranslate } from "@mug/contexts/translate";

// Types and models
import { Flow, FlowMode } from "@mug/models/flow";
import {
  getMockLanesForFlow,
  MockItem,
  MockLane,
  Priority,
} from "@mug/constants/mock-lanes";

// Constants
import { mockFlows } from "@mug/constants/mock-flows";

// UI Components
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import { Badge } from "@mug/components/ui/badge";
import { Button } from "@mug/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@mug/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@mug/components/ui/tooltip";

// Local components
import { EditFlowModal, UpdateFlowData } from "../components";
import { KanbanView } from "./kanban-view";
import { ListView } from "./list-view";
import {
  CreateItemModal,
  FiltersState,
  FlowFilters,
  ItemDetailsModal,
} from "./components";

interface ViewFlowProps {
  id?: string;
}

export function ViewFlow({ id }: ViewFlowProps) {
  const { t } = useTranslate();
  const router = useRouter();

  const [flow, setFlow] = useState<Flow | null>(null);
  const [lanes, setLanes] = useState<MockLane[]>([]);
  const [viewMode, setViewMode] = useState<"kanban" | "list">("kanban");
  const [loading, setLoading] = useState(true);
  const [isEditingLanes, setIsEditingLanes] = useState(false);

  // Filters state
  const [filters, setFilters] = useState<FiltersState>({
    search: "",
    priority: [] as Priority[],
    assignee: [] as string[],
    tags: [] as string[],
    progress: { min: 0, max: 100 },
    dateFilter: {
      type: "all",
      startDate: null,
      endDate: null,
    },
    selectedLane: "all",
  });

  // Filtered lanes state
  const [filteredLanes, setFilteredLanes] = useState<MockLane[]>([]);

  // Item details modal state
  const [selectedItem, setSelectedItem] = useState<MockItem | null>(null);
  const [selectedItemLane, setSelectedItemLane] = useState<MockLane | null>(
    null
  );
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Create item modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [createItemLaneId, setCreateItemLaneId] = useState<string>("");

  // Edit flow modal state
  const [isEditFlowModalOpen, setIsEditFlowModalOpen] = useState(false);

  useEffect(() => {
    if (!id) {
      router.push("/flows");
      return;
    }

    // Simulate flow loading
    setLoading(true);

    // Find the flow in the mock data
    const foundFlow = mockFlows.find((f) => f.id === id);

    if (foundFlow) {
      setFlow(foundFlow);
      setViewMode(foundFlow.viewMode || "kanban");

      // Load mock lanes for this flow
      const mockLanes = getMockLanesForFlow(id);
      setLanes(mockLanes);
      setFilteredLanes(mockLanes);
    } else {
      toast.error("Flow not found");
      router.push("/flows");
    }

    setLoading(false);
  }, [id, router]);

  // Effect to apply filters when filters or lanes change
  useEffect(() => {
    if (lanes.length === 0) return;

    // Function to filter items based on criteria
    const filterItems = (items: MockItem[]) => {
      return items.filter((item) => {
        // Text search filter
        if (
          filters.search &&
          !item.title.toLowerCase().includes(filters.search.toLowerCase()) &&
          !item.description
            ?.toLowerCase()
            .includes(filters.search.toLowerCase())
        ) {
          return false;
        }

        // Priority filter
        if (
          filters.priority.length > 0 &&
          item.priority &&
          !filters.priority.includes(item.priority)
        ) {
          return false;
        }

        // Assignee filter
        if (
          filters.assignee.length > 0 &&
          item.assignee &&
          !filters.assignee.includes(item.assignee.id)
        ) {
          return false;
        }

        // Tags filter
        if (
          filters.tags.length > 0 &&
          item.tags &&
          !item.tags.some((tag) => filters.tags.includes(tag))
        ) {
          return false;
        }

        // Progress filter
        if (
          item.progress !== undefined &&
          (item.progress < filters.progress.min ||
            item.progress > filters.progress.max)
        ) {
          return false;
        }

        // Date filter
        if (filters.dateFilter.type !== "all" && item.createdAt) {
          const itemDate = new Date(item.createdAt);
          const { startDate, endDate } = filters.dateFilter;

          // If we have defined dates, check if the item is within the range
          if (startDate && endDate) {
            // Adjust the end of day for the end date
            const endOfDay = new Date(endDate);
            endOfDay.setHours(23, 59, 59, 999);

            if (itemDate < startDate || itemDate > endOfDay) {
              return false;
            }
          } else if (startDate && !endDate) {
            if (itemDate < startDate) {
              return false;
            }
          } else if (!startDate && endDate) {
            const endOfDay = new Date(endDate);
            endOfDay.setHours(23, 59, 59, 999);

            if (itemDate > endOfDay) {
              return false;
            }
          }
        }

        return true;
      });
    };

    // Apply filters to all lanes
    let newFilteredLanes = lanes.map((lane) => ({
      ...lane,
      items: filterItems(lane.items),
    }));

    // Filter lanes if a specific lane was selected
    if (filters.selectedLane !== "all") {
      newFilteredLanes = newFilteredLanes.filter(
        (lane) => lane.id === filters.selectedLane
      );
    }

    setFilteredLanes(newFilteredLanes);
  }, [filters, lanes]);

  const handleViewModeChange = (mode: "kanban" | "list") => {
    setViewMode(mode);

    // Update the flow with the new view mode
    if (flow) {
      const updatedFlow = { ...flow, viewMode: mode };
      setFlow(updatedFlow);
      // In a real application, you would save the preference to the backend here
      toast.success(
        `View mode changed to ${mode === "kanban" ? "Kanban" : "List"}`
      );
    }
  };

  const handleDragEnd = (result: DropResult) => {
    const { source, destination } = result;

    // Dropped outside the list
    if (!destination) return;

    // Same position
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    )
      return;

    // Find source and destination lanes
    const sourceLane = lanes.find((lane) => lane.id === source.droppableId);
    const destLane = lanes.find((lane) => lane.id === destination.droppableId);

    if (!sourceLane || !destLane) return;

    // Moving within the same lane
    if (source.droppableId === destination.droppableId) {
      const newItems = Array.from(sourceLane.items);
      const [movedItem] = newItems.splice(source.index, 1);
      newItems.splice(destination.index, 0, movedItem);

      const newLanes = lanes.map((lane) =>
        lane.id === sourceLane.id ? { ...lane, items: newItems } : lane
      );

      setLanes(newLanes);
      return;
    }

    // Moving from one lane to another
    const sourceItems = Array.from(sourceLane.items);
    const [movedItem] = sourceItems.splice(source.index, 1);

    const destItems = Array.from(destLane.items);
    destItems.splice(destination.index, 0, movedItem);

    const newLanes = lanes.map((lane) => {
      if (lane.id === sourceLane.id) return { ...lane, items: sourceItems };
      if (lane.id === destLane.id) return { ...lane, items: destItems };
      return lane;
    });

    setLanes(newLanes);
    toast.success(`Item moved to ${destLane.title}`);
  };

  const handleAddItem = (laneId: string) => {
    // Open the create item modal
    setCreateItemLaneId(laneId);
    setIsCreateModalOpen(true);
  };

  // Function to create a new item
  const handleCreateItem = (newItemData: Partial<MockItem>, laneId: string) => {
    const newItem: MockItem = {
      id: `item-${Date.now()}`,
      title: newItemData.title || "",
      description: newItemData.description || "",
      priority: newItemData.priority,
      progress: newItemData.progress,
      tags: newItemData.tags,
      dueDate: newItemData.dueDate,
      assignee: newItemData.assignee,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedLanes = lanes.map((lane) => {
      if (lane.id === laneId) {
        return {
          ...lane,
          items: [...lane.items, newItem],
        };
      }
      return lane;
    });

    setLanes(updatedLanes);
    toast.success("New item added");
  };

  const handleAddLane = (): MockLane => {
    const title = prompt("Enter the title for the new column:");
    if (!title) return {} as MockLane;

    const newLane: MockLane = {
      id: `lane-${Date.now()}`,
      title,
      items: [],
    };

    return newLane;
  };

  const handleArchiveFlow = () => {
    if (!flow) return;

    const updatedFlow = { ...flow, isArchived: !flow.isArchived };
    setFlow(updatedFlow);

    const action = flow.isArchived ? "unarchived" : "archived";
    toast.success(`Flow ${action} successfully!`);
  };

  const handleDeleteFlow = () => {
    if (!flow) return;

    if (confirm("Are you sure you want to delete this flow?")) {
      toast.success("Flow deleted successfully!");
      router.push("/flows");
    }
  };

  // Function to open the edit flow modal
  const handleOpenEditFlowModal = () => {
    setIsEditFlowModalOpen(true);
  };

  // Function to update the flow
  const handleUpdateFlow = (_flowId: string, flowData: UpdateFlowData) => {
    if (!flow) return;

    // Update the flow with the new data
    const updatedFlow = {
      ...flow,
      name: flowData.title,
      description: flowData.description || "",
      mode: flowData.type.toUpperCase() as FlowMode,
      isArchived: flowData.visibility === "private",
      // In a real application, you would update the members here
    };

    setFlow(updatedFlow);
    setIsEditFlowModalOpen(false);
    toast.success(t("flows.flowUpdated"));
  };

  const toggleLanesEditMode = () => {
    setIsEditingLanes(!isEditingLanes);
    toast.success(
      isEditingLanes
        ? "Lanes edit mode disabled"
        : "Lanes edit mode enabled"
    );
  };

  // Functions to manage filters
  const handleFilterChange = (newFilters: FiltersState) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({
      search: "",
      priority: [],
      assignee: [],
      tags: [],
      progress: { min: 0, max: 100 },
      dateFilter: {
        type: "all",
        startDate: null,
        endDate: null,
      },
      selectedLane: "all",
    });
    toast.success("Filters cleared");
  };

  // Extract all unique tags from items
  const getAllTags = () => {
    const allTags = new Set<string>();
    lanes.forEach((lane) => {
      lane.items.forEach((item) => {
        if (item.tags) {
          item.tags.forEach((tag) => allTags.add(tag));
        }
      });
    });
    return Array.from(allTags);
  };

  // Extract all unique assignees from items
  const getAllAssignees = () => {
    const assigneesMap = new Map<
      string,
      { id: string; name: string; avatar: string }
    >();
    lanes.forEach((lane) => {
      lane.items.forEach((item) => {
        if (item.assignee) {
          assigneesMap.set(item.assignee.id, {
            id: item.assignee.id,
            name: item.assignee.name,
            avatar: item.assignee.avatar || "https://github.com/shadcn.png", // Default avatar if none exists
          });
        }
      });
    });
    return Array.from(assigneesMap.values());
  };

  // Get all available lanes
  const getAllLanes = () => {
    return lanes.map((lane) => ({
      id: lane.id,
      title: lane.title,
    }));
  };

  // Function to open the item details modal
  const handleOpenItemDetails = (item: MockItem, lane?: MockLane) => {
    setSelectedItem(item);
    setSelectedItemLane(lane || null);
    setIsDetailsModalOpen(true);
  };

  // Function to close the item details modal
  const handleCloseItemDetails = () => {
    setIsDetailsModalOpen(false);
    setSelectedItem(null);
    setSelectedItemLane(null);
  };

  // Function to move an item from one lane to another
  const handleMoveItemToLane = (itemId: string, targetLaneId: string) => {
    // Find the item and its current lane
    let sourceItem: MockItem | null = null;
    let sourceLane: MockLane | null = null;

    for (const lane of lanes) {
      const item = lane.items.find((item) => item.id === itemId);
      if (item) {
        sourceItem = item;
        sourceLane = lane;
        break;
      }
    }

    if (!sourceItem || !sourceLane) return;

    // Find the destination lane
    const destLane = lanes.find((lane) => lane.id === targetLaneId);
    if (!destLane) return;

    // Remove the item from the current lane
    const updatedSourceLane = {
      ...sourceLane,
      items: sourceLane.items.filter((item) => item.id !== itemId),
    };

    // Add the item to the destination lane
    const updatedDestLane = {
      ...destLane,
      items: [...destLane.items, sourceItem],
    };

    // Update the lanes state
    const updatedLanes = lanes.map((lane) => {
      if (lane.id === sourceLane.id) return updatedSourceLane;
      if (lane.id === destLane.id) return updatedDestLane;
      return lane;
    });

    setLanes(updatedLanes);
    toast.success(`Item moved to ${destLane.title}`);
  };

  // Function to get the flow mode icon with the appropriate color
  const getFlowModeIcon = (mode: string) => {
    const flowMode = mode.toLowerCase() as FlowMode;
    switch (flowMode) {
      case FlowMode.ATTRACT:
        return (
          <Badge variant="outline" className="bg-blue-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.CONVERT:
        return (
          <Badge variant="outline" className="bg-green-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.ENGAGE:
        return (
          <Badge variant="outline" className="bg-purple-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.CLOSE:
        return (
          <Badge variant="outline" className="bg-orange-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.DELIGHT:
        return (
          <Badge variant="outline" className="bg-pink-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      case FlowMode.ANALYZE:
        return (
          <Badge variant="outline" className="bg-cyan-50">
            {t(`flows.modes.${mode}`)}
          </Badge>
        );
      default:
        return <Badge variant="outline">{t(`flows.modes.${mode}`)}</Badge>;
    }
  };

  if (loading || !flow) {
    return (
      <div className="flex items-center justify-center h-64">Loading...</div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/flows")}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">
            {flow.name}
            {/* Info Icons with Tooltips */}
            <div className="flex flex-1 items-center font-normal justify-end gap-2 text-sm text-muted-foreground">
              {/* Flow Description Tooltip */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 cursor-help">
                      <FileText className="h-4 w-4" />
                      <span>{t("common.description")}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    align="start"
                    className="max-w-sm p-4"
                  >
                    <div className="space-y-2">
                      <h4 className="font-medium">{t("common.description")}</h4>
                      <p className="text-sm">
                        {flow.description || t("flows.noDescription")}
                      </p>
                      {flow.tags && flow.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {flow.tags.map((tag: string) => (
                            <Badge
                              key={tag}
                              variant="secondary"
                              className="flex items-center gap-1 text-xs"
                            >
                              <Tag className="h-3 w-3" />
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <span>•</span>

              {/* Flow Stats Tooltip */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 cursor-help">
                      <BarChart className="h-4 w-4" />
                      <span>{t("common.stats")}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" align="start" className="p-4">
                    <div className="space-y-3">
                      <h4 className="font-medium">{t("common.stats")}</h4>
                      <div className="space-y-2">
                        <div className="grid grid-cols-2 items-center gap-4">
                          <span className="text-muted-foreground flex items-center gap-1">
                            <Columns className="h-4 w-4" />
                            {t("flows.totalItems")}
                          </span>
                          <span className="font-medium text-right">
                            {flow.stats?.totalItems || 0}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 items-center gap-4">
                          <span className="text-muted-foreground flex items-center gap-1">
                            <BarChart className="h-4 w-4" />
                            {t("flows.conversionRate")}
                          </span>
                          <span className="font-medium text-right">
                            {flow.stats?.conversionRate || 0}%
                          </span>
                        </div>
                        <div className="grid grid-cols-2 items-center gap-4">
                          <span className="text-muted-foreground flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {t("flows.avgCycleTime")}
                          </span>
                          <span className="font-medium text-right">
                            {flow.stats?.avgCycleTime || 0} {t("common.days")}
                          </span>
                        </div>
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <span>•</span>

              {/* Flow Members Tooltip */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 cursor-help">
                      <Users className="h-4 w-4" />
                      <span>{t("flows.members")}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" align="start" className="p-4">
                    <div className="space-y-3">
                      <h4 className="font-medium">{t("flows.members")}</h4>
                      <div className="flex flex-col gap-2">
                        {flow.members && flow.members.length > 0 ? (
                          flow.members.map((member: { id: string }) => (
                            <div
                              key={member.id}
                              className="flex items-center gap-2"
                            >
                              <Avatar className="h-6 w-6">
                                <AvatarImage
                                  src={`https://avatar.vercel.sh/${member.id}.png`}
                                />
                                <AvatarFallback>
                                  {member.id.substring(0, 2).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <span>{member.id}</span>
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-muted-foreground">
                            {t("flows.noMembers")}
                          </p>
                        )}
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <span>•</span>

              {/* Flow Details Tooltip */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 cursor-help">
                      <Info className="h-4 w-4" />
                      <span>{t("flows.details")}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" align="start" className="p-4">
                    <div className="space-y-3">
                      <h4 className="font-medium">{t("flows.details")}</h4>
                      <div className="space-y-2 text-sm">
                        <div className="grid grid-cols-2 items-center gap-4">
                          <span className="text-muted-foreground">
                            {t("flows.flowCreatedAt")}
                          </span>
                          <span className="text-right">
                            {flow.createdAt
                              ? format(new Date(flow.createdAt), "PPp")
                              : "-"}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 items-center gap-4">
                          <span className="text-muted-foreground">
                            {t("flows.flowUpdatedAt")}
                          </span>
                          <span className="text-right">
                            {flow.updatedAt
                              ? format(new Date(flow.updatedAt), "PPp")
                              : "-"}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 items-center gap-4">
                          <span className="text-muted-foreground">
                            {t("flows.flowCreatedBy")}
                          </span>
                          <span className="text-right">
                            {flow.createdBy?.name || "Unknown"}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 items-center gap-4">
                          <span className="text-muted-foreground">
                            {t("flows.visibility")}
                          </span>
                          <div className="text-right">
                            <Badge variant="secondary" className="text-xs">
                              {flow.isArchived
                                ? t("flows.visibility.private")
                                : t("flows.visibility.public")}
                            </Badge>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 items-center gap-4">
                          <span className="text-muted-foreground">
                            {t("flows.flowStatus")}
                          </span>
                          <div className="text-right">
                            <Badge
                              variant={flow.isArchived ? "outline" : "default"}
                              className="text-xs"
                            >
                              {flow.isArchived
                                ? t("flows.archived")
                                : t("common.active")}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </h1>
        </div>
        <div className="flex flex-col items-end gap-2">
          <div className="flex flex-wrap flex-1 items-start gap-1">
            {getFlowModeIcon(flow.mode)}
            {flow.isArchived && (
              <Badge variant="secondary">{t("flows.archived")}</Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleOpenEditFlowModal}
              className="gap-2"
            >
              <Edit className="h-4 w-4" />
              {t("common.edit")}
            </Button>

            <Button
              variant={isEditingLanes ? "default" : "outline"}
              size="sm"
              onClick={toggleLanesEditMode}
              className="gap-2"
            >
              {isEditingLanes ? (
                <>
                  <Check className="h-4 w-4" />
                  {t("common.done")}
                </>
              ) : (
                <>
                  <Columns className="h-4 w-4" />
                  {t("flows.editLanes")}
                </>
              )}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleArchiveFlow}>
                  <Archive className="mr-2 h-4 w-4" />
                  {flow.isArchived ? t("flows.unarchive") : t("flows.archive")}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleDeleteFlow}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t("common.delete")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* View Mode Tabs */}
      <Tabs
        value={viewMode}
        onValueChange={(value) =>
          handleViewModeChange(value as "kanban" | "list")
        }
      >
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="kanban" className="flex items-center gap-2">
              <LayoutGrid className="h-4 w-4" />
              Kanban
            </TabsTrigger>
            <TabsTrigger value="list" className="flex items-center gap-2">
              <List className="h-4 w-4" />
              List
            </TabsTrigger>
          </TabsList>

          {/* Filters component */}
          <FlowFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            availableTags={getAllTags()}
            availableAssignees={getAllAssignees()}
            availableLanes={getAllLanes()}
            onClearFilters={handleClearFilters}
            onAddItem={handleAddItem}
          />
        </div>

        <TabsContent value="kanban" className="mt-4">
          <KanbanView
            lanes={filteredLanes}
            onDragEnd={handleDragEnd}
            onAddItem={handleAddItem}
            onAddLane={handleAddLane}
            setLanes={setLanes}
            isEditMode={isEditingLanes}
            onToggleEditMode={toggleLanesEditMode}
            onItemClick={handleOpenItemDetails}
            flowMode={flow?.mode}
          />
        </TabsContent>

        <TabsContent value="list" className="mt-4">
          <ListView
            lanes={filteredLanes}
            onAddItem={handleAddItem}
            onItemClick={handleOpenItemDetails}
          />
        </TabsContent>
      </Tabs>

      {/* Item details modal */}
      <ItemDetailsModal
        item={selectedItem}
        isOpen={isDetailsModalOpen}
        onClose={handleCloseItemDetails}
        currentLane={selectedItemLane}
        allLanes={lanes}
        onMoveLane={handleMoveItemToLane}
      />

      {/* Create item modal */}
      <CreateItemModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreateItem={handleCreateItem}
        lanes={lanes}
        initialLaneId={createItemLaneId}
        availableTags={getAllTags()}
        availableAssignees={getAllAssignees()}
      />

      {/* Edit flow modal */}
      {flow && (
        <EditFlowModal
          isOpen={isEditFlowModalOpen}
          onClose={() => setIsEditFlowModalOpen(false)}
          onUpdateFlow={handleUpdateFlow}
          onDeleteFlow={handleDeleteFlow}
          onArchiveFlow={handleArchiveFlow}
          flow={flow}
        />
      )}
    </div>
  );
}
