import { Flow } from "@mug/models/flow";
import { FlowCard } from "@mug/features/flows";

interface FlowsGridProps {
  flows: Flow[];
  onArchive?: (id: string) => void;
  onDelete?: (id: string) => void;
}
export function FlowsGrid(props: FlowsGridProps) {
  const { flows, onArchive, onDelete } = props;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {flows.map((flow) => (
        <FlowCard
          key={flow.id}
          flow={flow}
          onArchive={onArchive}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
}
