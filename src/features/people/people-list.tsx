"use client";

// React and hooks
import { useState, useEffect } from "react";
import { useTranslate } from "@mug/contexts/translate";

// Models and types
import { Person, PersonType } from "@mug/models/person";
import { Pagination } from "@mug/models/core";

// Services
import { readPeople, deletePeople } from "@mug/services/person";

// Icons
import {
  ArrowUpDown,
  Building2,
  Check,
  ChevronLeft,
  ChevronRight,
  Filter,
  MoreHorizontal,
  Plus,
  Search,
  Trash2,
  User,
  Users,
} from "lucide-react";

// UI Components
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@mug/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";
import { Input } from "@mug/components/ui/input";
import { Button } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { Checkbox } from "@mug/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@mug/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";

// Feature components
import { CreatePersonModal } from "./components/create-person-modal";
import { EditPersonModal } from "./components/edit-person-modal";
import { PersonDetailsModal } from "./components/person-details-modal";

interface PeopleListProps {
  initialPeople?: Person[];
  initialPagination?: Pagination;
}

export function PeopleList({ initialPeople, initialPagination }: PeopleListProps) {
  const { t } = useTranslate();
  
  // State for people data and pagination
  const [people, setPeople] = useState<Person[]>(initialPeople || []);
  const [pagination, setPagination] = useState<Pagination | null>(initialPagination || null);
  const [loading, setLoading] = useState<boolean>(!initialPeople);
  
  // State for filters
  const [typeFilter, setTypeFilter] = useState<PersonType | "all">("all");
  const [statusFilter, setStatusFilter] = useState<"active" | "inactive" | "all">("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  
  // State for sorting
  const [sortField, setSortField] = useState<"name" | "type" | "email" | "status">("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  
  // State for selected items
  const [selectedPeople, setSelectedPeople] = useState<string[]>([]);
  
  // State for modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState<boolean>(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState<boolean>(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [selectedPerson, setSelectedPerson] = useState<Person | null>(null);
  
  // Load people data
  const loadPeople = async () => {
    setLoading(true);
    try {
      const result = await readPeople(
        currentPage,
        10,
        {
          type: typeFilter !== "all" ? typeFilter : undefined,
          status: statusFilter === "active" ? true : statusFilter === "inactive" ? false : undefined,
          search: searchQuery || undefined,
        }
      );
      
      setPeople(result.data);
      setPagination(result.pagination);
    } catch (error) {
      console.error("Error loading people:", error);
    } finally {
      setLoading(false);
    }
  };
  
  // Load people when filters or pagination change
  useEffect(() => {
    loadPeople();
  }, [currentPage, typeFilter, statusFilter, searchQuery]);
  
  // Handle search input
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page when searching
    loadPeople();
  };
  
  // Handle sort toggle
  const toggleSort = (field: "name" | "type" | "email" | "status") => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };
  
  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  // Handle selection of all items
  const handleSelectAll = () => {
    if (selectedPeople.length === people.length) {
      setSelectedPeople([]);
    } else {
      setSelectedPeople(people.map(person => person.id));
    }
  };
  
  // Handle selection of a single item
  const handleSelectPerson = (id: string) => {
    if (selectedPeople.includes(id)) {
      setSelectedPeople(selectedPeople.filter(personId => personId !== id));
    } else {
      setSelectedPeople([...selectedPeople, id]);
    }
  };
  
  // Handle opening the create modal
  const handleOpenCreateModal = () => {
    setIsCreateModalOpen(true);
  };
  
  // Handle opening the edit modal
  const handleOpenEditModal = (person: Person) => {
    setSelectedPerson(person);
    setIsEditModalOpen(true);
  };
  
  // Handle opening the details modal
  const handleOpenDetailsModal = (person: Person) => {
    setSelectedPerson(person);
    setIsDetailsModalOpen(true);
  };
  
  // Handle opening the delete dialog
  const handleOpenDeleteDialog = () => {
    setIsDeleteDialogOpen(true);
  };
  
  // Handle deleting selected people
  const handleDeleteSelected = async () => {
    try {
      await deletePeople(selectedPeople);
      setSelectedPeople([]);
      loadPeople();
    } catch (error) {
      console.error("Error deleting people:", error);
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };
  
  // Handle creating a new person
  const handlePersonCreated = () => {
    loadPeople();
    setIsCreateModalOpen(false);
  };
  
  // Handle updating a person
  const handlePersonUpdated = () => {
    loadPeople();
    setIsEditModalOpen(false);
  };
  
  // Sort people based on current sort settings
  const sortedPeople = [...people].sort((a, b) => {
    let comparison = 0;
    
    switch (sortField) {
      case "name":
        comparison = a.name.localeCompare(b.name);
        break;
      case "type":
        comparison = a.type.localeCompare(b.type);
        break;
      case "email":
        comparison = (a.email || "").localeCompare(b.email || "");
        break;
      case "status":
        comparison = Number(a.status) - Number(b.status);
        break;
    }
    
    return sortDirection === "asc" ? comparison : -comparison;
  });
  
  return (
    <div className="space-y-4">
      {/* Header with title and actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl font-bold">{t("people.title")}</h1>
        
        <div className="flex items-center gap-2">
          {selectedPeople.length > 0 && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleOpenDeleteDialog}
              className="gap-1"
            >
              <Trash2 className="h-4 w-4" />
              {t("common.delete")} ({selectedPeople.length})
            </Button>
          )}
          
          <Button
            variant="default"
            size="sm"
            onClick={handleOpenCreateModal}
            className="gap-1"
          >
            <Plus className="h-4 w-4" />
            {t("people.addPerson")}
          </Button>
        </div>
      </div>
      
      {/* Filters and search */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex flex-wrap items-center gap-2">
          <Select
            value={typeFilter}
            onValueChange={(value) => setTypeFilter(value as PersonType | "all")}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={t("people.filterByType")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("common.all")}</SelectItem>
              <SelectItem value="person">{t("people.person")}</SelectItem>
              <SelectItem value="company">{t("people.company")}</SelectItem>
            </SelectContent>
          </Select>
          
          <Select
            value={statusFilter}
            onValueChange={(value) => setStatusFilter(value as "active" | "inactive" | "all")}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={t("people.filterByStatus")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("common.all")}</SelectItem>
              <SelectItem value="active">{t("common.active")}</SelectItem>
              <SelectItem value="inactive">{t("common.inactive")}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <form onSubmit={handleSearch} className="flex w-full sm:w-auto">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder={t("common.search")}
              className="pl-8 w-full sm:w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button type="submit" variant="default" size="sm" className="ml-2">
            {t("common.search")}
          </Button>
        </form>
      </div>
      
      {/* People table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <Checkbox
                  checked={selectedPeople.length === people.length && people.length > 0}
                  indeterminate={selectedPeople.length > 0 && selectedPeople.length < people.length}
                  onCheckedChange={handleSelectAll}
                  aria-label={t("common.selectAll")}
                />
              </TableHead>
              <TableHead className="w-[250px]">
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => toggleSort("name")}
                >
                  {t("common.name")}
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => toggleSort("type")}
                >
                  {t("common.type")}
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => toggleSort("email")}
                >
                  {t("common.email")}
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                {t("common.phone")}
              </TableHead>
              <TableHead>
                <div
                  className="flex items-center cursor-pointer"
                  onClick={() => toggleSort("status")}
                >
                  {t("common.status")}
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead className="w-[80px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  {t("common.loading")}
                </TableCell>
              </TableRow>
            ) : sortedPeople.length > 0 ? (
              sortedPeople.map((person) => (
                <TableRow
                  key={person.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleOpenDetailsModal(person)}
                >
                  <TableCell
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectPerson(person.id);
                    }}
                  >
                    <Checkbox
                      checked={selectedPeople.includes(person.id)}
                      aria-label={t("common.selectItem")}
                    />
                  </TableCell>
                  <TableCell className="font-medium">{person.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className="flex items-center gap-1 w-fit">
                      {person.type === "person" ? (
                        <>
                          <User className="h-3 w-3" />
                          {t("people.person")}
                        </>
                      ) : (
                        <>
                          <Building2 className="h-3 w-3" />
                          {t("people.company")}
                        </>
                      )}
                    </Badge>
                  </TableCell>
                  <TableCell>{person.email || "—"}</TableCell>
                  <TableCell>{person.phone || "—"}</TableCell>
                  <TableCell>
                    <Badge
                      variant={person.status ? "success" : "destructive"}
                      className="w-fit"
                    >
                      {person.status ? t("common.active") : t("common.inactive")}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div
                      onClick={(e) => e.stopPropagation()}
                      className="flex justify-end"
                    >
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">{t("common.openMenu")}</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleOpenDetailsModal(person)}
                          >
                            {t("common.view")}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleOpenEditModal(person)}
                          >
                            {t("common.edit")}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => {
                              setSelectedPeople([person.id]);
                              handleOpenDeleteDialog();
                            }}
                          >
                            {t("common.delete")}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  {t("people.noPeopleFound")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Pagination */}
      {pagination && pagination.total_pages > 1 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            {t("common.showing")} {(pagination.page - 1) * pagination.records_per_page + 1}-
            {Math.min(pagination.page * pagination.records_per_page, pagination.total_records)} {t("common.of")}{" "}
            {pagination.total_records} {t("common.items")}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">{t("common.previousPage")}</span>
            </Button>
            
            <div className="flex items-center gap-1">
              {pagination.pages.map((page) => (
                <Button
                  key={page}
                  variant={page === currentPage ? "default" : "outline"}
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </Button>
              ))}
            </div>
            
            <Button
              variant="outline"
              size="icon"
              onClick={() => handlePageChange(Math.min(pagination.total_pages, currentPage + 1))}
              disabled={currentPage === pagination.total_pages}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">{t("common.nextPage")}</span>
            </Button>
          </div>
        </div>
      )}
      
      {/* Create person modal */}
      <CreatePersonModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onPersonCreated={handlePersonCreated}
      />
      
      {/* Edit person modal */}
      {selectedPerson && (
        <EditPersonModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onPersonUpdated={handlePersonUpdated}
          person={selectedPerson}
        />
      )}
      
      {/* Person details modal */}
      {selectedPerson && (
        <PersonDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={() => setIsDetailsModalOpen(false)}
          person={selectedPerson}
          onEdit={() => {
            setIsDetailsModalOpen(false);
            setIsEditModalOpen(true);
          }}
        />
      )}
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("people.deleteConfirmation", { count: selectedPeople.length })}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("people.deleteWarning")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSelected}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
