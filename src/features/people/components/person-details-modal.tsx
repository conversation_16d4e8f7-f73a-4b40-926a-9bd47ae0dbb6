"use client";

// React and hooks
import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";

// Models and types
import { Person, PersonContact, PersonDocument, PersonAddress } from "@mug/models/person";

// Date formatting
import { format } from "date-fns";
import { enUS } from "date-fns/locale";

// Icons
import {
  Building2,
  Calendar,
  Edit,
  FileText,
  Mail,
  MapPin,
  Phone,
  Tag,
  User,
  Users,
} from "lucide-react";

// UI Components - Dialog
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";

// UI Components - Other
import { Button } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@mug/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";

interface PersonDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  person: Person;
  onEdit: () => void;
}

export function PersonDetailsModal({
  isOpen,
  onClose,
  person,
  onEdit,
}: PersonDetailsModalProps) {
  const { t } = useTranslate();
  const [activeTab, setActiveTab] = useState("overview");
  
  // Helper function to get avatar initials
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] overflow-hidden">
        <DialogHeader className="px-6 py-4 border-b sticky top-0 bg-background z-10">
          <div className="flex items-center gap-4">
            <Avatar className="h-12 w-12">
              <AvatarFallback>{getInitials(person.name)}</AvatarFallback>
            </Avatar>
            <div>
              <DialogTitle className="text-xl">{person.name}</DialogTitle>
              <DialogDescription className="flex items-center gap-2">
                <Badge variant="outline" className="flex items-center gap-1">
                  {person.type === "person" ? (
                    <>
                      <User className="h-3 w-3" />
                      {t("people.person")}
                    </>
                  ) : (
                    <>
                      <Building2 className="h-3 w-3" />
                      {t("people.company")}
                    </>
                  )}
                </Badge>
                <Badge
                  variant={person.status ? "success" : "destructive"}
                  className="flex items-center gap-1"
                >
                  {person.status ? t("common.active") : t("common.inactive")}
                </Badge>
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>
        
        <div className="overflow-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="p-6">
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="overview">{t("common.overview")}</TabsTrigger>
              <TabsTrigger value="contacts">{t("people.contacts")}</TabsTrigger>
              <TabsTrigger value="documents">{t("people.documents")}</TabsTrigger>
              <TabsTrigger value="addresses">{t("people.addresses")}</TabsTrigger>
            </TabsList>
            
            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>{t("common.basicInfo")}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Name */}
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">{t("common.name")}</p>
                      <p className="font-medium">{person.name}</p>
                    </div>
                    
                    {/* Type */}
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">{t("common.type")}</p>
                      <div className="flex items-center gap-2">
                        {person.type === "person" ? (
                          <>
                            <User className="h-4 w-4 text-muted-foreground" />
                            <p className="font-medium">{t("people.person")}</p>
                          </>
                        ) : (
                          <>
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            <p className="font-medium">{t("people.company")}</p>
                          </>
                        )}
                      </div>
                    </div>
                    
                    {/* Email */}
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">{t("common.email")}</p>
                      {person.email ? (
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <p className="font-medium">{person.email}</p>
                        </div>
                      ) : (
                        <p className="text-muted-foreground">{t("common.notSet")}</p>
                      )}
                    </div>
                    
                    {/* Phone */}
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">{t("common.phone")}</p>
                      {person.phone ? (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <p className="font-medium">{person.phone}</p>
                        </div>
                      ) : (
                        <p className="text-muted-foreground">{t("common.notSet")}</p>
                      )}
                    </div>
                    
                    {/* Status */}
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">{t("common.status")}</p>
                      <Badge
                        variant={person.status ? "success" : "destructive"}
                      >
                        {person.status ? t("common.active") : t("common.inactive")}
                      </Badge>
                    </div>
                    
                    {/* Created At */}
                    {person.createdAt && (
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">{t("common.createdAt")}</p>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <p className="font-medium">
                            {format(new Date(person.createdAt), "PPP", { locale: enUS })}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              {/* Partners (for companies) */}
              {person.type === "company" && person.partners && person.partners.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>{t("people.partners")}</CardTitle>
                    <CardDescription>
                      {t("people.partnersDescription")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {person.partners.map((partner) => (
                        <div
                          key={partner.id}
                          className="flex items-center gap-4 p-3 border rounded-md"
                        >
                          <Avatar className="h-10 w-10">
                            <AvatarFallback>{getInitials(partner.name)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{partner.name}</p>
                            {partner.email && (
                              <p className="text-sm text-muted-foreground">{partner.email}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            
            {/* Contacts Tab */}
            <TabsContent value="contacts" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t("people.contacts")}</CardTitle>
                  <CardDescription>
                    {t("people.contactsDescription")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {person.contacts && person.contacts.length > 0 ? (
                    <div className="space-y-4">
                      {person.contacts.map((contact) => (
                        <div
                          key={contact.id}
                          className="flex items-start justify-between p-3 border rounded-md"
                        >
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                {contact.type}
                              </Badge>
                              <span className="font-medium">{contact.name}</span>
                            </div>
                            <p className="text-sm text-muted-foreground">{contact.value}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {t("people.noContacts")}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Documents Tab */}
            <TabsContent value="documents" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t("people.documents")}</CardTitle>
                  <CardDescription>
                    {t("people.documentsDescription")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {person.documents && person.documents.length > 0 ? (
                    <div className="space-y-4">
                      {person.documents.map((document) => (
                        <div
                          key={document.id}
                          className="flex items-start justify-between p-3 border rounded-md"
                        >
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{document.name}</span>
                            </div>
                            <p className="text-sm text-muted-foreground">{document.value}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {t("people.noDocuments")}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Addresses Tab */}
            <TabsContent value="addresses" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t("people.addresses")}</CardTitle>
                  <CardDescription>
                    {t("people.addressesDescription")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {person.addresses && person.addresses.length > 0 ? (
                    <div className="space-y-4">
                      {person.addresses.map((address) => (
                        <div
                          key={address.id}
                          className="flex items-start justify-between p-3 border rounded-md"
                        >
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{address.name}</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {address.street}, {address.number}
                              {address.complement ? `, ${address.complement}` : ""}
                              {address.neighborhood ? `, ${address.neighborhood}` : ""}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {address.city}, {address.state} - {address.postalCode}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {address.country}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {t("people.noAddresses")}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter className="px-6 py-4 border-t sticky bottom-0 bg-background z-10">
          <Button variant="outline" onClick={onClose}>
            {t("common.close")}
          </Button>
          <Button onClick={onEdit} className="gap-1">
            <Edit className="h-4 w-4" />
            {t("common.edit")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
