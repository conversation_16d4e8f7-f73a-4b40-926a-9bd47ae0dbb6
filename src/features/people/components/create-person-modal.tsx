"use client";

// React and hooks
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslate } from "@mug/contexts/translate";

// Form validation
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

// Models and types
import { PersonType, PersonContactType } from "@mug/models/person";
import { CreatePersonDTO } from "@mug/services/person";

// Services
import { createPerson } from "@mug/services/person";

// Icons
import { Building2, Check, Plus, User, X } from "lucide-react";

// UI Components - Dialog
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";

// UI Components - Form
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@mug/components/ui/form";
import { Input } from "@mug/components/ui/input";
import { But<PERSON> } from "@mug/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@mug/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@mug/components/ui/radio-group";
import { Switch } from "@mug/components/ui/switch";
import { Badge } from "@mug/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";

interface CreatePersonModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPersonCreated: () => void;
}

// Validation schema for the form
const formSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  type: z.enum(["person", "company"], {
    required_error: "Type is required",
  }),
  email: z.string().email({ message: "Invalid email" }).optional().or(z.literal("")),
  phone: z.string().optional().or(z.literal("")),
  status: z.boolean().default(true),
  // We'll handle documents, contacts, addresses, and partners separately
});

// Type for form values
type FormValues = z.infer<typeof formSchema>;

export function CreatePersonModal({
  isOpen,
  onClose,
  onPersonCreated,
}: CreatePersonModalProps) {
  const { t } = useTranslate();
  const [activeTab, setActiveTab] = useState("basic");

  // State for contacts, documents, addresses, and partners
  const [contacts, setContacts] = useState<Array<{ type: PersonContactType; name: string; value: string }>>([]);
  const [documents, setDocuments] = useState<Array<{ name: string; value: string }>>([]);
  const [addresses, setAddresses] = useState<Array<{
    name: string;
    street: string;
    number?: string;
    complement?: string;
    neighborhood?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  }>>([]);
  const [partners, setPartners] = useState<string[]>([]);

  // State for new contact form
  const [newContact, setNewContact] = useState<{ type: PersonContactType; name: string; value: string }>({
    type: "email",
    name: "",
    value: "",
  });

  // State for new document form
  const [newDocument, setNewDocument] = useState<{ name: string; value: string }>({
    name: "",
    value: "",
  });

  // State for new address form
  const [newAddress, setNewAddress] = useState<{
    name: string;
    street: string;
    number?: string;
    complement?: string;
    neighborhood?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  }>({
    name: "",
    street: "",
    number: "",
    complement: "",
    neighborhood: "",
    city: "",
    state: "",
    postalCode: "",
    country: "Brasil",
  });

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      type: "person",
      email: "",
      phone: "",
      status: true,
    },
  });

  // Reset form when modal is closed
  useEffect(() => {
    if (!isOpen) {
      form.reset();
      setContacts([]);
      setDocuments([]);
      setAddresses([]);
      setPartners([]);
      setActiveTab("basic");
    }
  }, [isOpen, form]);

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      // Prepare data for API
      const personData: CreatePersonDTO = {
        ...values,
        documents: documents.length > 0 ? documents : undefined,
        contacts: contacts.length > 0 ? contacts : undefined,
        addresses: addresses.length > 0 ? addresses : undefined,
        partners: partners.length > 0 ? partners : undefined,
      };

      // Create person
      await createPerson(personData);

      // Notify parent component
      onPersonCreated();
    } catch (error) {
      console.error("Error creating person:", error);
    }
  };

  // Handle adding a new contact
  const handleAddContact = () => {
    if (newContact.name && newContact.value) {
      setContacts([...contacts, { ...newContact }]);
      setNewContact({
        type: "email",
        name: "",
        value: "",
      });
    }
  };

  // Handle removing a contact
  const handleRemoveContact = (index: number) => {
    setContacts(contacts.filter((_, i) => i !== index));
  };

  // Handle adding a new document
  const handleAddDocument = () => {
    if (newDocument.name && newDocument.value) {
      setDocuments([...documents, { ...newDocument }]);
      setNewDocument({
        name: "",
        value: "",
      });
    }
  };

  // Handle removing a document
  const handleRemoveDocument = (index: number) => {
    setDocuments(documents.filter((_, i) => i !== index));
  };

  // Handle adding a new address
  const handleAddAddress = () => {
    if (newAddress.name && newAddress.street && newAddress.city && newAddress.state && newAddress.postalCode) {
      setAddresses([...addresses, { ...newAddress }]);
      setNewAddress({
        name: "",
        street: "",
        number: "",
        complement: "",
        neighborhood: "",
        city: "",
        state: "",
        postalCode: "",
        country: "Brasil",
      });
    }
  };

  // Handle removing an address
  const handleRemoveAddress = (index: number) => {
    setAddresses(addresses.filter((_, i) => i !== index));
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] overflow-hidden">
        <DialogHeader className="px-6 py-4 border-b sticky top-0 bg-background z-10">
          <DialogTitle>{t("people.createPerson")}</DialogTitle>
          <DialogDescription>
            {t("people.createPersonDescription")}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <div className="flex-1 flex flex-col overflow-auto py-4">
            <form
              id="create-person-form"
              onSubmit={form.handleSubmit(onSubmit)}
              className="grid grid-cols-8 gap-4 px-6 flex-1"
            >
              <Tabs value={activeTab} onValueChange={setActiveTab} className="col-span-8">
                <TabsList className="grid grid-cols-4 mb-6">
                  <TabsTrigger value="basic">{t("common.basicInfo")}</TabsTrigger>
                  <TabsTrigger value="contacts">{t("people.contacts")}</TabsTrigger>
                  <TabsTrigger value="documents">{t("people.documents")}</TabsTrigger>
                  <TabsTrigger value="addresses">{t("people.addresses")}</TabsTrigger>
                </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-4">
                {/* Person Type */}
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem className="col-span-8 space-y-3">
                      <div className="flex items-center h-6">
                        <FormLabel className="gap-0">
                          {t("people.personType")}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                      </div>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-1"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="person" id="person" />
                            <label
                              htmlFor="person"
                              className="flex items-center gap-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              <User className="h-4 w-4" />
                              {t("people.person")}
                            </label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="company" id="company" />
                            <label
                              htmlFor="company"
                              className="flex items-center gap-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              <Building2 className="h-4 w-4" />
                              {t("people.company")}
                            </label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Name */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="col-span-8">
                      <div className="flex items-center h-6">
                        <FormLabel className="gap-0">
                          {t("common.name")}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                      </div>
                      <FormControl>
                        <Input placeholder={t("people.enterName")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="col-span-4">
                      <div className="flex items-center h-6">
                        <FormLabel>{t("common.email")}</FormLabel>
                      </div>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder={t("people.enterEmail")}
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Phone */}
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem className="col-span-4">
                      <div className="flex items-center h-6">
                        <FormLabel>{t("common.phone")}</FormLabel>
                      </div>
                      <FormControl>
                        <Input
                          placeholder={t("people.enterPhone")}
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem className="col-span-8 flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          {t("common.status")}
                        </FormLabel>
                        <FormDescription>
                          {t("people.statusDescription")}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </TabsContent>

              {/* Contacts Tab */}
              <TabsContent value="contacts" className="space-y-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">{t("people.contacts")}</h3>

                  {/* List of existing contacts */}
                  {contacts.length > 0 ? (
                    <div className="space-y-2">
                      {contacts.map((contact, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border rounded-md"
                        >
                          <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                {contact.type}
                              </Badge>
                              <span className="font-medium">{contact.name}</span>
                            </div>
                            <span className="text-sm text-muted-foreground">
                              {contact.value}
                            </span>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveContact(index)}
                          >
                            <X className="h-4 w-4" />
                            <span className="sr-only">{t("common.remove")}</span>
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {t("people.noContacts")}
                    </div>
                  )}

                  {/* Form to add a new contact */}
                  <div className="space-y-4 border rounded-md p-4">
                    <h4 className="font-medium">{t("people.addContact")}</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.contactType")}
                        </label>
                        <Select
                          value={newContact.type}
                          onValueChange={(value) =>
                            setNewContact({ ...newContact, type: value as PersonContactType })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder={t("people.selectContactType")} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="email">{t("people.email")}</SelectItem>
                            <SelectItem value="phone">{t("people.phone")}</SelectItem>
                            <SelectItem value="whatsapp">{t("people.whatsapp")}</SelectItem>
                            <SelectItem value="telegram">{t("people.telegram")}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.contactName")}
                        </label>
                        <Input
                          placeholder={t("people.enterContactName")}
                          value={newContact.name}
                          onChange={(e) =>
                            setNewContact({ ...newContact, name: e.target.value })
                          }
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        {t("people.contactValue")}
                      </label>
                      <Input
                        placeholder={t("people.enterContactValue")}
                        value={newContact.value}
                        onChange={(e) =>
                          setNewContact({ ...newContact, value: e.target.value })
                        }
                      />
                    </div>

                    <Button
                      type="button"
                      onClick={handleAddContact}
                      disabled={!newContact.name || !newContact.value}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t("people.addContact")}
                    </Button>
                  </div>
                </div>
              </TabsContent>

              {/* Documents Tab */}
              <TabsContent value="documents" className="space-y-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">{t("people.documents")}</h3>

                  {/* List of existing documents */}
                  {documents.length > 0 ? (
                    <div className="space-y-2">
                      {documents.map((document, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border rounded-md"
                        >
                          <div className="flex flex-col gap-1">
                            <span className="font-medium">{document.name}</span>
                            <span className="text-sm text-muted-foreground">
                              {document.value}
                            </span>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveDocument(index)}
                          >
                            <X className="h-4 w-4" />
                            <span className="sr-only">{t("common.remove")}</span>
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {t("people.noDocuments")}
                    </div>
                  )}

                  {/* Form to add a new document */}
                  <div className="space-y-4 border rounded-md p-4">
                    <h4 className="font-medium">{t("people.addDocument")}</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.documentName")}
                        </label>
                        <Input
                          placeholder={t("people.enterDocumentName")}
                          value={newDocument.name}
                          onChange={(e) =>
                            setNewDocument({ ...newDocument, name: e.target.value })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.documentValue")}
                        </label>
                        <Input
                          placeholder={t("people.enterDocumentValue")}
                          value={newDocument.value}
                          onChange={(e) =>
                            setNewDocument({ ...newDocument, value: e.target.value })
                          }
                        />
                      </div>
                    </div>

                    <Button
                      type="button"
                      onClick={handleAddDocument}
                      disabled={!newDocument.name || !newDocument.value}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t("people.addDocument")}
                    </Button>
                  </div>
                </div>
              </TabsContent>

              {/* Addresses Tab */}
              <TabsContent value="addresses" className="space-y-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">{t("people.addresses")}</h3>

                  {/* List of existing addresses */}
                  {addresses.length > 0 ? (
                    <div className="space-y-2">
                      {addresses.map((address, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border rounded-md"
                        >
                          <div className="flex flex-col gap-1">
                            <span className="font-medium">{address.name}</span>
                            <span className="text-sm text-muted-foreground">
                              {address.street}, {address.number}
                              {address.complement ? `, ${address.complement}` : ""}
                              {address.neighborhood ? `, ${address.neighborhood}` : ""}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              {address.city}, {address.state} - {address.postalCode}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              {address.country}
                            </span>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveAddress(index)}
                          >
                            <X className="h-4 w-4" />
                            <span className="sr-only">{t("common.remove")}</span>
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {t("people.noAddresses")}
                    </div>
                  )}

                  {/* Form to add a new address */}
                  <div className="space-y-4 border rounded-md p-4">
                    <h4 className="font-medium">{t("people.addAddress")}</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.addressName")}
                        </label>
                        <Input
                          placeholder={t("people.enterAddressName")}
                          value={newAddress.name}
                          onChange={(e) =>
                            setNewAddress({ ...newAddress, name: e.target.value })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.street")}
                        </label>
                        <Input
                          placeholder={t("people.enterStreet")}
                          value={newAddress.street}
                          onChange={(e) =>
                            setNewAddress({ ...newAddress, street: e.target.value })
                          }
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.number")}
                        </label>
                        <Input
                          placeholder={t("people.enterNumber")}
                          value={newAddress.number || ""}
                          onChange={(e) =>
                            setNewAddress({ ...newAddress, number: e.target.value })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.complement")}
                        </label>
                        <Input
                          placeholder={t("people.enterComplement")}
                          value={newAddress.complement || ""}
                          onChange={(e) =>
                            setNewAddress({ ...newAddress, complement: e.target.value })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.neighborhood")}
                        </label>
                        <Input
                          placeholder={t("people.enterNeighborhood")}
                          value={newAddress.neighborhood || ""}
                          onChange={(e) =>
                            setNewAddress({ ...newAddress, neighborhood: e.target.value })
                          }
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.city")}
                        </label>
                        <Input
                          placeholder={t("people.enterCity")}
                          value={newAddress.city}
                          onChange={(e) =>
                            setNewAddress({ ...newAddress, city: e.target.value })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.state")}
                        </label>
                        <Input
                          placeholder={t("people.enterState")}
                          value={newAddress.state}
                          onChange={(e) =>
                            setNewAddress({ ...newAddress, state: e.target.value })
                          }
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {t("people.postalCode")}
                        </label>
                        <Input
                          placeholder={t("people.enterPostalCode")}
                          value={newAddress.postalCode}
                          onChange={(e) =>
                            setNewAddress({ ...newAddress, postalCode: e.target.value })
                          }
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        {t("people.country")}
                      </label>
                      <Input
                        placeholder={t("people.enterCountry")}
                        value={newAddress.country}
                        onChange={(e) =>
                          setNewAddress({ ...newAddress, country: e.target.value })
                        }
                      />
                    </div>

                    <Button
                      type="button"
                      onClick={handleAddAddress}
                      disabled={
                        !newAddress.name ||
                        !newAddress.street ||
                        !newAddress.city ||
                        !newAddress.state ||
                        !newAddress.postalCode
                      }
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t("people.addAddress")}
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
            </form>
          </div>
        </Form>

        <DialogFooter className="px-6 py-4 border-t sticky bottom-0 bg-background z-10">
          <Button variant="outline" onClick={onClose}>
            {t("common.cancel")}
          </Button>
          <Button type="submit" form="create-person-form">
            <Check className="h-4 w-4 mr-2" />
            {t("common.create")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
