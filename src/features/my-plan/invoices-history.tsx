"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Invoice } from "@mug/models/plan";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Download, FileText } from "lucide-react";
import { cn } from "@mug/lib/utils/class-name-builder";

interface InvoicesHistoryProps {
  invoices: Invoice[];
}

export function InvoicesHistory({ invoices }: InvoicesHistoryProps) {
  const { t } = useTranslate();
  
  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };
  
  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(price);
  };
  
  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    const statusColors: Record<string, string> = {
      paid: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
      unpaid: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
      void: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
      draft: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100",
    };
    
    return statusColors[status] || statusColors.draft;
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{t("myPlan.invoicesHistory")}</CardTitle>
        <CardDescription>{t("myPlan.invoicesHistoryDescription")}</CardDescription>
      </CardHeader>
      <CardContent>
        {invoices.length > 0 ? (
          <div className="space-y-4">
            {invoices.map(invoice => (
              <div 
                key={invoice.id} 
                className="flex items-center justify-between p-4 border rounded-md"
              >
                <div className="flex items-center space-x-4">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">
                      {invoice.items[0]?.description || t("myPlan.invoice")}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {t("myPlan.issuedOn")} {formatDate(invoice.issueDate)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <Badge className={cn(getStatusBadgeColor(invoice.status))}>
                    {t(`myPlan.invoiceStatuses.${invoice.status}`)}
                  </Badge>
                  <p className="font-medium">{formatPrice(invoice.amount)}</p>
                  {invoice.pdf && (
                    <Button variant="outline" size="icon" asChild>
                      <a href={invoice.pdf} target="_blank" rel="noopener noreferrer">
                        <Download className="h-4 w-4" />
                        <span className="sr-only">{t("myPlan.downloadInvoice")}</span>
                      </a>
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center justify-center p-8 border rounded-lg bg-muted/10">
            <p className="text-muted-foreground">{t("myPlan.noInvoices")}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
