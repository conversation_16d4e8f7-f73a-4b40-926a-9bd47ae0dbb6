"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { Plan } from "@mug/models/plan";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { But<PERSON> } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { Check, X, ArrowRight, CheckCircle2 } from "lucide-react";
import { cn } from "@mug/lib/utils/class-name-builder";
import { changePlan } from "@mug/services/plan";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@mug/components/ui/tabs";

interface PlanComparisonProps {
  plans: Plan[];
  currentPlanId: string;
  onPlanChanged?: () => void;
}

export function PlanComparison({ plans, currentPlanId, onPlanChanged }: PlanComparisonProps) {
  const { t } = useTranslate();
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly");
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  
  // Filter plans by billing cycle and exclude enterprise plans
  const filteredPlans = plans.filter(
    plan => !plan.isEnterprise && (plan.billingCycle === billingCycle || plan.billingCycle === "custom")
  );
  
  // Format price
  const formatPrice = (price: number) => {
    if (price === 0) {
      return t("myPlan.free");
    }
    
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(price);
  };
  
  // Calculate yearly price (20% discount)
  const calculateYearlyPrice = (monthlyPrice: number) => {
    if (monthlyPrice === 0) {
      return 0;
    }
    
    const yearlyPrice = monthlyPrice * 12 * 0.8; // 20% discount
    return yearlyPrice;
  };
  
  // Handle plan selection
  const handleSelectPlan = (planId: string) => {
    if (planId === currentPlanId) {
      return; // Already on this plan
    }
    
    setSelectedPlanId(planId);
    setIsConfirmDialogOpen(true);
  };
  
  // Handle plan change confirmation
  const handleConfirmPlanChange = async () => {
    if (selectedPlanId) {
      try {
        await changePlan(selectedPlanId);
        setIsConfirmDialogOpen(false);
        if (onPlanChanged) {
          onPlanChanged();
        }
      } catch (error) {
        console.error("Error changing plan:", error);
      }
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-center">
        <Tabs
          value={billingCycle}
          onValueChange={(value) => setBillingCycle(value as "monthly" | "yearly")}
          className="w-[400px]"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="monthly">{t("myPlan.billingCycles.monthly")}</TabsTrigger>
            <TabsTrigger value="yearly">
              {t("myPlan.billingCycles.yearly")}
              <Badge variant="secondary" className="ml-2">
                {t("myPlan.savePercent", { percent: 20 })}
              </Badge>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {filteredPlans.map(plan => (
          <Card 
            key={plan.id} 
            className={cn(
              "flex flex-col",
              plan.isPopular && "border-primary shadow-md"
            )}
          >
            {plan.isPopular && (
              <Badge className="absolute top-0 right-0 translate-x-1/3 -translate-y-1/3 bg-primary">
                {t("myPlan.popular")}
              </Badge>
            )}
            <CardHeader>
              <CardTitle>{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
              <div className="mt-4">
                <div className="text-3xl font-bold">
                  {billingCycle === "monthly" 
                    ? formatPrice(plan.price) 
                    : formatPrice(calculateYearlyPrice(plan.price))}
                  {plan.price > 0 && (
                    <span className="text-sm font-normal text-muted-foreground ml-1">
                      / {billingCycle === "monthly" 
                          ? t("myPlan.month") 
                          : t("myPlan.year")}
                    </span>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-2">
                {plan.features.map(feature => (
                  <li key={feature.id} className="flex items-start">
                    {feature.included ? (
                      <Check className="h-5 w-5 mr-2 text-green-500 shrink-0" />
                    ) : (
                      <X className="h-5 w-5 mr-2 text-muted-foreground shrink-0" />
                    )}
                    <span className={cn(!feature.included && "text-muted-foreground")}>
                      {feature.name}
                    </span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              {plan.id === currentPlanId ? (
                <Button className="w-full" disabled>
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  {t("myPlan.currentPlan")}
                </Button>
              ) : (
                <Button 
                  className="w-full" 
                  variant={plan.isPopular ? "default" : "outline"}
                  onClick={() => handleSelectPlan(plan.id)}
                >
                  {t("myPlan.selectPlan")}
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
        
        {/* Enterprise Plan */}
        <Card className="flex flex-col bg-muted/50">
          <CardHeader>
            <CardTitle>{t("myPlan.enterprise")}</CardTitle>
            <CardDescription>{t("myPlan.enterpriseDescription")}</CardDescription>
            <div className="mt-4">
              <div className="text-3xl font-bold">
                {t("myPlan.contactUs")}
              </div>
            </div>
          </CardHeader>
          <CardContent className="flex-1">
            <ul className="space-y-2">
              <li className="flex items-start">
                <Check className="h-5 w-5 mr-2 text-green-500 shrink-0" />
                <span>{t("myPlan.enterpriseFeatures.users")}</span>
              </li>
              <li className="flex items-start">
                <Check className="h-5 w-5 mr-2 text-green-500 shrink-0" />
                <span>{t("myPlan.enterpriseFeatures.storage")}</span>
              </li>
              <li className="flex items-start">
                <Check className="h-5 w-5 mr-2 text-green-500 shrink-0" />
                <span>{t("myPlan.enterpriseFeatures.support")}</span>
              </li>
              <li className="flex items-start">
                <Check className="h-5 w-5 mr-2 text-green-500 shrink-0" />
                <span>{t("myPlan.enterpriseFeatures.sla")}</span>
              </li>
              <li className="flex items-start">
                <Check className="h-5 w-5 mr-2 text-green-500 shrink-0" />
                <span>{t("myPlan.enterpriseFeatures.integrations")}</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button className="w-full" variant="outline">
              {t("myPlan.contactSales")} <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>
      
      {/* Confirm Plan Change Dialog */}
      <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("myPlan.confirmPlanChange")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("myPlan.confirmPlanChangeDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmPlanChange}>
              {t("myPlan.confirmChange")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
