"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { SubscriptionUser, UserRole } from "@mug/models/plan";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { Avatar, AvatarFallback } from "@mug/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@mug/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";
import { 
  MoreHorizontal, 
  UserPlus, 
  Mail, 
  UserCog, 
  UserMinus,
  Check
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@mug/lib/utils/class-name-builder";
import { inviteUser, updateUserRole, removeUser, InviteUserDTO } from "@mug/services/plan";

interface UsersManagementProps {
  users: SubscriptionUser[];
  maxUsers: number;
  onUserAdded?: () => void;
  onUserUpdated?: () => void;
  onUserRemoved?: () => void;
}

export function UsersManagement({ 
  users, 
  maxUsers,
  onUserAdded,
  onUserUpdated,
  onUserRemoved
}: UsersManagementProps) {
  const { t } = useTranslate();
  
  // State for invite user dialog
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [inviteFormData, setInviteFormData] = useState<InviteUserDTO>({
    name: "",
    email: "",
    role: "member"
  });
  
  // State for change role dialog
  const [isChangeRoleDialogOpen, setIsChangeRoleDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<SubscriptionUser | null>(null);
  const [selectedRole, setSelectedRole] = useState<UserRole>("member");
  
  // State for remove user dialog
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [userToRemove, setUserToRemove] = useState<SubscriptionUser | null>(null);
  
  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    
    try {
      const date = new Date(dateString);
      return format(date, "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };
  
  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    const statusColors: Record<string, string> = {
      active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
      invited: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100",
      suspended: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
    };
    
    return statusColors[status] || statusColors.active;
  };
  
  // Get role badge color
  const getRoleBadgeColor = (role: string) => {
    const roleColors: Record<string, string> = {
      owner: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100",
      admin: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-100",
      manager: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100",
      member: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
      viewer: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100",
    };
    
    return roleColors[role] || roleColors.member;
  };
  
  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(part => part[0])
      .slice(0, 2)
      .join("")
      .toUpperCase();
  };
  
  // Handle invite user
  const handleInviteUser = async () => {
    try {
      await inviteUser(inviteFormData);
      setIsInviteDialogOpen(false);
      setInviteFormData({
        name: "",
        email: "",
        role: "member"
      });
      if (onUserAdded) {
        onUserAdded();
      }
    } catch (error) {
      console.error("Error inviting user:", error);
    }
  };
  
  // Handle change role
  const handleChangeRole = async () => {
    if (selectedUser && selectedRole) {
      try {
        await updateUserRole(selectedUser.userId, selectedRole);
        setIsChangeRoleDialogOpen(false);
        if (onUserUpdated) {
          onUserUpdated();
        }
      } catch (error) {
        console.error("Error updating user role:", error);
      }
    }
  };
  
  // Handle remove user
  const handleRemoveUser = async () => {
    if (userToRemove) {
      try {
        await removeUser(userToRemove.userId);
        setIsRemoveDialogOpen(false);
        if (onUserRemoved) {
          onUserRemoved();
        }
      } catch (error) {
        console.error("Error removing user:", error);
      }
    }
  };
  
  // Open change role dialog
  const openChangeRoleDialog = (user: SubscriptionUser) => {
    setSelectedUser(user);
    setSelectedRole(user.role);
    setIsChangeRoleDialogOpen(true);
  };
  
  // Open remove user dialog
  const openRemoveUserDialog = (user: SubscriptionUser) => {
    setUserToRemove(user);
    setIsRemoveDialogOpen(true);
  };
  
  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{t("myPlan.usersManagement")}</CardTitle>
          <CardDescription>
            {t("myPlan.usersCount", { count: users.length, max: maxUsers })}
          </CardDescription>
        </div>
        <Button 
          onClick={() => setIsInviteDialogOpen(true)}
          disabled={users.length >= maxUsers}
        >
          <UserPlus className="h-4 w-4 mr-2" />
          {t("myPlan.inviteUser")}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {users.map(user => (
            <div 
              key={user.id} 
              className="flex items-center justify-between p-4 border rounded-md"
            >
              <div className="flex items-center space-x-4">
                <Avatar>
                  <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{user.name}</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={cn(getStatusBadgeColor(user.status))}>
                  {t(`myPlan.userStatuses.${user.status}`)}
                </Badge>
                <Badge className={cn(getRoleBadgeColor(user.role))}>
                  {t(`myPlan.roles.${user.role}`)}
                </Badge>
                {user.role !== "owner" && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{t("common.openMenu")}</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {user.status === "invited" && (
                        <DropdownMenuItem>
                          <Mail className="h-4 w-4 mr-2" />
                          {t("myPlan.resendInvite")}
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => openChangeRoleDialog(user)}>
                        <UserCog className="h-4 w-4 mr-2" />
                        {t("myPlan.changeRole")}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => openRemoveUserDialog(user)}
                        className="text-destructive focus:text-destructive"
                      >
                        <UserMinus className="h-4 w-4 mr-2" />
                        {t("myPlan.removeUser")}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      
      {/* Invite User Dialog */}
      <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("myPlan.inviteUser")}</DialogTitle>
            <DialogDescription>
              {t("myPlan.inviteUserDescription")}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t("myPlan.userName")}</Label>
              <Input
                id="name"
                value={inviteFormData.name}
                onChange={(e) => setInviteFormData({ ...inviteFormData, name: e.target.value })}
                placeholder={t("myPlan.userNamePlaceholder")}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">{t("myPlan.userEmail")}</Label>
              <Input
                id="email"
                type="email"
                value={inviteFormData.email}
                onChange={(e) => setInviteFormData({ ...inviteFormData, email: e.target.value })}
                placeholder={t("myPlan.userEmailPlaceholder")}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="role">{t("myPlan.userRole")}</Label>
              <Select
                value={inviteFormData.role}
                onValueChange={(value) => setInviteFormData({ ...inviteFormData, role: value as UserRole })}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("myPlan.selectRole")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">{t("myPlan.roles.admin")}</SelectItem>
                  <SelectItem value="manager">{t("myPlan.roles.manager")}</SelectItem>
                  <SelectItem value="member">{t("myPlan.roles.member")}</SelectItem>
                  <SelectItem value="viewer">{t("myPlan.roles.viewer")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
              {t("common.cancel")}
            </Button>
            <Button onClick={handleInviteUser}>
              <Check className="h-4 w-4 mr-2" />
              {t("myPlan.sendInvite")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Change Role Dialog */}
      <Dialog open={isChangeRoleDialogOpen} onOpenChange={setIsChangeRoleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("myPlan.changeRole")}</DialogTitle>
            <DialogDescription>
              {t("myPlan.changeRoleDescription", { name: selectedUser?.name })}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="role">{t("myPlan.userRole")}</Label>
              <Select
                value={selectedRole}
                onValueChange={(value) => setSelectedRole(value as UserRole)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("myPlan.selectRole")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">{t("myPlan.roles.admin")}</SelectItem>
                  <SelectItem value="manager">{t("myPlan.roles.manager")}</SelectItem>
                  <SelectItem value="member">{t("myPlan.roles.member")}</SelectItem>
                  <SelectItem value="viewer">{t("myPlan.roles.viewer")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsChangeRoleDialogOpen(false)}>
              {t("common.cancel")}
            </Button>
            <Button onClick={handleChangeRole}>
              {t("common.save")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Remove User Dialog */}
      <AlertDialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("myPlan.removeUserConfirmation")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("myPlan.removeUserWarning", { name: userToRemove?.name })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRemoveUser}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("myPlan.removeUser")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
