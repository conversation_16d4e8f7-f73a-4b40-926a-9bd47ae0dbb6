"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Subscription } from "@mug/models/plan";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { ArrowRight, Check, CreditCard, Calendar } from "lucide-react";
import { cn } from "@mug/lib/utils/class-name-builder";
import Link from "next/link";

interface PlanDetailsProps {
  subscription: Subscription;
}

export function PlanDetails({ subscription }: PlanDetailsProps) {
  const { t } = useTranslate();
  
  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    
    try {
      const date = new Date(dateString);
      return format(date, "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };
  
  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    const statusColors: Record<string, string> = {
      active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
      trialing: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100",
      past_due: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
      canceled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
      unpaid: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
      expired: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100",
    };
    
    return statusColors[status] || statusColors.active;
  };
  
  // Format price
  const formatPrice = (price: number) => {
    if (price === 0) {
      return t("myPlan.free");
    }
    
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(price);
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl">{subscription.plan.name}</CardTitle>
            <CardDescription>{subscription.plan.description}</CardDescription>
          </div>
          <Badge className={cn(getStatusBadgeColor(subscription.status))}>
            {t(`myPlan.statuses.${subscription.status}`)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Price and billing cycle */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{t("myPlan.price")}</p>
            <p className="text-2xl font-bold">
              {formatPrice(subscription.plan.price)}
              {subscription.plan.price > 0 && (
                <span className="text-sm font-normal text-muted-foreground ml-1">
                  / {t(`myPlan.billingCycles.${subscription.plan.billingCycle}`)}
                </span>
              )}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-muted-foreground">{t("myPlan.nextBilling")}</p>
            <p className="flex items-center">
              <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
              {formatDate(subscription.endDate)}
            </p>
          </div>
        </div>
        
        {/* Payment method */}
        {subscription.paymentMethod && (
          <div className="border rounded-md p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2 text-muted-foreground" />
                <div>
                  <p className="font-medium">
                    {t(`myPlan.paymentMethods.${subscription.paymentMethod.type}`)}
                    {subscription.paymentMethod.lastFour && (
                      <span className="ml-1">•••• {subscription.paymentMethod.lastFour}</span>
                    )}
                  </p>
                  {subscription.paymentMethod.expiryDate && (
                    <p className="text-sm text-muted-foreground">
                      {t("myPlan.expires")} {subscription.paymentMethod.expiryDate}
                    </p>
                  )}
                </div>
              </div>
              <Button variant="outline" size="sm">
                {t("myPlan.updatePaymentMethod")}
              </Button>
            </div>
          </div>
        )}
        
        {/* Plan features */}
        <div className="space-y-4">
          <h3 className="font-medium">{t("myPlan.includedFeatures")}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {subscription.plan.features
              .filter(feature => feature.included)
              .map(feature => (
                <div key={feature.id} className="flex items-center">
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                  <span>{feature.name}</span>
                </div>
              ))}
          </div>
        </div>
        
        {/* Auto-renew */}
        <div className="flex items-center justify-between border-t pt-4">
          <div>
            <p className="font-medium">{t("myPlan.autoRenew")}</p>
            <p className="text-sm text-muted-foreground">
              {subscription.autoRenew
                ? t("myPlan.autoRenewEnabled")
                : t("myPlan.autoRenewDisabled")}
            </p>
          </div>
          <Button variant="outline" size="sm">
            {subscription.autoRenew
              ? t("myPlan.disableAutoRenew")
              : t("myPlan.enableAutoRenew")}
          </Button>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6">
        <Button variant="outline">
          {t("myPlan.cancelSubscription")}
        </Button>
        <Button asChild>
          <Link href="/my-plan/change">
            {t("myPlan.changePlan")} <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
