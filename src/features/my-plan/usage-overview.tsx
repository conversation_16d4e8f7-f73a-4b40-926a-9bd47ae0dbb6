"use client";

import { useTranslate } from "@mug/contexts/translate";
import { UsageData, UsageItem } from "@mug/models/plan";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Progress } from "@mug/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { cn } from "@mug/lib/utils/class-name-builder";

interface UsageOverviewProps {
  usage: UsageData;
}

export function UsageOverview({ usage }: UsageOverviewProps) {
  const { t } = useTranslate();
  
  // Get progress color based on warning level
  const getProgressColor = (warningLevel: string) => {
    const warningColors: Record<string, string> = {
      safe: "bg-green-500",
      warning: "bg-yellow-500",
      critical: "bg-red-500",
    };
    
    return warningColors[warningLevel] || warningColors.safe;
  };
  
  // Format usage value
  const formatUsageValue = (item: UsageItem, type: string) => {
    if (type === "storage") {
      return `${item.used} GB / ${item.limit === 999999 ? "∞" : `${item.limit} GB`}`;
    }
    
    return `${item.used} / ${item.limit === 999999 ? "∞" : item.limit}`;
  };
  
  // Check if any resource is at critical level
  const hasCriticalResources = Object.values(usage).some(
    item => item.warningLevel === "critical"
  );
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{t("myPlan.usageOverview")}</CardTitle>
        <CardDescription>{t("myPlan.usageOverviewDescription")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Critical resources warning */}
        {hasCriticalResources && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>{t("myPlan.criticalResourcesWarning")}</AlertTitle>
            <AlertDescription>
              {t("myPlan.criticalResourcesDescription")}
            </AlertDescription>
          </Alert>
        )}
        
        {/* Usage items */}
        <div className="space-y-4">
          {/* Users */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">{t("myPlan.resources.users")}</p>
              <p className="text-sm text-muted-foreground">
                {formatUsageValue(usage.users, "users")}
              </p>
            </div>
            <Progress
              value={usage.users.percentage}
              className="h-2"
              indicatorClassName={cn(getProgressColor(usage.users.warningLevel))}
            />
          </div>
          
          {/* Flows */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">{t("myPlan.resources.flows")}</p>
              <p className="text-sm text-muted-foreground">
                {formatUsageValue(usage.flows, "flows")}
              </p>
            </div>
            <Progress
              value={usage.flows.percentage}
              className="h-2"
              indicatorClassName={cn(getProgressColor(usage.flows.warningLevel))}
            />
          </div>
          
          {/* Records */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">{t("myPlan.resources.records")}</p>
              <p className="text-sm text-muted-foreground">
                {formatUsageValue(usage.records, "records")}
              </p>
            </div>
            <Progress
              value={usage.records.percentage}
              className="h-2"
              indicatorClassName={cn(getProgressColor(usage.records.warningLevel))}
            />
          </div>
          
          {/* Storage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">{t("myPlan.resources.storage")}</p>
              <p className="text-sm text-muted-foreground">
                {formatUsageValue(usage.storage, "storage")}
              </p>
            </div>
            <Progress
              value={usage.storage.percentage}
              className="h-2"
              indicatorClassName={cn(getProgressColor(usage.storage.warningLevel))}
            />
          </div>
          
          {/* Landing Pages */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">{t("myPlan.resources.landingPages")}</p>
              <p className="text-sm text-muted-foreground">
                {formatUsageValue(usage.landingPages, "landingPages")}
              </p>
            </div>
            <Progress
              value={usage.landingPages.percentage}
              className="h-2"
              indicatorClassName={cn(getProgressColor(usage.landingPages.warningLevel))}
            />
          </div>
          
          {/* Forms */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">{t("myPlan.resources.forms")}</p>
              <p className="text-sm text-muted-foreground">
                {formatUsageValue(usage.forms, "forms")}
              </p>
            </div>
            <Progress
              value={usage.forms.percentage}
              className="h-2"
              indicatorClassName={cn(getProgressColor(usage.forms.warningLevel))}
            />
          </div>
          
          {/* Automations */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">{t("myPlan.resources.automations")}</p>
              <p className="text-sm text-muted-foreground">
                {formatUsageValue(usage.automations, "automations")}
              </p>
            </div>
            <Progress
              value={usage.automations.percentage}
              className="h-2"
              indicatorClassName={cn(getProgressColor(usage.automations.warningLevel))}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
