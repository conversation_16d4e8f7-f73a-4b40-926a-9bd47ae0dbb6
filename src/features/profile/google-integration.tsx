"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { User } from "@mug/models/user";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { But<PERSON> } from "@mug/components/ui/button";
import { connectGoogle, disconnectGoogle } from "@mug/services/user";
import { Loader2, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";

interface GoogleIntegrationProps {
  user: User;
  onUpdate?: (user: User) => void;
}

export function GoogleIntegration({ user, onUpdate }: GoogleIntegrationProps) {
  const { t } = useTranslate();
  const [isLoading, setIsLoading] = useState(false);
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  
  // Handle Google connection
  const handleConnectGoogle = async () => {
    setIsLoading(true);
    
    try {
      // In a real implementation, this would open a popup for Google OAuth
      // For now, we'll simulate it with a timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock token
      const token = "mock-google-token";
      
      await connectGoogle({ token });
      
      // Update user object
      if (onUpdate) {
        onUpdate({
          ...user,
          googleConnected: true,
          googleProfile: {
            id: "google-123",
            email: user.email,
            name: user.name,
            picture: user.avatar
          }
        });
      }
    } catch (error) {
      console.error("Error connecting Google account:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle Google disconnection
  const handleDisconnectGoogle = async () => {
    setIsLoading(true);
    
    try {
      await disconnectGoogle();
      
      // Update user object
      if (onUpdate) {
        onUpdate({
          ...user,
          googleConnected: false,
          googleProfile: undefined
        });
      }
      
      setShowDisconnectDialog(false);
    } catch (error) {
      console.error("Error disconnecting Google account:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("profile.connectedAccounts")}</CardTitle>
        <CardDescription>{t("profile.connectedAccountsDescription")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="w-8 h-8">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
            </div>
            <div>
              <h3 className="font-medium">{t("profile.googleAccount")}</h3>
              <p className="text-sm text-muted-foreground">
                {user.googleConnected
                  ? t("profile.googleConnected")
                  : t("profile.googleNotConnected")}
              </p>
            </div>
          </div>
          {user.googleConnected ? (
            <Button
              variant="outline"
              onClick={() => setShowDisconnectDialog(true)}
              disabled={isLoading}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t("profile.disconnect")}
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={handleConnectGoogle}
              disabled={isLoading}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t("profile.connect")}
            </Button>
          )}
        </div>
        
        {user.twoFactorEnabled && user.googleConnected && (
          <Alert variant="warning">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>{t("profile.warning")}</AlertTitle>
            <AlertDescription>
              {t("profile.disconnectGoogleWarning")}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      
      {/* Disconnect Google Dialog */}
      <AlertDialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("profile.disconnectGoogleConfirmation")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("profile.disconnectGoogleDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDisconnectGoogle}
              disabled={isLoading}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t("profile.disconnect")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
