"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { User } from "@mug/models/user";
import { TwoFactorMethod } from "@mug/models/auth";
import { SetupTwoFactorDto } from "@mug/services/dtos/profile.dto";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import { setup2FA, disable2FA, generateBackupCodes, enable2FA } from "@mug/services/user";
import { Loader2, Shield, ShieldCheck, Copy, Download, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";
import { QRCodeSVG } from "qrcode.react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@mug/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";

interface TwoFactorAuthProps {
  user: User;
  onUpdate?: (user: User) => void;
}

export function TwoFactorAuth({ user, onUpdate }: TwoFactorAuthProps) {
  const { t } = useTranslate();
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<"initial" | "setup" | "verify">("initial");
  const [method, setMethod] = useState<TwoFactorMethod>(user.twoFactorMethod || "app");
  const [verificationCode, setVerificationCode] = useState("");
  const [setupData, setSetupData] = useState<{ secret?: string; qrCode?: string; otpauthUrl?: string }>({});
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [showDisableDialog, setShowDisableDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Start 2FA setup
  const handleStartSetup = async () => {
    console.log("Starting 2FA setup...");
    setIsLoading(true);
    setError(null);

    try {
      const data: SetupTwoFactorDto = {
        method: "app"
      };

      const setupResult = await setup2FA(data);
      console.log("Setup 2FA result:", setupResult);

      // Map API response fields to component expected fields
      const mappedData = {
        secret: setupResult.setupKey,
        qrCode: setupResult.qrCodeUrl,
        otpauthUrl: setupResult.qrCodeUrl
      };
      console.log("Mapped setup data:", mappedData);

      setSetupData(mappedData);
      setStep("setup");
    } catch (err) {
      console.error("Error setting up 2FA:", err);
      setError(err instanceof Error ? err.message : "Failed to setup two-factor authentication");
    } finally {
      setIsLoading(false);
    }
  };

  // Disable 2FA
  const handleDisable2FA = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await disable2FA();

      // Update user object
      if (onUpdate) {
        onUpdate({
          ...user,
          twoFactorEnabled: false,
          twoFactorMethod: undefined
        });
      }

      setShowDisableDialog(false);
    } catch (err) {
      console.error("Error disabling 2FA:", err);
      setError(err instanceof Error ? err.message : "Failed to disable two-factor authentication");
    } finally {
      setIsLoading(false);
    }
  };

  // Copy backup codes to clipboard
  const handleCopyBackupCodes = () => {
    navigator.clipboard.writeText(backupCodes.join("\n"));
  };

  // Download backup codes
  const handleDownloadBackupCodes = () => {
    const content = backupCodes.join("\n");
    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "backup-codes.txt";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Render initial state
  if (step === "initial") {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("profile.twoFactorAuth")}</CardTitle>
          <CardDescription>{t("profile.twoFactorAuthDescription")}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>{t("profile.error")}</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {user.twoFactorEnabled ? (
            <div className="space-y-4">
              <Alert className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-900/50">
                <ShieldCheck className="h-4 w-4 text-green-600 dark:text-green-400" />
                <AlertTitle>{t("profile.twoFactorEnabled")}</AlertTitle>
                <AlertDescription>
                  {t("profile.twoFactorEnabledDescription", { method: t(`profile.twoFactorMethods.${user.twoFactorMethod}`) })}
                </AlertDescription>
              </Alert>

              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowBackupCodes(true)}
                >
                  {t("profile.viewBackupCodes")}
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowDisableDialog(true)}
                >
                  {t("profile.disable2FA")}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Alert>
                <Shield className="h-4 w-4" />
                <AlertTitle>{t("profile.twoFactorDisabled")}</AlertTitle>
                <AlertDescription>
                  {t("profile.twoFactorDisabledDescription")}
                </AlertDescription>
              </Alert>

              <div className="space-y-4 pt-4">
                <div className="flex items-start space-x-2">
                  <div className="grid gap-1.5 leading-none">
                    <Label className="font-medium">
                      {t("profile.twoFactorMethods.app")}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {t("profile.twoFactorMethodDescriptions.app")}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
        {!user.twoFactorEnabled && (
          <CardFooter>
            <Button onClick={handleStartSetup} disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t("profile.setup2FA")}
            </Button>
          </CardFooter>
        )}

        {/* Backup Codes Dialog */}
        <Dialog open={showBackupCodes} onOpenChange={setShowBackupCodes}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("profile.backupCodes")}</DialogTitle>
              <DialogDescription>
                {t("profile.backupCodesDescription")}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="bg-muted p-4 rounded-md font-mono text-sm">
                {backupCodes.map((code, index) => (
                  <div key={index} className="py-1">
                    {code}
                  </div>
                ))}
              </div>
              <p className="text-sm text-muted-foreground">
                {t("profile.backupCodesWarning")}
              </p>
            </div>
            <DialogFooter className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" onClick={handleCopyBackupCodes} className="sm:flex-1">
                <Copy className="mr-2 h-4 w-4" />
                {t("profile.copyBackupCodes")}
              </Button>
              <Button variant="outline" onClick={handleDownloadBackupCodes} className="sm:flex-1">
                <Download className="mr-2 h-4 w-4" />
                {t("profile.downloadBackupCodes")}
              </Button>
              <Button onClick={() => setShowBackupCodes(false)} className="sm:flex-1">
                {t("common.done")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Disable 2FA Dialog */}
        <AlertDialog open={showDisableDialog} onOpenChange={setShowDisableDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{t("profile.disable2FAConfirmation")}</AlertDialogTitle>
              <AlertDialogDescription>
                {t("profile.disable2FAWarning")}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDisable2FA}
                disabled={isLoading}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t("profile.disable2FA")}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </Card>
    );
  }

  // Render setup state
  if (step === "setup") {
    console.log("Rendering setup state with data:", setupData);
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("profile.setup2FA")}</CardTitle>
          <CardDescription>
            {t(`profile.setup2FADescription.${method}`)}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>{t("profile.error")}</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {setupData.otpauthUrl ? (
            <div className="flex flex-col items-center space-y-4">
              <div className="border p-4 rounded-md">
                <QRCodeSVG
                  value={setupData.otpauthUrl}
                  size={192}
                  level="H"
                  className="w-48 h-48"
                  style={{ margin: '0 auto' }}
                />
                <p className="text-xs text-center mt-2 text-muted-foreground">
                  Scan this QR code with your authenticator app
                </p>
                <p className="text-xs text-center mt-1 text-muted-foreground">
                  URI: {setupData.otpauthUrl.length > 50 ? `${setupData.otpauthUrl.substring(0, 50)}...` : setupData.otpauthUrl}
                </p>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-4">
              <div className="border p-4 rounded-md bg-muted flex items-center justify-center h-48 w-48">
                <p className="text-muted-foreground">QR Code not available</p>
              </div>
            </div>
          )}

          {setupData.secret && (
            <div className="space-y-2 w-full">
              <Label>{t("profile.manualEntry")}</Label>
              <div className="flex">
                <Input
                  value={setupData.secret}
                  readOnly
                  className="font-mono"
                />
                <Button
                  variant="outline"
                  size="icon"
                  className="ml-2"
                  onClick={() => navigator.clipboard.writeText(setupData.secret!)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                {t("profile.manualEntryDescription")}
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="verificationCode">{t("profile.verificationCode")}</Label>
            <Input
              id="verificationCode"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              placeholder={t("profile.verificationCodePlaceholder")}
              className="font-mono text-center text-lg tracking-widest"
              maxLength={6}
            />
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => setStep("initial")}
            className="sm:flex-1"
          >
            {t("common.cancel")}
          </Button>
          <Button
            onClick={async () => {
              setIsLoading(true);
              setError(null);

              try {
                // Enable 2FA directly with the setup key, method and verification code
                if (setupData.secret) {
                  await enable2FA(setupData.secret, method, verificationCode);
                }

                // Generate backup codes
                const codes = await generateBackupCodes();
                setBackupCodes(codes);
                setShowBackupCodes(true);

                // Update user object
                if (onUpdate) {
                  onUpdate({
                    ...user,
                    twoFactorEnabled: true,
                    twoFactorMethod: method
                  });
                }

                setStep("initial");
              } catch (err) {
                console.error("Error enabling 2FA:", err);
                setError(err instanceof Error ? err.message : "Failed to enable two-factor authentication");
              } finally {
                setIsLoading(false);
              }
            }}
            disabled={isLoading || verificationCode.length < 6}
            className="sm:flex-1"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("profile.activate")}
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return null;
}
