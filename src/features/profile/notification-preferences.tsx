"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { User, NotificationPreferences as NotificationPreferencesType } from "@mug/models/user";
import { UpdateProfileDTO } from "@mug/services/auth";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";
import { Switch } from "@mug/components/ui/switch";
import { Label } from "@mug/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@mug/components/ui/select";
import { updateUserProfile } from "@mug/services/user";
import { Loader2 } from "lucide-react";

interface NotificationPreferencesProps {
  user: User;
  onUpdate?: (user: User) => void;
}

export function NotificationPreferences({ user, onUpdate }: NotificationPreferencesProps) {
  const { t } = useTranslate();
  const [isLoading, setIsLoading] = useState(false);
  const [preferences, setPreferences] = useState<NotificationPreferencesType>(
    user.notificationPreferences || {
      email: true,
      push: true,
      inApp: true,
      marketing: false,
      digest: "daily"
    }
  );

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const data: UpdateProfileDTO = {
        notificationPreferences: preferences
      };

      const updatedUser = await updateUserProfile(data);

      if (onUpdate) {
        onUpdate(updatedUser);
      }
    } catch (error) {
      console.error("Error updating notification preferences:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle switch change
  const handleSwitchChange = (name: keyof Omit<NotificationPreferencesType, "digest">) => {
    setPreferences(prev => ({
      ...prev,
      [name]: !prev[name]
    }));
  };

  // Handle select change
  const handleSelectChange = (value: string) => {
    setPreferences(prev => ({
      ...prev,
      digest: value as "daily" | "weekly" | "never"
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("profile.notificationPreferences")}</CardTitle>
        <CardDescription>{t("profile.notificationPreferencesDescription")}</CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            {/* Email Notifications */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="email-notifications">{t("profile.emailNotifications")}</Label>
                <p className="text-sm text-muted-foreground">{t("profile.emailNotificationsDescription")}</p>
              </div>
              <Switch
                id="email-notifications"
                checked={preferences.email}
                onCheckedChange={() => handleSwitchChange("email")}
              />
            </div>

            {/* Push Notifications */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="push-notifications">{t("profile.pushNotifications")}</Label>
                <p className="text-sm text-muted-foreground">{t("profile.pushNotificationsDescription")}</p>
              </div>
              <Switch
                id="push-notifications"
                checked={preferences.push}
                onCheckedChange={() => handleSwitchChange("push")}
              />
            </div>

            {/* In-App Notifications */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="inapp-notifications">{t("profile.inAppNotifications")}</Label>
                <p className="text-sm text-muted-foreground">{t("profile.inAppNotificationsDescription")}</p>
              </div>
              <Switch
                id="inapp-notifications"
                checked={preferences.inApp}
                onCheckedChange={() => handleSwitchChange("inApp")}
              />
            </div>

            {/* Marketing Notifications */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="marketing-notifications">{t("profile.marketingNotifications")}</Label>
                <p className="text-sm text-muted-foreground">{t("profile.marketingNotificationsDescription")}</p>
              </div>
              <Switch
                id="marketing-notifications"
                checked={preferences.marketing}
                onCheckedChange={() => handleSwitchChange("marketing")}
              />
            </div>

            {/* Digest Frequency */}
            <div className="space-y-2 pt-4">
              <Label htmlFor="digest-frequency">{t("profile.digestFrequency")}</Label>
              <Select
                value={preferences.digest}
                onValueChange={handleSelectChange}
              >
                <SelectTrigger id="digest-frequency">
                  <SelectValue placeholder={t("profile.selectDigestFrequency")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">{t("profile.digestFrequencies.daily")}</SelectItem>
                  <SelectItem value="weekly">{t("profile.digestFrequencies.weekly")}</SelectItem>
                  <SelectItem value="never">{t("profile.digestFrequencies.never")}</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground mt-1">{t("profile.digestFrequencyDescription")}</p>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("common.save")}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
