"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@mug/components/ui/card";
import { <PERSON><PERSON> } from "@mug/components/ui/button";
import { exportUserData, requestAccountDeletion } from "@mug/services/user";
import { Loader2, Download, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";

interface DataManagementProps {}

export function DataManagement({}: DataManagementProps) {
  const { t } = useTranslate();
  const [isExporting, setIsExporting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmText, setConfirmText] = useState("");
  
  // Handle data export
  const handleExportData = async () => {
    setIsExporting(true);
    
    try {
      const blob = await exportUserData();
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "user-data.json";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error exporting user data:", error);
    } finally {
      setIsExporting(false);
    }
  };
  
  // Handle account deletion
  const handleDeleteAccount = async () => {
    setIsDeleting(true);
    
    try {
      await requestAccountDeletion();
      setShowDeleteDialog(false);
      
      // In a real implementation, this would redirect to a logout page
      // or show a success message
    } catch (error) {
      console.error("Error requesting account deletion:", error);
    } finally {
      setIsDeleting(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("profile.dataManagement")}</CardTitle>
        <CardDescription>{t("profile.dataManagementDescription")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="font-medium">{t("profile.exportData")}</h3>
            <p className="text-sm text-muted-foreground">
              {t("profile.exportDataDescription")}
            </p>
            <Button
              variant="outline"
              onClick={handleExportData}
              disabled={isExporting}
            >
              {isExporting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Download className="mr-2 h-4 w-4" />
              )}
              {t("profile.downloadData")}
            </Button>
          </div>
          
          <div className="pt-4 border-t">
            <div className="space-y-2">
              <h3 className="font-medium text-destructive">{t("profile.deleteAccount")}</h3>
              <p className="text-sm text-muted-foreground">
                {t("profile.deleteAccountDescription")}
              </p>
              <Button
                variant="destructive"
                onClick={() => setShowDeleteDialog(true)}
              >
                {t("profile.deleteAccount")}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
      
      {/* Delete Account Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("profile.deleteAccountConfirmation")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("profile.deleteAccountWarning")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4 py-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>{t("profile.warning")}</AlertTitle>
              <AlertDescription>
                {t("profile.deleteAccountPermanent")}
              </AlertDescription>
            </Alert>
            
            <div className="space-y-2">
              <Label htmlFor="confirm-delete">
                {t("profile.typeToConfirm", { text: "DELETE" })}
              </Label>
              <Input
                id="confirm-delete"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder="DELETE"
              />
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAccount}
              disabled={isDeleting || confirmText !== "DELETE"}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t("profile.confirmDelete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
