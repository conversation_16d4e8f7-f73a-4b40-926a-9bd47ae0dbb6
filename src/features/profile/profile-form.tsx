"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { User } from "@mug/models/user";
import { UpdateProfileDTO } from "@mug/services/auth";
import { Language, Theme } from "@mug/models/core";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@mug/components/ui/select";
import { updateUserProfile } from "@mug/services/user";
import { Loader2, Upload } from "lucide-react";
import { timezones } from "@mug/constants/timezones";

interface ProfileFormProps {
  user: User;
  onUpdate?: (user: User) => void;
}

export function ProfileForm({ user, onUpdate }: ProfileFormProps) {
  const { t, locale, setLocale } = useTranslate();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<UpdateProfileDTO>({
    name: user.name,
    email: user.email,
    phone: user.phone || "",
    timezone: user.timezone || "America/Sao_Paulo",
    language: user.language || (locale as Language),
    theme: user.theme || Theme.SYSTEM
  });

  // Get user initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  const userInitials = getInitials(user.name);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const updatedUser = await updateUserProfile(formData);

      // Update language if changed
      if (formData.language && formData.language !== locale) {
        setLocale(formData.language);
      }

      if (onUpdate) {
        onUpdate(updatedUser);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("profile.personalInfo")}</CardTitle>
        <CardDescription>{t("profile.personalInfoDescription")}</CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {/* Avatar */}
          <div className="flex flex-col items-center sm:flex-row sm:items-start gap-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="text-lg">{userInitials}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col gap-2 text-center sm:text-left">
              <h3 className="font-medium">{t("profile.profilePicture")}</h3>
              <p className="text-sm text-muted-foreground">{t("profile.profilePictureDescription")}</p>
              <div className="flex gap-2 mt-2 justify-center sm:justify-start">
                <Button type="button" variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  {t("profile.uploadPicture")}
                </Button>
                {user.avatar && (
                  <Button type="button" variant="outline" size="sm">
                    {t("profile.removePicture")}
                  </Button>
                )}
              </div>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name">{t("profile.name")}</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={t("profile.namePlaceholder")}
              />
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">{t("profile.email")}</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder={t("profile.emailPlaceholder")}
              />
            </div>

            {/* Phone */}
            <div className="space-y-2">
              <Label htmlFor="phone">{t("profile.phone")}</Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder={t("profile.phonePlaceholder")}
              />
            </div>

            {/* Timezone */}
            <div className="space-y-2">
              <Label htmlFor="timezone">{t("profile.timezone")}</Label>
              <Select
                value={formData.timezone}
                onValueChange={(value) => handleSelectChange("timezone", value)}
              >
                <SelectTrigger id="timezone">
                  <SelectValue placeholder={t("profile.selectTimezone")} />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((timezone) => (
                    <SelectItem key={timezone.value} value={timezone.value}>
                      {timezone.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Language */}
            <div className="space-y-2">
              <Label htmlFor="language">{t("profile.language")}</Label>
              <Select
                value={formData.language}
                onValueChange={(value) => handleSelectChange("language", value)}
              >
                <SelectTrigger id="language">
                  <SelectValue placeholder={t("profile.selectLanguage")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={Language.PORTUGUESE}>{t("languages.pt-BR")}</SelectItem>
                  <SelectItem value={Language.ENGLISH}>{t("languages.en")}</SelectItem>
                  <SelectItem value={Language.SPANISH}>{t("languages.es")}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Theme */}
            <div className="space-y-2">
              <Label htmlFor="theme">{t("profile.theme")}</Label>
              <Select
                value={formData.theme}
                onValueChange={(value) => handleSelectChange("theme", value as Theme)}
              >
                <SelectTrigger id="theme">
                  <SelectValue placeholder={t("profile.selectTheme")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={Theme.LIGHT}>{t("themes.light")}</SelectItem>
                  <SelectItem value={Theme.DARK}>{t("themes.dark")}</SelectItem>
                  <SelectItem value={Theme.SYSTEM}>{t("themes.system")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("common.save")}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
