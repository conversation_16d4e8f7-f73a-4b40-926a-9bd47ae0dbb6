"use client";

import { useState, useEffect } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { LoginHistory as LoginHistoryType, ConnectedDevice } from "@mug/models/user";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@mug/components/ui/tabs";
import { getLoginHistory, getConnectedDevices, revokeDevice } from "@mug/services/user";
import { Loader2, Check, X, Laptop, Smartphone, Tablet, Monitor, LogOut } from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import { ptBR, enUS, es } from "date-fns/locale";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";
import { Badge } from "@mug/components/ui/badge";

interface LoginHistoryProps {}

export function LoginHistoryComponent({}: LoginHistoryProps) {
  const { t, locale } = useTranslate();
  const [isLoading, setIsLoading] = useState(true);
  const [loginHistory, setLoginHistory] = useState<LoginHistoryType[]>([]);
  const [connectedDevices, setConnectedDevices] = useState<ConnectedDevice[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<ConnectedDevice | null>(null);
  const [showRevokeDialog, setShowRevokeDialog] = useState(false);
  const [activeTab, setActiveTab] = useState("history");
  
  // Get date-fns locale
  const getLocale = () => {
    switch (locale) {
      case "pt-BR":
        return ptBR;
      case "es":
        return es;
      default:
        return enUS;
    }
  };
  
  // Load data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      
      try {
        const [history, devices] = await Promise.all([
          getLoginHistory(),
          getConnectedDevices()
        ]);
        
        setLoginHistory(history);
        setConnectedDevices(devices);
      } catch (error) {
        console.error("Error loading data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, []);
  
  // Handle device revocation
  const handleRevokeDevice = async () => {
    if (!selectedDevice) return;
    
    try {
      await revokeDevice(selectedDevice.id);
      
      // Update devices list
      setConnectedDevices(prev => prev.filter(device => device.id !== selectedDevice.id));
      setShowRevokeDialog(false);
      setSelectedDevice(null);
    } catch (error) {
      console.error("Error revoking device:", error);
    }
  };
  
  // Get device icon
  const getDeviceIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "mobile":
        return <Smartphone className="h-4 w-4" />;
      case "tablet":
        return <Tablet className="h-4 w-4" />;
      case "desktop":
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "PPpp", { locale: getLocale() });
    } catch (error) {
      return dateString;
    }
  };
  
  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: getLocale() });
    } catch (error) {
      return dateString;
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("profile.securityActivity")}</CardTitle>
        <CardDescription>{t("profile.securityActivityDescription")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="history">{t("profile.loginHistory")}</TabsTrigger>
            <TabsTrigger value="devices">{t("profile.connectedDevices")}</TabsTrigger>
          </TabsList>
          
          {/* Login History Tab */}
          <TabsContent value="history" className="space-y-4 pt-4">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : loginHistory.length > 0 ? (
              <div className="space-y-4">
                {loginHistory.map((login) => (
                  <div
                    key={login.id}
                    className="flex items-center justify-between p-4 border rounded-md"
                  >
                    <div className="flex items-start space-x-4">
                      <div className={`mt-0.5 rounded-full p-1 ${login.success ? "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400" : "bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400"}`}>
                        {login.success ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          {login.success
                            ? t("profile.successfulLogin")
                            : t("profile.failedLogin")}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {login.device} • {login.browser}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {login.ip} • {login.location}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm">{formatDate(login.timestamp)}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatRelativeTime(login.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex justify-center py-8 text-muted-foreground">
                {t("profile.noLoginHistory")}
              </div>
            )}
          </TabsContent>
          
          {/* Connected Devices Tab */}
          <TabsContent value="devices" className="space-y-4 pt-4">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : connectedDevices.length > 0 ? (
              <div className="space-y-4">
                {connectedDevices.map((device) => (
                  <div
                    key={device.id}
                    className="flex items-center justify-between p-4 border rounded-md"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="mt-0.5 rounded-full p-1 bg-primary/10 text-primary">
                        {getDeviceIcon(device.type)}
                      </div>
                      <div>
                        <div className="flex items-center">
                          <p className="font-medium">{device.name}</p>
                          {device.isCurrent && (
                            <Badge variant="outline" className="ml-2">
                              {t("profile.currentDevice")}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {device.browser} • {device.os}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {device.ip} • {device.location}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t("profile.lastActive")}: {formatRelativeTime(device.lastActive)}
                        </p>
                      </div>
                    </div>
                    {!device.isCurrent && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedDevice(device);
                          setShowRevokeDialog(true);
                        }}
                      >
                        <LogOut className="h-4 w-4" />
                        <span className="sr-only">{t("profile.revokeAccess")}</span>
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex justify-center py-8 text-muted-foreground">
                {t("profile.noConnectedDevices")}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      
      {/* Revoke Device Dialog */}
      <AlertDialog open={showRevokeDialog} onOpenChange={setShowRevokeDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("profile.revokeDeviceConfirmation")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("profile.revokeDeviceDescription", { device: selectedDevice?.name })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRevokeDevice}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("profile.revokeAccess")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
