"use client";

import { useState, useEffect } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { CalendarEvent } from "@mug/models/calendar";
import { readEvents } from "@mug/services/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@mug/components/ui/popover";
import { Button } from "@mug/components/ui/button";
import { Calendar } from "@mug/components/ui/calendar";
import { Badge } from "@mug/components/ui/badge";
import { Calendar as CalendarIcon } from "lucide-react";
import { format, isSameDay } from "date-fns";
import { ptBR } from "date-fns/locale";
import { classNameBuilder as cn } from "@mug/lib/utils/class-name-builder";
import { useRouter } from "next/navigation";

interface MiniCalendarProps {
  className?: string;
}

export function MiniCalendar({ className }: MiniCalendarProps) {
  const { t } = useTranslate();
  const router = useRouter();
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [isLoading, setIsLoading] = useState(true);

  // Load events
  useEffect(() => {
    const loadEvents = async () => {
      setIsLoading(true);
      try {
        const data = await readEvents();
        setEvents(data);
      } catch (error) {
        console.error("Error loading events:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadEvents();
  }, []);

  // Get events for selected date
  const getEventsForDate = (date: Date) => {
    return events.filter(event => {
      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);

      // Check if the date is between start and end dates
      return (
        isSameDay(date, eventStart) ||
        isSameDay(date, eventEnd) ||
        (date > eventStart && date < eventEnd)
      );
    });
  };

  // Handle date selection
  const handleSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    if (date) {
      router.push(`/calendar?date=${format(date, "yyyy-MM-dd")}`);
    }
  };

  // We'll use a simpler approach for event indicators

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "h-9 gap-1 border-dashed",
            className
          )}
        >
          <CalendarIcon className="h-4 w-4" />
          <span className="hidden md:inline-block">
            {selectedDate ? format(selectedDate, "PPP", { locale: ptBR }) : t("calendar.selectDate")}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="center">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={handleSelect}
          initialFocus
          // Removed custom Day component due to type issues
        />

        {selectedDate && (
          <div className="p-3 border-t">
            <h3 className="font-medium text-sm mb-2">
              {format(selectedDate, "PPP", { locale: ptBR })}
            </h3>
            <div className="space-y-1 max-h-[150px] overflow-auto">
              {isLoading ? (
                <p className="text-xs text-muted-foreground">{t("common.loading")}</p>
              ) : (
                <>
                  {getEventsForDate(selectedDate).length > 0 ? (
                    getEventsForDate(selectedDate).map((event) => (
                      <div
                        key={event.id}
                        className="text-xs p-1 rounded bg-muted flex items-center justify-between"
                      >
                        <span className="truncate max-w-[180px]">{event.title}</span>
                        <Badge
                          variant="outline"
                          className="text-[10px] h-4 px-1"
                        >
                          {t(`calendar.types.${event.type}`)}
                        </Badge>
                      </div>
                    ))
                  ) : (
                    <p className="text-xs text-muted-foreground">{t("calendar.noEventsForDate")}</p>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
