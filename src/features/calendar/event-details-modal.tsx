"use client";

import { useTranslate } from "@mug/contexts/translate";
import { CalendarEvent } from "@mug/models/calendar";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@mug/components/ui/dialog";
import { But<PERSON> } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar as CalendarIcon, MapPin, Link2, Users, Edit, Trash2, ExternalLink } from "lucide-react";
import { classNameBuilder as cn } from "@mug/lib/utils/class-name-builder";

interface EventDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: CalendarEvent;
  onEdit?: () => void;
  onDelete?: () => void;
}

export function EventDetailsModal({
  isOpen,
  onClose,
  event,
  onEdit,
  onDelete,
}: EventDetailsModalProps) {
  const { t } = useTranslate();

  // Format dates
  const formatDate = (dateString: string, showTime: boolean = true) => {
    const date = new Date(dateString);
    return format(
      date,
      showTime ? "PPP 'às' HH:mm" : "PPP",
      { locale: ptBR }
    );
  };

  // Get type badge color
  const getTypeBadgeColor = (type: string) => {
    const typeColors: Record<string, string> = {
      meeting: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100",
      call: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100",
      task: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
      reminder: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
      deadline: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
      appointment: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-100",
      other: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100",
    };

    return typeColors[type] || typeColors.other;
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    const statusColors: Record<string, string> = {
      scheduled: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100",
      confirmed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
      cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
      completed: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100",
      pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
    };

    return statusColors[status] || statusColors.scheduled;
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority?: string) => {
    if (!priority) return "";

    const priorityColors: Record<string, string> = {
      high: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
      medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
      low: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
    };

    return priorityColors[priority] || priorityColors.medium;
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl">{event.title}</DialogTitle>
            <div className="flex items-center space-x-2">
              <Badge className={cn(getTypeBadgeColor(event.type))}>
                {t(`calendar.types.${event.type}`)}
              </Badge>
              <Badge className={cn(getStatusBadgeColor(event.status))}>
                {t(`calendar.statuses.${event.status}`)}
              </Badge>
              {event.priority && (
                <Badge className={cn(getPriorityBadgeColor(event.priority))}>
                  {t(`calendar.priorities.${event.priority}`)}
                </Badge>
              )}
            </div>
          </div>
          {event.description && (
            <DialogDescription className="text-sm mt-2">
              {event.description}
            </DialogDescription>
          )}
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Date and Time */}
          <div className="flex items-start space-x-2">
            <CalendarIcon className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div className="space-y-1">
              <p className="font-medium">{t("calendar.dateAndTime")}</p>
              <p className="text-sm text-muted-foreground">
                {event.allDay ? (
                  <>
                    {formatDate(event.start, false)}
                    {" - "}
                    {formatDate(event.end, false)}
                    {" "}
                    <span className="italic">({t("calendar.allDay")})</span>
                  </>
                ) : (
                  <>
                    {formatDate(event.start)}
                    {" - "}
                    {formatDate(event.end)}
                  </>
                )}
              </p>
            </div>
          </div>

          {/* Location */}
          {event.location && (
            <div className="flex items-start space-x-2">
              <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div className="space-y-1">
                <p className="font-medium">{t("calendar.location")}</p>
                <p className="text-sm text-muted-foreground">{event.location}</p>
              </div>
            </div>
          )}

          {/* URL */}
          {event.url && (
            <div className="flex items-start space-x-2">
              <Link2 className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div className="space-y-1">
                <p className="font-medium">{t("calendar.url")}</p>
                <a
                  href={event.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:underline flex items-center"
                >
                  {event.url}
                  <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </div>
            </div>
          )}

          {/* Attendees */}
          {event.attendees && event.attendees.length > 0 && (
            <div className="flex items-start space-x-2">
              <Users className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div className="space-y-1">
                <p className="font-medium">{t("calendar.attendees")}</p>
                <ul className="text-sm text-muted-foreground space-y-1">
                  {event.attendees.map((attendee) => (
                    <li key={attendee.id} className="flex items-center justify-between">
                      <span>{attendee.name}</span>
                      <Badge
                        variant="outline"
                        className="text-xs"
                      >
                        {t(`calendar.attendeeStatuses.${attendee.status}`)}
                      </Badge>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Related Flow */}
          {event.flowId && (
            <div className="flex items-start space-x-2">
              <div className="h-5 w-5 text-muted-foreground mt-0.5 flex items-center justify-center">
                <span className="text-xs font-bold">F</span>
              </div>
              <div className="space-y-1">
                <p className="font-medium">{t("calendar.relatedFlow")}</p>
                <p className="text-sm text-muted-foreground">{event.flowId}</p>
              </div>
            </div>
          )}

          {/* Related Record */}
          {event.recordId && (
            <div className="flex items-start space-x-2">
              <div className="h-5 w-5 text-muted-foreground mt-0.5 flex items-center justify-center">
                <span className="text-xs font-bold">R</span>
              </div>
              <div className="space-y-1">
                <p className="font-medium">{t("calendar.relatedRecord")}</p>
                <p className="text-sm text-muted-foreground">{event.recordId}</p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <div>
            {onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={onDelete}
                className="mr-2"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                {t("common.delete")}
              </Button>
            )}
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              {t("common.close")}
            </Button>
            {onEdit && (
              <Button onClick={onEdit}>
                <Edit className="h-4 w-4 mr-1" />
                {t("common.edit")}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
