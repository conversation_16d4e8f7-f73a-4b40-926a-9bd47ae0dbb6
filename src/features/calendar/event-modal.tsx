"use client";

import { useTranslate } from "@mug/contexts/translate";
import { CalendarEvent, EventFormValues, EventType, EventStatus, EventPriority } from "@mug/models/calendar";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@mug/components/ui/dialog";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Textarea } from "@mug/components/ui/textarea";
import { Checkbox } from "@mug/components/ui/checkbox";
import { Calendar as CalendarIcon, MapPin, Link2 } from "lucide-react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@mug/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@mug/components/ui/popover";
import { Calendar } from "@mug/components/ui/calendar";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { classNameBuilder as cn } from "@mug/lib/utils/class-name-builder";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: EventFormValues) => void;
  event?: CalendarEvent;
  initialDate?: Date;
  initialEndDate?: Date;
}

export function EventModal({
  isOpen,
  onClose,
  onSave,
  event,
  initialDate,
  initialEndDate,
}: EventModalProps) {
  const { t } = useTranslate();
  const isEditing = !!event;

  // Create form schema
  const formSchema = z.object({
    title: z.string().min(1, t("calendar.titleRequired")),
    description: z.string().optional(),
    start: z.date(),
    end: z.date(),
    allDay: z.boolean().default(false),
    location: z.string().optional(),
    url: z.string().optional(),
    type: z.string() as z.ZodType<EventType>,
    status: z.string() as z.ZodType<EventStatus>,
    priority: z.string().optional() as z.ZodType<EventPriority | undefined>,
    flowId: z.string().optional(),
    recordId: z.string().optional(),
  }).refine(data => data.end > data.start, {
    message: t("calendar.endDateMustBeAfterStart"),
    path: ["end"],
  });

  // Create form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: event?.title || "",
      description: event?.description || "",
      start: event ? new Date(event.start) : initialDate || new Date(),
      end: event ? new Date(event.end) : initialEndDate || new Date(new Date().setHours(new Date().getHours() + 1)),
      allDay: event?.allDay || false,
      location: event?.location || "",
      url: event?.url || "",
      type: event?.type || "meeting",
      status: event?.status || "scheduled",
      priority: event?.priority || "medium",
      flowId: event?.flowId || "",
      recordId: event?.recordId || "",
    },
  });

  // Handle form submission
  const onSubmit = (data: z.infer<typeof formSchema>) => {
    onSave(data);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? t("calendar.editEvent") : t("calendar.createEvent")}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? t("calendar.editEventDescription")
              : t("calendar.createEventDescription")}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("calendar.title")}</FormLabel>
                  <FormControl>
                    <Input placeholder={t("calendar.titlePlaceholder")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("calendar.description")}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("calendar.descriptionPlaceholder")}
                      className="resize-none"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* All Day */}
            <FormField
              control={form.control}
              name="allDay"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>{t("calendar.allDay")}</FormLabel>
                    <FormDescription>
                      {t("calendar.allDayDescription")}
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              {/* Start Date */}
              <FormField
                control={form.control}
                name="start"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>{t("calendar.startDate")}</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? (
                              format(field.value, "PPP", { locale: ptBR })
                            ) : (
                              <span>{t("calendar.selectDate")}</span>
                            )}
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* End Date */}
              <FormField
                control={form.control}
                name="end"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>{t("calendar.endDate")}</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? (
                              format(field.value, "PPP", { locale: ptBR })
                            ) : (
                              <span>{t("calendar.selectDate")}</span>
                            )}
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Location */}
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("calendar.location")}</FormLabel>
                  <FormControl>
                    <div className="flex items-center">
                      <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t("calendar.locationPlaceholder")}
                        {...field}
                        value={field.value || ""}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* URL */}
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("calendar.url")}</FormLabel>
                  <FormControl>
                    <div className="flex items-center">
                      <Link2 className="mr-2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t("calendar.urlPlaceholder")}
                        {...field}
                        value={field.value || ""}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              {/* Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("calendar.type")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("calendar.selectType")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="meeting">{t("calendar.types.meeting")}</SelectItem>
                        <SelectItem value="call">{t("calendar.types.call")}</SelectItem>
                        <SelectItem value="task">{t("calendar.types.task")}</SelectItem>
                        <SelectItem value="reminder">{t("calendar.types.reminder")}</SelectItem>
                        <SelectItem value="deadline">{t("calendar.types.deadline")}</SelectItem>
                        <SelectItem value="appointment">{t("calendar.types.appointment")}</SelectItem>
                        <SelectItem value="other">{t("calendar.types.other")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("calendar.status")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("calendar.selectStatus")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="scheduled">{t("calendar.statuses.scheduled")}</SelectItem>
                        <SelectItem value="confirmed">{t("calendar.statuses.confirmed")}</SelectItem>
                        <SelectItem value="cancelled">{t("calendar.statuses.cancelled")}</SelectItem>
                        <SelectItem value="completed">{t("calendar.statuses.completed")}</SelectItem>
                        <SelectItem value="pending">{t("calendar.statuses.pending")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Priority */}
            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("calendar.priority")}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t("calendar.selectPriority")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="high">{t("calendar.priorities.high")}</SelectItem>
                      <SelectItem value="medium">{t("calendar.priorities.medium")}</SelectItem>
                      <SelectItem value="low">{t("calendar.priorities.low")}</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                {t("common.cancel")}
              </Button>
              <Button type="submit">
                {isEditing ? t("common.save") : t("common.create")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
