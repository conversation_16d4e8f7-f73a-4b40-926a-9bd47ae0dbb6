"use client";

import { useCallback, useMemo } from "react";
import { Calendar, Views, DateLocalizer, SlotInfo, dateFnsLocalizer } from "react-big-calendar";
import { format as formatDate, parse as parseDate, startOfWeek as getStartOfWeek, getDay as getDayOfWeek, addMinutes } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CalendarEvent, CalendarView as ViewType } from "@mug/models/calendar";
import { useTheme } from "next-themes";
import { classNameBuilder as cn } from "@mug/lib/utils/class-name-builder";

// Import styles
import "react-big-calendar/lib/css/react-big-calendar.css";

// Create a custom localizer for date-fns
const locales = {
  "pt-BR": ptBR,
};

// Create a proper localizer using dateFnsLocalizer
const localizer = dateFnsLocalizer({
  format: formatDate,
  parse: parseDate,
  startOfWeek: getStartOfWeek,
  getDay: getDayOfWeek,
  locales,
});

interface CalendarViewProps {
  events: CalendarEvent[];
  defaultView?: ViewType;
  onSelectEvent?: (event: CalendarEvent) => void;
  onSelectSlot?: (slotInfo: SlotInfo) => void;
  onNavigate?: (date: Date) => void;
  onView?: (view: ViewType) => void;
}

export function CalendarView({
  events,
  defaultView = "month",
  onSelectEvent,
  onSelectSlot,
  onNavigate,
  onView,
}: CalendarViewProps) {
  // We'll use the defaultView directly
  const { theme } = useTheme();

  // Convert CalendarEvent to react-big-calendar Event
  const calendarEvents = useMemo(() => {
    return events.map(event => ({
      id: event.id,
      title: event.title,
      start: new Date(event.start),
      end: new Date(event.end),
      allDay: event.allDay,
      resource: event,
    }));
  }, [events]);

  // Handle event selection
  const handleSelectEvent = useCallback(
    (event: { resource: CalendarEvent }) => {
      if (onSelectEvent) {
        onSelectEvent(event.resource);
      }
    },
    [onSelectEvent]
  );

  // Handle slot selection
  const handleSelectSlot = useCallback(
    (slotInfo: SlotInfo) => {
      if (onSelectSlot) {
        // Ensure end time is at least 30 minutes after start time
        const start = new Date(slotInfo.start);
        let end = new Date(slotInfo.end);

        if (start.getTime() === end.getTime()) {
          end = addMinutes(start, 30);
        }

        onSelectSlot({
          ...slotInfo,
          start,
          end,
        });
      }
    },
    [onSelectSlot]
  );

  // Handle view change
  const handleView = useCallback(
    (newView: string) => {
      const typedView = newView as ViewType;
      if (onView) {
        onView(typedView);
      }
    },
    [onView]
  );

  // Get event style based on event type and priority
  const eventStyleGetter = useCallback(
    (event: { resource: CalendarEvent }) => {
      const resource = event.resource;
      const type = resource.type;
      const priority = resource.priority || "medium";
      const status = resource.status;

      // Base colors
      const typeColors: Record<string, { light: string; dark: string }> = {
        meeting: { light: "bg-blue-100 border-blue-500 text-blue-800", dark: "bg-blue-900 border-blue-500 text-blue-100" },
        call: { light: "bg-purple-100 border-purple-500 text-purple-800", dark: "bg-purple-900 border-purple-500 text-purple-100" },
        task: { light: "bg-green-100 border-green-500 text-green-800", dark: "bg-green-900 border-green-500 text-green-100" },
        reminder: { light: "bg-yellow-100 border-yellow-500 text-yellow-800", dark: "bg-yellow-900 border-yellow-500 text-yellow-100" },
        deadline: { light: "bg-red-100 border-red-500 text-red-800", dark: "bg-red-900 border-red-500 text-red-100" },
        appointment: { light: "bg-indigo-100 border-indigo-500 text-indigo-800", dark: "bg-indigo-900 border-indigo-500 text-indigo-100" },
        other: { light: "bg-gray-100 border-gray-500 text-gray-800", dark: "bg-gray-800 border-gray-500 text-gray-100" },
      };

      // Priority modifiers
      const priorityOpacity: Record<string, string> = {
        high: "opacity-100",
        medium: "opacity-90",
        low: "opacity-80",
      };

      // Status modifiers
      const statusStyles: Record<string, string> = {
        scheduled: "",
        confirmed: "",
        cancelled: "line-through opacity-60",
        completed: "",
        pending: "italic",
      };

      const colorSet = typeColors[type] || typeColors.other;
      const colors = theme === "dark" ? colorSet.dark : colorSet.light;

      return {
        className: cn(
          "border-l-4 rounded-sm px-1",
          colors,
          priorityOpacity[priority],
          statusStyles[status]
        ),
        style: {
          borderRadius: "3px",
        },
      };
    },
    [theme]
  );

  return (
    <div className={cn(
      "h-full",
      theme === "dark" ? "rbc-calendar-dark" : "rbc-calendar-light"
    )}>
      <Calendar
        localizer={localizer as DateLocalizer}
        events={calendarEvents}
        startAccessor="start"
        endAccessor="end"
        style={{ height: "100%" }}
        defaultView={defaultView}
        views={[Views.MONTH, Views.WEEK, Views.DAY, Views.AGENDA]}
        onSelectEvent={handleSelectEvent}
        onSelectSlot={handleSelectSlot}
        selectable
        onNavigate={onNavigate}
        onView={handleView}
        eventPropGetter={eventStyleGetter}
        popup
        tooltipAccessor={(event) => event.resource?.description || event.title}
        messages={{
          today: "Hoje",
          previous: "Anterior",
          next: "Próximo",
          month: "Mês",
          week: "Semana",
          day: "Dia",
          agenda: "Agenda",
          date: "Data",
          time: "Hora",
          event: "Evento",
          allDay: "Dia inteiro",
          noEventsInRange: "Não há eventos neste período.",
        }}
      />
    </div>
  );
}
