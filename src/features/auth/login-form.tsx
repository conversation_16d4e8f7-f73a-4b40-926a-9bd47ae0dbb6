"use client";

import { useState, FormEvent } from "react";
import Link from "next/link";
import { Loader2, Mail, User, Lock } from "lucide-react";

import { <PERSON><PERSON> } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import {
  <PERSON><PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from "@mug/components/ui/tabs";
import { Separator } from "@mug/components/ui/separator";

import { useTranslate } from "@mug/contexts/translate";
import { useAuth } from "@mug/contexts/auth";

import { LoginCredentialsDto } from "@mug/services/dtos";

interface LoginFormProps {
  returnUrl?: string;
}

export function LoginForm(props: LoginFormProps) {
  const { returnUrl } = props;

  const { t } = useTranslate();
  const {
    login,
    loginWithGoogle,
    requestMagicLink,
    clearError,
    isLoading,
  } = useAuth();
  const [activeTab, setActiveTab] = useState<"password" | "magic-link">(
    "password"
  );
  const [credentials, setCredentials] = useState<LoginCredentialsDto>({
    username: "",
    password: "",
  });
  const [magicLinkEmail, setMagicLinkEmail] = useState("");

  const handlePasswordLogin = async (e: FormEvent) => {
    e.preventDefault();
    clearError();
    await login(credentials, returnUrl);
  };

  const handleMagicLinkLogin = async (e: FormEvent) => {
    e.preventDefault();
    clearError();

    if (returnUrl) {
      localStorage.setItem("magic_link_return_url", returnUrl);
    }

    await requestMagicLink(magicLinkEmail);
  };

  const handleGoogleLogin = () => {
    clearError();
    loginWithGoogle(returnUrl);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{t("auth.login")}</CardTitle>
        <CardDescription>{t("auth.loginDescription")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs
          value={activeTab}
          onValueChange={(value) =>
            setActiveTab(value as "password" | "magic-link")
          }
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="password">{t("auth.withPassword")}</TabsTrigger>
            <TabsTrigger value="magic-link">
              {t("auth.withMagicLink")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="password">
            <form onSubmit={handlePasswordLogin} className="space-y-4 mt-4 flex flex-col items-center">
              <div className="space-y-2 w-full">
                <Label htmlFor="username">Username</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="username"
                    type="text"
                    placeholder="Enter your username"
                    className="pl-10"
                    value={credentials.username}
                    onChange={(e) =>
                      setCredentials({
                        ...credentials,
                        username: e.target.value,
                      })
                    }
                    required
                  />
                </div>
              </div>

              <div className="space-y-2 w-full">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">{t("auth.password")}</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="text-sm font-medium text-primary hover:underline"
                  >
                    {t("auth.forgotPassword")}
                  </Link>
                </div>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    placeholder={t("auth.passwordPlaceholder")}
                    className="pl-10"
                    value={credentials.password}
                    onChange={(e) =>
                      setCredentials({
                        ...credentials,
                        password: e.target.value,
                      })
                    }
                    required
                  />
                </div>
              </div>
              
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t("auth.login")}
              </Button>
            </form>
          </TabsContent>  

          <TabsContent value="magic-link">
            <form onSubmit={handleMagicLinkLogin} className="space-y-4 mt-4 flex flex-col items-center">
              <div className="space-y-2 w-full">
                <Label htmlFor="magic-link-email">{t("auth.email")}</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="magic-link-email"
                    type="email"
                    placeholder={t("auth.emailPlaceholder")}
                    className="pl-10"
                    value={magicLinkEmail}
                    onChange={(e) => setMagicLinkEmail(e.target.value)}
                    required
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("auth.magicLinkDescription")}
                </p>
              </div>

              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t("auth.sendMagicLink")}
              </Button>
            </form>
          </TabsContent>
        </Tabs>

        <div className="relative my-4">
          <div className="absolute inset-0 flex items-center">
            <Separator />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              {t("auth.orContinueWith")}
            </span>
          </div>
        </div>

        <div className="flex justify-center">
          <Button
            variant="outline"
            className="w-fit"
            onClick={() => {
              console.log("Google login button clicked");
              handleGoogleLogin();
            }}
            disabled={isLoading}
          >
            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
              <path
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                fill="#4285F4"
              />
              <path
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                fill="#34A853"
              />
              <path
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                fill="#FBBC05"
              />
              <path
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                fill="#EA4335"
              />
            </svg>
            {t("auth.continueWithGoogle")}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
