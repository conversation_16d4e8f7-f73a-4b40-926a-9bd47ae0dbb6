"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useAuth } from "@mug/contexts/auth";
import { TwoFactorVerificationDto } from "@mug/services/dtos";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";
import { Loader2, AlertCircle, KeyRound } from "lucide-react";

interface TwoFactorFormProps {
  returnUrl?: string;
}

export function TwoFactorForm({ returnUrl }: TwoFactorFormProps = {}) {
  const { t } = useTranslate();
  const { user, verifyTwoFactor, error, clearError, isLoading } = useAuth();
  const [code, setCode] = useState("");

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      return;
    }

    clearError();

    // Get the session ID from localStorage
    let sessionId = "current";
    if (typeof window !== "undefined") {
      const sessionData = localStorage.getItem("auth_session");
      if (sessionData) {
        try {
          const { session } = JSON.parse(sessionData);
          sessionId = session.id || "current";
        } catch (error) {
          console.error("Error parsing session data:", error);
        }
      }
    }

    const verification: TwoFactorVerificationDto = {
      userId: user.id,
      method: user.twoFactorMethod || "app",
      code,
      sessionId
    };

    await verifyTwoFactor(verification, returnUrl);
  };

  // Get method name
  const getMethodName = () => {
    if (!user || !user.twoFactorMethod) {
      return t("auth.twoFactorMethods.app");
    }

    return t(`auth.twoFactorMethods.${user.twoFactorMethod}`);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{t("auth.twoFactorVerification")}</CardTitle>
        <CardDescription>
          {t("auth.twoFactorVerificationDescription", { method: getMethodName() })}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Error message */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{t("auth.error")}</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="code">{t("auth.verificationCode")}</Label>
            <div className="relative">
              <KeyRound className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="code"
                value={code}
                onChange={(e) => setCode(e.target.value.replace(/[^0-9]/g, '').substring(0, 6))}
                placeholder={t("auth.verificationCodePlaceholder")}
                className="pl-10 text-center tracking-widest text-lg font-mono"
                maxLength={6}
                inputMode="numeric"
                pattern="[0-9]*"
                aria-label={t("auth.verificationCode")}
                autoComplete="one-time-code"
                required
              />
            </div>
            <p className="text-sm text-muted-foreground">
              {t("auth.enterCodeFromApp")}
            </p>
          </div>

          <Button type="submit" className="w-full" disabled={isLoading || code.length < 6}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("auth.verify")}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
