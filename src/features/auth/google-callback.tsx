"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Loader2 } from "lucide-react";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";

import { useTranslate } from "@mug/contexts/translate";
import { useAuth } from "@mug/contexts/auth";

interface GoogleCallbackProps {
  returnUrl?: string;
}
export function GoogleCallback(props: GoogleCallbackProps) {
  const { returnUrl: propReturnUrl } = props;
  const { t } = useTranslate();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { handleGoogleCallback } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [autoProcessed, setAutoProcessed] = useState(false);

  const processCode = useCallback(async () => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);

      const code = searchParams?.get("code");
      const state = searchParams?.get("state");
      const urlReturnUrl = searchParams?.get("returnUrl") || undefined;
      
      const finalReturnUrl = propReturnUrl || urlReturnUrl;

      if (!code) {
        setIsProcessing(false);
        return;
      }

      await handleGoogleCallback(code, state || null, finalReturnUrl);
    } finally {
      setIsProcessing(false);
    }
  }, [searchParams, handleGoogleCallback, isProcessing, propReturnUrl]);

  useEffect(() => {
    if (
      !autoProcessed &&
      searchParams?.get("code") &&
      !isProcessing 
        ) {
      setAutoProcessed(true);
      processCode();
    }
  }, [searchParams, isProcessing, autoProcessed, processCode]);

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>{t("auth.processingGoogleLogin")}</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-center text-muted-foreground">
          {t("auth.waitingForAuthentication")}
        </p>

        <div className="mt-4 flex justify-center space-x-4">
          <Button variant="outline" onClick={() => router.push("/auth/login")}>
            {t("auth.backToLogin")}
          </Button>
          <Button onClick={processCode} disabled={isProcessing}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("auth.processing")}
              </>
            ) : (
              t("auth.processAuthentication")
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
