"use client";

import Link from "next/link";
import { Mail, ArrowLeft } from "lucide-react";


import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";

import { useTranslate } from "@mug/contexts/translate";

export function MagicLinkSent() {
  const { t } = useTranslate();

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("auth.checkYourEmail")}</CardTitle>
        <CardDescription>{t("auth.magicLinkSentDescription")}</CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center py-6">
        <div className="rounded-full bg-primary/10 p-6 mb-4">
          <Mail className="h-12 w-12 text-primary" />
        </div>
        <p className="text-center text-muted-foreground">
          {t("auth.magicLinkSentInstructions")}
        </p>
      </CardContent>
      <CardFooter className="flex justify-center">
        <Button variant="outline" asChild>
          <Link href="/auth/login">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("auth.backToLogin")}
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
