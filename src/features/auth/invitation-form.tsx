"use client";

import { useState, useEffect } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useAuth } from "@mug/contexts/auth";
import { InvitationAcceptance } from "@mug/models/auth";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";
import { Progress } from "@mug/components/ui/progress";
import { Loader2, AlertCircle, User, Lock, Check, X } from "lucide-react";
import { findInvitationByToken } from "@mug/constants/mock-auth";
import { UserInvitation } from "@mug/models/auth";

interface InvitationFormProps {
  token: string;
}

export function InvitationForm({ token }: InvitationFormProps) {
  const { t } = useTranslate();
  const { acceptInvitation, error, clearError, isLoading } = useAuth();
  const [invitation, setInvitation] = useState<UserInvitation | null>(null);
  const [invitationLoading, setInvitationLoading] = useState(true);
  const [invitationError, setInvitationError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<InvitationAcceptance>({
    token,
    name: "",
    password: "",
    confirmPassword: ""
  });
  
  // Password strength
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [passwordChecks, setPasswordChecks] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false
  });
  
  // Load invitation details
  useEffect(() => {
    const loadInvitation = async () => {
      setInvitationLoading(true);
      
      try {
        // In a real app, this would be an API call
        const invitation = findInvitationByToken(token);
        
        if (!invitation) {
          setInvitationError(t("auth.invalidOrExpiredInvitation"));
          return;
        }
        
        setInvitation(invitation);
        
        // Pre-fill name if available
        if (invitation.name) {
          setFormData(prev => ({ ...prev, name: invitation.name || "" }));
        }
      } catch (error) {
        console.error("Error loading invitation:", error);
        setInvitationError(t("auth.errorLoadingInvitation"));
      } finally {
        setInvitationLoading(false);
      }
    };
    
    loadInvitation();
  }, [token, t]);
  
  // Handle password change
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    setFormData({ ...formData, password });
    
    // Check password strength
    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[^A-Za-z0-9]/.test(password)
    };
    
    setPasswordChecks(checks);
    
    // Calculate strength
    const strength = Object.values(checks).filter(Boolean).length * 20;
    setPasswordStrength(strength);
  };
  
  // Get strength color
  const getStrengthColor = () => {
    if (passwordStrength <= 20) return "bg-red-500";
    if (passwordStrength <= 40) return "bg-orange-500";
    if (passwordStrength <= 60) return "bg-yellow-500";
    if (passwordStrength <= 80) return "bg-green-500";
    return "bg-green-600";
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    
    if (formData.password !== formData.confirmPassword) {
      // Show error
      return;
    }
    
    await acceptInvitation(formData);
  };
  
  // Show loading state
  if (invitationLoading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="flex items-center justify-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }
  
  // Show error state
  if (invitationError) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle>{t("auth.invitation")}</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{t("auth.error")}</AlertTitle>
            <AlertDescription>{invitationError}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{t("auth.acceptInvitation")}</CardTitle>
        <CardDescription>
          {t("auth.acceptInvitationDescription", { email: invitation?.email })}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Error message */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{t("auth.error")}</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Name */}
          <div className="space-y-2">
            <Label htmlFor="name">{t("auth.name")}</Label>
            <div className="relative">
              <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="name"
                placeholder={t("auth.namePlaceholder")}
                className="pl-10"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>
          </div>
          
          {/* Password */}
          <div className="space-y-2">
            <Label htmlFor="password">{t("auth.password")}</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="password"
                type="password"
                placeholder={t("auth.passwordPlaceholder")}
                className="pl-10"
                value={formData.password}
                onChange={handlePasswordChange}
                required
              />
            </div>
            
            {/* Password strength */}
            {formData.password && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {t("auth.passwordStrength")}
                  </span>
                  <span className="text-sm font-medium">
                    {passwordStrength <= 20
                      ? t("auth.passwordStrengths.veryWeak")
                      : passwordStrength <= 40
                      ? t("auth.passwordStrengths.weak")
                      : passwordStrength <= 60
                      ? t("auth.passwordStrengths.fair")
                      : passwordStrength <= 80
                      ? t("auth.passwordStrengths.good")
                      : t("auth.passwordStrengths.strong")}
                  </span>
                </div>
                <Progress value={passwordStrength} className={getStrengthColor()} />
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-2">
                  <div className="flex items-center text-sm">
                    {passwordChecks.length ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.length")}
                  </div>
                  <div className="flex items-center text-sm">
                    {passwordChecks.uppercase ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.uppercase")}
                  </div>
                  <div className="flex items-center text-sm">
                    {passwordChecks.lowercase ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.lowercase")}
                  </div>
                  <div className="flex items-center text-sm">
                    {passwordChecks.number ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.number")}
                  </div>
                  <div className="flex items-center text-sm">
                    {passwordChecks.special ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.special")}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Confirm Password */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">{t("auth.confirmPassword")}</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="confirmPassword"
                type="password"
                placeholder={t("auth.confirmPasswordPlaceholder")}
                className="pl-10"
                value={formData.confirmPassword}
                onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                required
              />
            </div>
            
            {/* Password match error */}
            {formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword && (
              <p className="text-sm text-destructive">
                {t("auth.passwordsDoNotMatch")}
              </p>
            )}
          </div>
          
          <Button 
            type="submit" 
            className="w-full" 
            disabled={
              isLoading || 
              !formData.name ||
              passwordStrength < 60 || 
              formData.password !== formData.confirmPassword
            }
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("auth.createAccount")}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
