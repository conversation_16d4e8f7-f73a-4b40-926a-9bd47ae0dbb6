"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslate } from "@mug/contexts/translate";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";
import { ShieldAlert, ShieldCheck } from "lucide-react";

interface TwoFactorReminderProps {
  onSetupNow: () => void;
  onRemindLater: () => void;
}

export function TwoFactorReminder({ onSetupNow, onRemindLater }: TwoFactorReminderProps) {
  const { t } = useTranslate();
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSetupNow = () => {
    setIsLoading(true);
    onSetupNow();
  };

  const handleRemindLater = () => {
    setIsLoading(true);
    onRemindLater();
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <div className="flex items-center gap-2">
          <ShieldAlert className="h-6 w-6 text-warning" />
          <CardTitle>{t("auth.twoFactorReminder")}</CardTitle>
        </div>
        <CardDescription>
          {t("auth.twoFactorReminderDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-muted p-4 rounded-md">
          <div className="flex items-start gap-3">
            <ShieldCheck className="h-5 w-5 text-primary mt-0.5" />
            <div>
              <h4 className="font-medium">{t("auth.whyUse2FA")}</h4>
              <p className="text-sm text-muted-foreground mt-1">
                {t("auth.whyUse2FADescription")}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row gap-2">
        <Button
          variant="outline"
          onClick={handleRemindLater}
          disabled={isLoading}
          className="w-full sm:w-auto"
        >
          {t("auth.remindLater")}
        </Button>
        <Button
          onClick={handleSetupNow}
          disabled={isLoading}
          className="w-full sm:w-auto"
        >
          {t("auth.setupNow")}
        </Button>
      </CardFooter>
    </Card>
  );
}
