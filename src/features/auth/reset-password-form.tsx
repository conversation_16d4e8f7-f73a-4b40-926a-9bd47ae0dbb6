"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useAuth } from "@mug/contexts/auth";
import { PasswordResetCompletion } from "@mug/models/auth";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@mug/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";
import { Progress } from "@mug/components/ui/progress";
import { Loader2, AlertCircle, Lock, Check, X } from "lucide-react";

interface ResetPasswordFormProps {
  token: string;
}

export function ResetPasswordForm({ token }: ResetPasswordFormProps) {
  const { t } = useTranslate();
  const { resetPassword, error, clearError, isLoading } = useAuth();
  const [formData, setFormData] = useState<PasswordResetCompletion>({
    token,
    newPassword: "",
    confirmPassword: ""
  });

  // Password strength
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [passwordChecks, setPasswordChecks] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false
  });

  // Handle password change
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    setFormData({ ...formData, newPassword: password });

    // Check password strength
    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[^A-Za-z0-9]/.test(password)
    };

    setPasswordChecks(checks);

    // Calculate strength
    const strength = Object.values(checks).filter(Boolean).length * 20;
    setPasswordStrength(strength);
  };

  // Get strength color
  const getStrengthColor = () => {
    if (passwordStrength <= 20) return "bg-red-500";
    if (passwordStrength <= 40) return "bg-orange-500";
    if (passwordStrength <= 60) return "bg-yellow-500";
    if (passwordStrength <= 80) return "bg-green-500";
    return "bg-green-600";
  };

  // Local error state for form validation
  const [formError, setFormError] = useState<string | null>(null);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    setFormError(null);

    if (formData.newPassword !== formData.confirmPassword) {
      setFormError(t("auth.passwordsDoNotMatch"));
      return;
    }

    if (passwordStrength < 60) {
      setFormError(t("auth.passwordTooWeak"));
      return;
    }

    await resetPassword(formData);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{t("auth.resetPassword")}</CardTitle>
        <CardDescription>{t("auth.resetPasswordDescription")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Error messages */}
        {(error || formError) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{t("auth.error")}</AlertTitle>
            <AlertDescription>{error || formError}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* New Password */}
          <div className="space-y-2">
            <Label htmlFor="newPassword">{t("auth.newPassword")}</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="newPassword"
                type="password"
                placeholder={t("auth.newPasswordPlaceholder")}
                className="pl-10"
                value={formData.newPassword}
                onChange={handlePasswordChange}
                required
              />
            </div>

            {/* Password strength */}
            {formData.newPassword && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {t("auth.passwordStrength")}
                  </span>
                  <span className="text-sm font-medium">
                    {passwordStrength <= 20
                      ? t("auth.passwordStrengths.veryWeak")
                      : passwordStrength <= 40
                      ? t("auth.passwordStrengths.weak")
                      : passwordStrength <= 60
                      ? t("auth.passwordStrengths.fair")
                      : passwordStrength <= 80
                      ? t("auth.passwordStrengths.good")
                      : t("auth.passwordStrengths.strong")}
                  </span>
                </div>
                <Progress value={passwordStrength} className={getStrengthColor()} />

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-2">
                  <div className="flex items-center text-sm">
                    {passwordChecks.length ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.length")}
                  </div>
                  <div className="flex items-center text-sm">
                    {passwordChecks.uppercase ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.uppercase")}
                  </div>
                  <div className="flex items-center text-sm">
                    {passwordChecks.lowercase ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.lowercase")}
                  </div>
                  <div className="flex items-center text-sm">
                    {passwordChecks.number ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.number")}
                  </div>
                  <div className="flex items-center text-sm">
                    {passwordChecks.special ? (
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 mr-2 text-red-500" />
                    )}
                    {t("auth.passwordRequirements.special")}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">{t("auth.confirmPassword")}</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="confirmPassword"
                type="password"
                placeholder={t("auth.confirmPasswordPlaceholder")}
                className="pl-10"
                value={formData.confirmPassword}
                onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                required
              />
            </div>

            {/* Password match error */}
            {formData.newPassword && formData.confirmPassword && formData.newPassword !== formData.confirmPassword && (
              <p className="text-sm text-destructive">
                {t("auth.passwordsDoNotMatch")}
              </p>
            )}
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={
              isLoading ||
              passwordStrength < 60 ||
              formData.newPassword !== formData.confirmPassword
            }
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("auth.resetPassword")}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
