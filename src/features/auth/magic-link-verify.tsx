"use client";

import { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import { Loader2, <PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON>w, AlertCircle } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import { Button } from "@mug/components/ui/button";

import { useTranslate } from "@mug/contexts/translate";
import { useAuth } from "@mug/contexts/auth";

interface MagicLinkVerifyProps {
  token: string;
  returnUrl?: string;
}
export function MagicLinkVerify(props: MagicLinkVerifyProps) {
  const { token, returnUrl } = props;
  const { t } = useTranslate();
  const { verifyMagicLink, error, isLoading, clearError } = useAuth();
  const [verifying, setVerifying] = useState(false);
  const [autoProcessed, setAutoProcessed] = useState(false);

  const processToken = useCallback(async () => {
    if (verifying || isLoading) return;

    try {
      setVerifying(true);
      await verifyMagicLink(token, returnUrl);
    } finally {
      setVerifying(false);
    }
  }, [token, verifyMagicLink, verifying, isLoading, returnUrl]);

  useEffect(() => {
    if (!autoProcessed && !verifying && !isLoading) {
      setAutoProcessed(true);
      processToken();
    }
  }, [autoProcessed, verifying, isLoading, processToken]);

  const handleManualVerification = () => {
    clearError();
    processToken();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("auth.verifyingMagicLink")}</CardTitle>
        <CardDescription>
          {t("auth.verifyingMagicLinkDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center py-6">
        {verifying || isLoading ? (
          <div className="flex flex-col items-center">
            <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
            <p className="text-center text-muted-foreground">
              {t("auth.verifyingMagicLinkWait")}
            </p>
          </div>
        ) : error ? (
          <div className="w-full">
            <div className="rounded-full bg-destructive/10 p-6 mb-4">
              <AlertCircle className="h-12 w-12 text-destructive" />
            </div>
            <p className="text-center text-muted-foreground">
              {t("auth.magicLinkError")}
            </p>
            <div className="flex justify-center mt-4 space-x-4">
              <Button variant="outline" asChild>
                <Link href="/auth/login">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  {t("auth.backToLogin")}
                </Link>
              </Button>
              <Button onClick={handleManualVerification}>
                <RefreshCw className="mr-2 h-4 w-4" />
                {t("auth.tryAgain")}
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <p className="text-center text-muted-foreground">
              {t("auth.redirecting")}
            </p>
            {!isLoading && !verifying && (
              <Button
                onClick={handleManualVerification}
                variant="outline"
                className="mt-4"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                {t("auth.verifyManually")}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
