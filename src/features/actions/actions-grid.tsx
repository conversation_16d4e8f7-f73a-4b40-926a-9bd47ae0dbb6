"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { Action } from "@mug/models/action";
import { ActionCard } from "./action-card";
import { CreateActionModal } from "./components/create-action-modal";
import { EditActionModal } from "./components/edit-action-modal";
import { deleteAction } from "@mug/services/action";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";

interface ActionsGridProps {
  actions: Action[];
  onActionCreated?: () => void;
  onActionUpdated?: () => void;
  onActionDeleted?: () => void;
}

export function ActionsGrid({
  actions,
  onActionCreated,
  onActionUpdated,
  onActionDeleted,
}: ActionsGridProps) {
  const { t } = useTranslate();
  
  // State for modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<Action | null>(null);
  
  // State for delete confirmation
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  // Handle opening the create modal
  const handleOpenCreateModal = () => {
    setIsCreateModalOpen(true);
  };
  
  // Handle opening the edit modal
  const handleOpenEditModal = (action: Action) => {
    setSelectedAction(action);
    setIsEditModalOpen(true);
  };
  
  // Handle opening the delete dialog
  const handleOpenDeleteDialog = (action: Action) => {
    setSelectedAction(action);
    setIsDeleteDialogOpen(true);
  };
  
  // Handle deleting an action
  const handleDeleteAction = async () => {
    if (selectedAction) {
      try {
        await deleteAction(selectedAction.id);
        if (onActionDeleted) {
          onActionDeleted();
        }
      } catch (error) {
        console.error("Error deleting action:", error);
      } finally {
        setIsDeleteDialogOpen(false);
      }
    }
  };
  
  // Handle action creation
  const handleActionCreated = () => {
    setIsCreateModalOpen(false);
    if (onActionCreated) {
      onActionCreated();
    }
  };
  
  // Handle action update
  const handleActionUpdated = () => {
    setIsEditModalOpen(false);
    if (onActionUpdated) {
      onActionUpdated();
    }
  };
  
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {actions.length > 0 ? (
          actions.map((action) => (
            <ActionCard
              key={action.id}
              action={action}
              onView={() => console.log(`View action: ${action.id}`)}
              onEdit={() => handleOpenEditModal(action)}
              onDelete={() => handleOpenDeleteDialog(action)}
            />
          ))
        ) : (
          <div className="col-span-full flex items-center justify-center p-8 border rounded-lg bg-muted/10">
            <p className="text-muted-foreground">{t("actions.noActionsFound")}</p>
          </div>
        )}
      </div>
      
      {/* Create action modal */}
      <CreateActionModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onActionCreated={handleActionCreated}
      />
      
      {/* Edit action modal */}
      {selectedAction && (
        <EditActionModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onActionUpdated={handleActionUpdated}
          action={selectedAction}
        />
      )}
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("actions.deleteConfirmation")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("actions.deleteWarning")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAction}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
