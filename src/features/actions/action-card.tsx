"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Action } from "@mug/models/action";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
  Puzzle, 
  MoreVertical, 
  Eye, 
  Edit, 
  Trash2,
  Mail,
  Bell,
  Webhook,
  Database,
  FileText,
  Globe,
  Code
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import { Badge } from "@mug/components/ui/badge";
import { Button } from "@mug/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";

interface ActionCardProps {
  action: Action;
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export function ActionCard({ action, onView, onEdit, onDelete }: ActionCardProps) {
  const { t } = useTranslate();
  
  // Format the date
  const getFormattedDate = (dateString?: string) => {
    if (!dateString) return "";
    
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };
  
  // Get icon based on action type
  const getActionIcon = () => {
    switch (action.type) {
      case "email":
        return <Mail className="h-4 w-4" />;
      case "notification":
        return <Bell className="h-4 w-4" />;
      case "webhook":
        return <Webhook className="h-4 w-4" />;
      case "api_call":
        return <Globe className="h-4 w-4" />;
      case "database":
        return <Database className="h-4 w-4" />;
      case "file":
        return <FileText className="h-4 w-4" />;
      case "integration":
        return <Puzzle className="h-4 w-4" />;
      case "custom":
        return <Code className="h-4 w-4" />;
      default:
        return <Puzzle className="h-4 w-4" />;
    }
  };
  
  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <Badge 
              variant={action.status ? "default" : "secondary"}
              className="h-6 px-2 text-xs font-medium"
            >
              {action.status ? t("common.active") : t("common.inactive")}
            </Badge>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">{t("common.openMenu")}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={onView}>
                  <Eye className="mr-2 h-4 w-4" />
                  {t("common.view")}
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  {t("common.edit")}
                </DropdownMenuItem>
              )}
              {onDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={onDelete}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    {t("common.delete")}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardTitle className="flex items-center gap-2 mt-2">
          <Puzzle className="h-5 w-5 text-primary" />
          <span className="truncate">{action.name}</span>
        </CardTitle>
        <CardDescription className="line-clamp-2 h-10">
          {action.description || t("actions.noDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-3">
          {/* Type */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <span>{t("actions.type")}</span>
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {getActionIcon()}
              <span>{t(`actions.types.${action.type}`)}</span>
            </div>
          </div>
          
          {/* Category */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <span>{t("actions.category")}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {t(`actions.categories.${action.category}`)}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="pt-2 text-xs text-muted-foreground">
        <div className="w-full flex justify-between items-center">
          <div>
            <Badge variant="outline" className="text-xs font-normal">
              {t(`actions.types.${action.type}`)}
            </Badge>
          </div>
          <div>
            {action.updatedAt && (
              <span>{t("common.updated")} {getFormattedDate(action.updatedAt)}</span>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
