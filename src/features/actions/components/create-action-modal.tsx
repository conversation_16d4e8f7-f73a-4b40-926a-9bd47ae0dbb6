"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@mug/components/ui/dialog";
import { Button } from "@mug/components/ui/button";
import { Check } from "lucide-react";

interface CreateActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onActionCreated: () => void;
}

export function CreateActionModal({
  isOpen,
  onClose,
  onActionCreated,
}: CreateActionModalProps) {
  const { t } = useTranslate();
  
  // This is a placeholder modal - the actual implementation will be done later
  const handleSubmit = () => {
    console.log("Create action submitted");
    onActionCreated();
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] gap-0 flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>{t("actions.createAction")}</DialogTitle>
          <DialogDescription>
            {t("actions.createActionDescription")}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 flex flex-col overflow-auto py-4 px-6">
          <p className="text-center py-8 text-muted-foreground">
            {t("actions.implementationPending")}
          </p>
        </div>
        
        <DialogFooter className="px-6 py-4 border-t">
          <Button variant="outline" onClick={onClose}>
            {t("common.cancel")}
          </Button>
          <Button onClick={handleSubmit}>
            <Check className="h-4 w-4 mr-2" />
            {t("common.create")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
