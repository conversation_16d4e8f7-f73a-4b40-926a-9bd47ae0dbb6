"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Action } from "@mug/models/action";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@mug/components/ui/dialog";
import { But<PERSON> } from "@mug/components/ui/button";
import { Check } from "lucide-react";

interface EditActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onActionUpdated: () => void;
  action: Action;
}

export function EditActionModal({
  isOpen,
  onClose,
  onActionUpdated,
  action,
}: EditActionModalProps) {
  const { t } = useTranslate();
  
  // This is a placeholder modal - the actual implementation will be done later
  const handleSubmit = () => {
    console.log(`Edit action submitted for action: ${action.id}`);
    onActionUpdated();
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] gap-0 flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>{t("actions.editAction")}</DialogTitle>
          <DialogDescription>
            {t("actions.editActionDescription")}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 flex flex-col overflow-auto py-4 px-6">
          <p className="text-center py-8 text-muted-foreground">
            {t("actions.implementationPending")}
          </p>
        </div>
        
        <DialogFooter className="px-6 py-4 border-t">
          <Button variant="outline" onClick={onClose}>
            {t("common.cancel")}
          </Button>
          <Button onClick={handleSubmit}>
            <Check className="h-4 w-4 mr-2" />
            {t("common.save")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
