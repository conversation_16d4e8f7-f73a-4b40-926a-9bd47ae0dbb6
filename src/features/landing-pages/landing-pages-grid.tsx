"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { LandingPage } from "@mug/models/landing-page";
import { LandingPageCard } from "./landing-page-card";
import { CreateLandingPageModal } from "./components/create-landing-page-modal";
import { EditLandingPageModal } from "./components/edit-landing-page-modal";
import { deleteLandingPage } from "@mug/services/landing-page";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";

interface LandingPagesGridProps {
  landingPages: LandingPage[];
  onLandingPageCreated?: () => void;
  onLandingPageUpdated?: () => void;
  onLandingPageDeleted?: () => void;
}

export function LandingPagesGrid({
  landingPages,
  onLandingPageCreated,
  onLandingPageUpdated,
  onLandingPageDeleted,
}: LandingPagesGridProps) {
  const { t } = useTranslate();
  
  // State for modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedLandingPage, setSelectedLandingPage] = useState<LandingPage | null>(null);
  
  // State for delete confirmation
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  // Handle opening the create modal
  const handleOpenCreateModal = () => {
    setIsCreateModalOpen(true);
  };
  
  // Handle opening the edit modal
  const handleOpenEditModal = (landingPage: LandingPage) => {
    setSelectedLandingPage(landingPage);
    setIsEditModalOpen(true);
  };
  
  // Handle opening the delete dialog
  const handleOpenDeleteDialog = (landingPage: LandingPage) => {
    setSelectedLandingPage(landingPage);
    setIsDeleteDialogOpen(true);
  };
  
  // Handle deleting a landing page
  const handleDeleteLandingPage = async () => {
    if (selectedLandingPage) {
      try {
        await deleteLandingPage(selectedLandingPage.id);
        if (onLandingPageDeleted) {
          onLandingPageDeleted();
        }
      } catch (error) {
        console.error("Error deleting landing page:", error);
      } finally {
        setIsDeleteDialogOpen(false);
      }
    }
  };
  
  // Handle landing page creation
  const handleLandingPageCreated = () => {
    setIsCreateModalOpen(false);
    if (onLandingPageCreated) {
      onLandingPageCreated();
    }
  };
  
  // Handle landing page update
  const handleLandingPageUpdated = () => {
    setIsEditModalOpen(false);
    if (onLandingPageUpdated) {
      onLandingPageUpdated();
    }
  };
  
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {landingPages.length > 0 ? (
          landingPages.map((landingPage) => (
            <LandingPageCard
              key={landingPage.id}
              landingPage={landingPage}
              onView={() => console.log(`View landing page: ${landingPage.id}`)}
              onEdit={() => handleOpenEditModal(landingPage)}
              onDelete={() => handleOpenDeleteDialog(landingPage)}
            />
          ))
        ) : (
          <div className="col-span-full flex items-center justify-center p-8 border rounded-lg bg-muted/10">
            <p className="text-muted-foreground">{t("landingPages.noLandingPagesFound")}</p>
          </div>
        )}
      </div>
      
      {/* Create landing page modal */}
      <CreateLandingPageModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onLandingPageCreated={handleLandingPageCreated}
      />
      
      {/* Edit landing page modal */}
      {selectedLandingPage && (
        <EditLandingPageModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onLandingPageUpdated={handleLandingPageUpdated}
          landingPage={selectedLandingPage}
        />
      )}
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("landingPages.deleteConfirmation")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("landingPages.deleteWarning")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteLandingPage}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
