"use client";

import { useTranslate } from "@mug/contexts/translate";
import { LandingPage } from "@mug/models/landing-page";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Layout,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  ExternalLink,
  Copy,
  FileCode
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import { Badge } from "@mug/components/ui/badge";
import { Button } from "@mug/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@mug/components/ui/tooltip";
import { toast } from "sonner";

interface LandingPageCardProps {
  landingPage: LandingPage;
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export function LandingPageCard({ landingPage, onView, onEdit, onDelete }: LandingPageCardProps) {
  const { t } = useTranslate();

  // Format the date
  const getFormattedDate = (dateString?: string) => {
    if (!dateString) return "";

    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };

  // Get conversion mode label
  const getConversionModeLabel = (mode: string) => {
    return t(`landingPages.conversionModes.${mode}`);
  };

  // Get template label
  const getTemplateLabel = (template?: string) => {
    if (!template) return t("landingPages.customTemplate");
    return t(`landingPages.templates.${template}`);
  };

  // Handle copy URL
  const handleCopyUrl = () => {
    const url = `${window.location.origin}/${landingPage.slug}`;
    navigator.clipboard.writeText(url);
    toast.success(t("landingPages.urlCopied"));
  };

  // Handle preview
  const handlePreview = () => {
    window.open(`${window.location.origin}/${landingPage.slug}`, "_blank");
  };

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <Badge
              variant={landingPage.status ? "default" : "secondary"}
              className="h-6 px-2 text-xs font-medium"
            >
              {landingPage.status ? t("common.published") : t("common.draft")}
            </Badge>
            {landingPage.template && (
              <Badge variant="outline" className="h-6 px-2 text-xs font-normal">
                {getTemplateLabel(landingPage.template)}
              </Badge>
            )}
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">{t("common.openMenu")}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={onView}>
                  <Eye className="mr-2 h-4 w-4" />
                  {t("common.view")}
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  {t("common.edit")}
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={handlePreview}>
                <ExternalLink className="mr-2 h-4 w-4" />
                {t("landingPages.preview")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCopyUrl}>
                <Copy className="mr-2 h-4 w-4" />
                {t("landingPages.copyUrl")}
              </DropdownMenuItem>
              {onDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={onDelete}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    {t("common.delete")}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardTitle className="flex items-center gap-2 mt-2">
          <Layout className="h-5 w-5 text-primary" />
          <span className="truncate">{landingPage.name}</span>
        </CardTitle>
        <CardDescription className="line-clamp-2 h-10">
          {landingPage.description || t("landingPages.noDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-3">
          {/* URL */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <span>{t("landingPages.url")}</span>
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <code className="bg-muted px-1 py-0.5 rounded text-xs">
                /{landingPage.slug}
              </code>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5"
                onClick={handleCopyUrl}
              >
                <Copy className="h-3 w-3" />
                <span className="sr-only">{t("landingPages.copyUrl")}</span>
              </Button>
            </div>
          </div>

          {/* Blocks */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <span>{t("landingPages.blocks")}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {t("landingPages.blocksCount", { count: landingPage.blocks.length })}
            </div>
          </div>

          {/* Lane */}
          {landingPage.laneId && (
            <div className="space-y-1">
              <div className="text-sm font-medium flex items-center gap-1">
                <span>{t("landingPages.lane")}</span>
              </div>
              <div className="text-xs text-muted-foreground">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="cursor-help underline dotted">
                        {t("landingPages.linkedToLane")}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("landingPages.laneTooltip")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-2 text-xs text-muted-foreground">
        <div className="w-full flex justify-between items-center">
          <div>
            <Badge variant="outline" className="text-xs font-normal">
              {getConversionModeLabel(landingPage.conversionMode)}
            </Badge>
          </div>
          <div>
            {landingPage.updatedAt && (
              <span>{t("common.updated")} {getFormattedDate(landingPage.updatedAt)}</span>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
