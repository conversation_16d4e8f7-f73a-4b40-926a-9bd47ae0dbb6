"use client";

import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@mug/components/ui/card";
import {
  Layout,
  Users,
  Network,
  Puzzle,
  Zap,
  Bot,
  BarChart3,
  LucideIcon,
} from "lucide-react";

type IconType =
  | "layout"
  | "users"
  | "network"
  | "puzzle"
  | "zap"
  | "bot"
  | "barchart";

interface UnderDevelopmentProps {
  title: string;
  description: string;
  comingSoonText?: string;
  detailText?: string;
  iconType?: IconType;
}

export function UnderDevelopment(props: UnderDevelopmentProps) {
  const {
    title,
    description,
    comingSoonText = "Em breve",
    detailText = "Esta funcionalidade está em desenvolvimento e estará disponível em breve.",
    iconType = "layout",
  } = props;

  // Mapeamento de tipos de ícones para componentes
  const iconMap: Record<IconType, LucideIcon> = {
    layout: Layout,
    users: Users,
    network: Network,
    puzzle: Puzzle,
    zap: Zap,
    bot: <PERSON><PERSON>,
    barchart: BarChart3,
  };

  const Icon = iconMap[iconType];

  return (
    <Card className="h-full w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="flex h-full flex-col items-center justify-center py-10">
        <Icon className="h-16 w-16 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">{comingSoonText}</h3>
        <p className="text-muted-foreground text-center max-w-md">
          {detailText}
        </p>
      </CardContent>
    </Card>
  );
}
