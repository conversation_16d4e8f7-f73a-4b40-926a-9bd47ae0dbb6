"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Form } from "@mug/models/form";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  FileText,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  ExternalLink,
  Copy,
  ListChecks
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import { Badge } from "@mug/components/ui/badge";
import { Button } from "@mug/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@mug/components/ui/tooltip";
import { toast } from "sonner";

interface FormCardProps {
  form: Form;
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export function FormCard({ form, onView, onEdit, onDelete }: FormCardProps) {
  const { t } = useTranslate();

  // Format the date
  const getFormattedDate = (dateString?: string) => {
    if (!dateString) return "";

    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };

  // Get conversion mode label
  const getConversionModeLabel = (mode: string) => {
    return t(`forms.conversionModes.${mode}`);
  };

  // Get template label
  const getTemplateLabel = (template?: string) => {
    if (!template) return t("forms.customTemplate");
    return t(`forms.templates.${template}`);
  };

  // Handle copy URL
  const handleCopyUrl = () => {
    const url = `${window.location.origin}/forms/${form.slug}`;
    navigator.clipboard.writeText(url);
    toast.success(t("forms.urlCopied"));
  };

  // Handle preview
  const handlePreview = () => {
    window.open(`${window.location.origin}/forms/${form.slug}`, "_blank");
  };

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <Badge
              variant={form.status ? "default" : "secondary"}
              className="h-6 px-2 text-xs font-medium"
            >
              {form.status ? t("common.published") : t("common.draft")}
            </Badge>
            {form.template && (
              <Badge variant="outline" className="h-6 px-2 text-xs font-normal">
                {getTemplateLabel(form.template)}
              </Badge>
            )}
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">{t("common.openMenu")}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={onView}>
                  <Eye className="mr-2 h-4 w-4" />
                  {t("common.view")}
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  {t("common.edit")}
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={handlePreview}>
                <ExternalLink className="mr-2 h-4 w-4" />
                {t("forms.preview")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCopyUrl}>
                <Copy className="mr-2 h-4 w-4" />
                {t("forms.copyUrl")}
              </DropdownMenuItem>
              {onDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={onDelete}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    {t("common.delete")}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardTitle className="flex items-center gap-2 mt-2">
          <FileText className="h-5 w-5 text-primary" />
          <span className="truncate">{form.name}</span>
        </CardTitle>
        <CardDescription className="line-clamp-2 h-10">
          {form.description || t("forms.noDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-3">
          {/* URL */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <span>{t("forms.url")}</span>
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <code className="bg-muted px-1 py-0.5 rounded text-xs">
                /forms/{form.slug}
              </code>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5"
                onClick={handleCopyUrl}
              >
                <Copy className="h-3 w-3" />
                <span className="sr-only">{t("forms.copyUrl")}</span>
              </Button>
            </div>
          </div>

          {/* Fields */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <span>{t("forms.fields")}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {t("forms.fieldsCount", { count: form.fields.length })}
            </div>
          </div>

          {/* Lane */}
          {form.laneId && (
            <div className="space-y-1">
              <div className="text-sm font-medium flex items-center gap-1">
                <span>{t("forms.lane")}</span>
              </div>
              <div className="text-xs text-muted-foreground">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="cursor-help underline dotted">
                        {t("forms.linkedToLane")}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("forms.laneTooltip")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-2 text-xs text-muted-foreground">
        <div className="w-full flex justify-between items-center">
          <div>
            <Badge variant="outline" className="text-xs font-normal">
              {getConversionModeLabel(form.conversionMode)}
            </Badge>
          </div>
          <div>
            {form.updatedAt && (
              <span>{t("common.updated")} {getFormattedDate(form.updatedAt)}</span>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
