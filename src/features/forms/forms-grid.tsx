"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { Form } from "@mug/models/form";
import { FormCard } from "./form-card";
import { CreateFormModal } from "./components/create-form-modal";
import { EditFormModal } from "./components/edit-form-modal";
import { deleteForm } from "@mug/services/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";

interface FormsGridProps {
  forms: Form[];
  onFormCreated?: () => void;
  onFormUpdated?: () => void;
  onFormDeleted?: () => void;
}

export function FormsGrid({
  forms,
  onFormCreated,
  onFormUpdated,
  onFormDeleted,
}: FormsGridProps) {
  const { t } = useTranslate();
  
  // State for modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedForm, setSelectedForm] = useState<Form | null>(null);
  
  // State for delete confirmation
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  // Handle opening the create modal
  const handleOpenCreateModal = () => {
    setIsCreateModalOpen(true);
  };
  
  // Handle opening the edit modal
  const handleOpenEditModal = (form: Form) => {
    setSelectedForm(form);
    setIsEditModalOpen(true);
  };
  
  // Handle opening the delete dialog
  const handleOpenDeleteDialog = (form: Form) => {
    setSelectedForm(form);
    setIsDeleteDialogOpen(true);
  };
  
  // Handle deleting a form
  const handleDeleteForm = async () => {
    if (selectedForm) {
      try {
        await deleteForm(selectedForm.id);
        if (onFormDeleted) {
          onFormDeleted();
        }
      } catch (error) {
        console.error("Error deleting form:", error);
      } finally {
        setIsDeleteDialogOpen(false);
      }
    }
  };
  
  // Handle form creation
  const handleFormCreated = () => {
    setIsCreateModalOpen(false);
    if (onFormCreated) {
      onFormCreated();
    }
  };
  
  // Handle form update
  const handleFormUpdated = () => {
    setIsEditModalOpen(false);
    if (onFormUpdated) {
      onFormUpdated();
    }
  };
  
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {forms.length > 0 ? (
          forms.map((form) => (
            <FormCard
              key={form.id}
              form={form}
              onView={() => console.log(`View form: ${form.id}`)}
              onEdit={() => handleOpenEditModal(form)}
              onDelete={() => handleOpenDeleteDialog(form)}
            />
          ))
        ) : (
          <div className="col-span-full flex items-center justify-center p-8 border rounded-lg bg-muted/10">
            <p className="text-muted-foreground">{t("forms.noFormsFound")}</p>
          </div>
        )}
      </div>
      
      {/* Create form modal */}
      <CreateFormModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onFormCreated={handleFormCreated}
      />
      
      {/* Edit form modal */}
      {selectedForm && (
        <EditFormModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onFormUpdated={handleFormUpdated}
          form={selectedForm}
        />
      )}
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("forms.deleteConfirmation")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("forms.deleteWarning")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteForm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
