"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Form } from "@mug/models/form";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@mug/components/ui/dialog";
import { But<PERSON> } from "@mug/components/ui/button";
import { Check } from "lucide-react";

interface EditFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onFormUpdated: () => void;
  form: Form;
}

export function EditFormModal({
  isOpen,
  onClose,
  onFormUpdated,
  form,
}: EditFormModalProps) {
  const { t } = useTranslate();
  
  // This is a placeholder modal - the actual implementation will be done later
  const handleSubmit = () => {
    console.log(`Edit form submitted for form: ${form.id}`);
    onFormUpdated();
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] gap-0 flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>{t("forms.editForm")}</DialogTitle>
          <DialogDescription>
            {t("forms.editFormDescription")}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 flex flex-col overflow-auto py-4 px-6">
          <p className="text-center py-8 text-muted-foreground">
            {t("forms.implementationPending")}
          </p>
        </div>
        
        <DialogFooter className="px-6 py-4 border-t">
          <Button variant="outline" onClick={onClose}>
            {t("common.cancel")}
          </Button>
          <Button onClick={handleSubmit}>
            <Check className="h-4 w-4 mr-2" />
            {t("common.save")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
