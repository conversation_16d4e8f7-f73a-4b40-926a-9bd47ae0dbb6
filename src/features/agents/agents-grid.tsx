"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { Agent } from "@mug/models/agent";
import { AgentCard } from "./agent-card";
import { CreateAgentModal } from "./components/create-agent-modal";
import { EditAgentModal } from "./components/edit-agent-modal";
import { AgentDetailsModal } from "./components/agent-details-modal";
import { deleteAgent } from "@mug/services/agent";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";

interface AgentsGridProps {
  agents: Agent[];
  onAgentCreated?: () => void;
  onAgentUpdated?: () => void;
  onAgentDeleted?: () => void;
}

export function AgentsGrid({
  agents,
  onAgentCreated,
  onAgentUpdated,
  onAgentDeleted,
}: AgentsGridProps) {
  const { t } = useTranslate();
  
  // State for modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  
  // State for delete confirmation
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  // Handle opening the create modal
  const handleOpenCreateModal = () => {
    setIsCreateModalOpen(true);
  };
  
  // Handle opening the edit modal
  const handleOpenEditModal = (agent: Agent) => {
    setSelectedAgent(agent);
    setIsEditModalOpen(true);
  };
  
  // Handle opening the details modal
  const handleOpenDetailsModal = (agent: Agent) => {
    setSelectedAgent(agent);
    setIsDetailsModalOpen(true);
  };
  
  // Handle opening the delete dialog
  const handleOpenDeleteDialog = (agent: Agent) => {
    setSelectedAgent(agent);
    setIsDeleteDialogOpen(true);
  };
  
  // Handle deleting an agent
  const handleDeleteAgent = async () => {
    if (selectedAgent) {
      try {
        await deleteAgent(selectedAgent.id);
        if (onAgentDeleted) {
          onAgentDeleted();
        }
      } catch (error) {
        console.error("Error deleting agent:", error);
      } finally {
        setIsDeleteDialogOpen(false);
      }
    }
  };
  
  // Handle agent creation
  const handleAgentCreated = () => {
    setIsCreateModalOpen(false);
    if (onAgentCreated) {
      onAgentCreated();
    }
  };
  
  // Handle agent update
  const handleAgentUpdated = () => {
    setIsEditModalOpen(false);
    if (onAgentUpdated) {
      onAgentUpdated();
    }
  };
  
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {agents.length > 0 ? (
          agents.map((agent) => (
            <AgentCard
              key={agent.id}
              agent={agent}
              onView={() => handleOpenDetailsModal(agent)}
              onEdit={() => handleOpenEditModal(agent)}
              onDelete={() => handleOpenDeleteDialog(agent)}
            />
          ))
        ) : (
          <div className="col-span-full flex items-center justify-center p-8 border rounded-lg bg-muted/10">
            <p className="text-muted-foreground">{t("agents.noAgentsFound")}</p>
          </div>
        )}
      </div>
      
      {/* Create agent modal */}
      <CreateAgentModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onAgentCreated={handleAgentCreated}
      />
      
      {/* Edit agent modal */}
      {selectedAgent && (
        <EditAgentModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onAgentUpdated={handleAgentUpdated}
          agent={selectedAgent}
        />
      )}
      
      {/* Agent details modal */}
      {selectedAgent && (
        <AgentDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={() => setIsDetailsModalOpen(false)}
          agent={selectedAgent}
          onEdit={() => {
            setIsDetailsModalOpen(false);
            setIsEditModalOpen(true);
          }}
        />
      )}
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("agents.deleteConfirmation")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("agents.deleteWarning")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAgent}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
