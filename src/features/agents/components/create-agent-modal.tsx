"use client";

// React and hooks
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslate } from "@mug/contexts/translate";

// Form validation
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

// Models and types
import { AgentModelType, AgentTemplate } from "@mug/models/agent";
import { CreateAgentDTO } from "@mug/services/agent";

// Services
import { createAgent, getTemplate, getAvailableActions } from "@mug/services/agent";

// Icons
import { Bot, Check, HelpCircle, Plus, Trash2, X } from "lucide-react";

// UI Components - Dialog
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";

// UI Components - Form
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@mug/components/ui/form";
import { Input } from "@mug/components/ui/input";
import { Button } from "@mug/components/ui/button";
import { Textarea } from "@mug/components/ui/textarea";
import { Switch } from "@mug/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@mug/components/ui/tooltip";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";
import { Badge } from "@mug/components/ui/badge";
import { ScrollArea } from "@mug/components/ui/scroll-area";
import { Separator } from "@mug/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@mug/components/ui/alert";

interface CreateAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAgentCreated: () => void;
}

// Validation schema for the form
const formSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  description: z.string().optional(),
  actions: z.array(z.string()).default([]),
  rules: z.array(z.string()).default([]),
  model: z.nativeEnum(AgentModelType),
  template: z.string().optional() as z.ZodType<AgentTemplate | undefined>,
  status: z.boolean().default(true),
});

// Type for form values
type FormValues = z.infer<typeof formSchema>;

export function CreateAgentModal({
  isOpen,
  onClose,
  onAgentCreated,
}: CreateAgentModalProps) {
  const { t } = useTranslate();

  // State for available actions
  const [availableActions, setAvailableActions] = useState<any[]>([]);
  const [actionsOpen, setActionsOpen] = useState(false);

  // State for rules
  const [newRule, setNewRule] = useState("");

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      actions: [],
      rules: [],
      model: AgentModelType.GPT35,
      status: true,
    },
  });

  // Load available actions
  useEffect(() => {
    if (isOpen) {
      const loadActions = async () => {
        try {
          const actions = await getAvailableActions();
          setAvailableActions(actions);
        } catch (error) {
          console.error("Error loading actions:", error);
        }
      };

      loadActions();
    }
  }, [isOpen]);

  // Reset form when modal is closed
  useEffect(() => {
    if (!isOpen) {
      form.reset();
      setNewRule("");
    }
  }, [isOpen, form]);

  // Handle template selection
  const handleTemplateChange = async (template: AgentTemplate) => {
    try {
      const templateData = await getTemplate(template);

      // Update form values with template data
      form.setValue("name", templateData.name || "");
      form.setValue("description", templateData.description || "");
      form.setValue("actions", templateData.actions || []);
      form.setValue("rules", templateData.rules || []);
      form.setValue("model", templateData.model || AgentModelType.GPT35);

      // Set the template value
      form.setValue("template", template);
    } catch (error) {
      console.error("Error loading template:", error);
    }
  };

  // Handle adding a new rule
  const handleAddRule = () => {
    if (newRule.trim()) {
      const currentRules = form.getValues("rules");
      form.setValue("rules", [...currentRules, newRule.trim()]);
      setNewRule("");
    }
  };

  // Handle removing a rule
  const handleRemoveRule = (index: number) => {
    const currentRules = form.getValues("rules");
    form.setValue(
      "rules",
      currentRules.filter((_, i) => i !== index)
    );
  };

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      // Prepare data for API
      const agentData: CreateAgentDTO = {
        name: values.name,
        description: values.description,
        actions: values.actions,
        rules: values.rules,
        model: values.model,
        status: values.status,
      };

      // Create agent
      await createAgent(agentData);

      // Notify parent component
      onAgentCreated();
    } catch (error) {
      console.error("Error creating agent:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] gap-0 flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>{t("agents.createAgent")}</DialogTitle>
          <DialogDescription>
            {t("agents.createAgentDescription")}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <div className="flex-1 flex flex-col overflow-auto py-4">
            <form
              id="create-agent-form"
              onSubmit={form.handleSubmit(onSubmit)}
              className="grid grid-cols-8 gap-4 px-6 flex-1"
            >
              {/* Template Selection */}
              <FormField
                control={form.control}
                name="template"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center h-6">
                      <FormLabel>{t("agents.template")}</FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("agents.templateDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select
                      onValueChange={(value) => handleTemplateChange(value as AgentTemplate)}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("agents.selectTemplate")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="empty">{t("agents.templates.empty")}</SelectItem>
                        <SelectItem value="customer-service">{t("agents.templates.customerService")}</SelectItem>
                        <SelectItem value="sales-assistant">{t("agents.templates.salesAssistant")}</SelectItem>
                        <SelectItem value="data-analyst">{t("agents.templates.dataAnalyst")}</SelectItem>
                        <SelectItem value="content-creator">{t("agents.templates.contentCreator")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("common.name")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                    </div>
                    <FormControl>
                      <Input placeholder={t("agents.enterName")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center h-6">
                      <FormLabel>{t("common.description")}</FormLabel>
                    </div>
                    <FormControl>
                      <Textarea
                        placeholder={t("agents.enterDescription")}
                        className="resize-none min-h-24"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Model */}
              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem className="col-span-4">
                    <div className="flex items-center h-6">
                      <FormLabel className="gap-0">
                        {t("agents.model")}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("agents.modelDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("agents.selectModel")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={AgentModelType.GPT35}>GPT-3.5</SelectItem>
                        <SelectItem value={AgentModelType.GPT4}>GPT-4</SelectItem>
                        <SelectItem value={AgentModelType.CLAUDE3_OPUS}>Claude 3 Opus</SelectItem>
                        <SelectItem value={AgentModelType.CLAUDE3_SONNET}>Claude 3 Sonnet</SelectItem>
                        <SelectItem value={AgentModelType.CLAUDE3_HAIKU}>Claude 3 Haiku</SelectItem>
                        <SelectItem value={AgentModelType.GEMINI_PRO}>Gemini Pro</SelectItem>
                        <SelectItem value={AgentModelType.MISTRAL_LARGE}>Mistral Large</SelectItem>
                        <SelectItem value={AgentModelType.MISTRAL_MEDIUM}>Mistral Medium</SelectItem>
                        <SelectItem value={AgentModelType.LLAMA3}>Llama 3</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem className="col-span-4 flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        {t("common.status")}
                      </FormLabel>
                      <FormDescription>
                        {t("agents.statusDescription")}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Actions */}
              <FormField
                control={form.control}
                name="actions"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center h-6">
                      <FormLabel>{t("agents.actions")}</FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("agents.actionsDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Popover open={actionsOpen} onOpenChange={setActionsOpen}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            className="w-full justify-between"
                          >
                            {field.value.length > 0
                              ? t("agents.actionsSelected", { count: field.value.length })
                              : t("agents.selectActions")}
                            <Plus className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0" align="start">
                        <Command>
                          <CommandInput placeholder={t("agents.searchActions")} />
                          <CommandList>
                            <CommandEmpty>{t("agents.noActionsFound")}</CommandEmpty>
                            <CommandGroup>
                              <ScrollArea className="h-72">
                                {availableActions.map((action) => {
                                  const isSelected = field.value.includes(action.id);
                                  return (
                                    <CommandItem
                                      key={action.id}
                                      value={action.id}
                                      onSelect={() => {
                                        const newValue = isSelected
                                          ? field.value.filter((id) => id !== action.id)
                                          : [...field.value, action.id];
                                        form.setValue("actions", newValue);
                                      }}
                                    >
                                      <div className="flex items-center gap-2 w-full">
                                        <div className={`flex h-4 w-4 items-center justify-center rounded-sm border ${
                                          isSelected ? "bg-primary border-primary" : "border-primary"
                                        }`}>
                                          {isSelected && <Check className="h-3 w-3 text-primary-foreground" />}
                                        </div>
                                        <div className="flex flex-col">
                                          <span>{action.name}</span>
                                          <span className="text-xs text-muted-foreground">
                                            {action.description}
                                          </span>
                                        </div>
                                      </div>
                                    </CommandItem>
                                  );
                                })}
                              </ScrollArea>
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    {field.value.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {field.value.map((actionId) => {
                          const action = availableActions.find((a) => a.id === actionId);
                          return (
                            <Badge
                              key={actionId}
                              variant="secondary"
                              className="flex items-center gap-1"
                            >
                              {action ? action.name : actionId}
                              <X
                                className="h-3 w-3 cursor-pointer"
                                onClick={() => {
                                  form.setValue(
                                    "actions",
                                    field.value.filter((id) => id !== actionId)
                                  );
                                }}
                              />
                            </Badge>
                          );
                        })}
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Rules */}
              <FormField
                control={form.control}
                name="rules"
                render={({ field }) => (
                  <FormItem className="col-span-8">
                    <div className="flex items-center h-6">
                      <FormLabel>{t("agents.rules")}</FormLabel>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground opacity-70 ml-2" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3">
                            <p>{t("agents.rulesDescription")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    <div className="flex gap-2">
                      <Input
                        placeholder={t("agents.enterRule")}
                        value={newRule}
                        onChange={(e) => setNewRule(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            handleAddRule();
                          }
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleAddRule}
                        disabled={!newRule.trim()}
                      >
                        <Plus className="h-4 w-4" />
                        <span className="sr-only">{t("common.add")}</span>
                      </Button>
                    </div>

                    {field.value.length > 0 ? (
                      <div className="mt-2 space-y-2">
                        {field.value.map((rule, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 rounded-md border"
                          >
                            <span className="text-sm">{rule}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveRule(index)}
                              className="h-6 w-6"
                            >
                              <X className="h-4 w-4" />
                              <span className="sr-only">{t("common.remove")}</span>
                            </Button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        {t("agents.noRules")}
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </div>
        </Form>

        <DialogFooter className="px-6 py-4 border-t">
          <Button variant="outline" onClick={onClose}>
            {t("common.cancel")}
          </Button>
          <Button type="submit" form="create-agent-form">
            <Check className="h-4 w-4 mr-2" />
            {t("common.create")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
