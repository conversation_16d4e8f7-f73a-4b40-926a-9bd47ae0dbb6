"use client";

// React and hooks
import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";

// Models and types
import { Agent } from "@mug/models/agent";

// Date formatting
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

// Icons
import { 
  Bot, 
  Calendar, 
  Edit, 
  Zap, 
  Scroll,
  CheckCircle2,
  XCircle
} from "lucide-react";

// UI Components - Dialog
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";

// UI Components - Other
import { Button } from "@mug/components/ui/button";
import { Badge } from "@mug/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@mug/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import { getActionById } from "@mug/constants/mock-agents";

interface AgentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  agent: Agent;
  onEdit: () => void;
}

export function AgentDetailsModal({
  isOpen,
  onClose,
  agent,
  onEdit,
}: AgentDetailsModalProps) {
  const { t } = useTranslate();
  const [activeTab, setActiveTab] = useState("overview");
  
  // Format the date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    
    try {
      return format(new Date(dateString), "PPP", { locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };
  
  // Get action details
  const getActionDetails = (actionId: string) => {
    const action = getActionById(actionId);
    return action || { id: actionId, name: actionId, description: "" };
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[800px] w-full max-h-[90vh] gap-0 flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <div className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary" />
            <DialogTitle className="text-xl">{agent.name}</DialogTitle>
          </div>
          <DialogDescription className="flex items-center gap-2 mt-1">
            <Badge 
              variant={agent.status ? "default" : "secondary"}
              className="h-6 px-2 text-xs font-medium"
            >
              {agent.status ? t("common.active") : t("common.inactive")}
            </Badge>
            <Badge variant="outline">{agent.model}</Badge>
          </DialogDescription>
        </DialogHeader>
        
        <div className="overflow-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="p-6">
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="overview">{t("common.overview")}</TabsTrigger>
              <TabsTrigger value="actions">{t("agents.actions")}</TabsTrigger>
              <TabsTrigger value="rules">{t("agents.rules")}</TabsTrigger>
            </TabsList>
            
            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>{t("common.basicInfo")}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Description */}
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">{t("common.description")}</p>
                    <p className="font-medium">
                      {agent.description || t("agents.noDescription")}
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Model */}
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">{t("agents.model")}</p>
                      <Badge variant="outline" className="font-medium">
                        {agent.model}
                      </Badge>
                    </div>
                    
                    {/* Status */}
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">{t("common.status")}</p>
                      <div className="flex items-center gap-2">
                        {agent.status ? (
                          <>
                            <CheckCircle2 className="h-4 w-4 text-success" />
                            <span className="font-medium">{t("common.active")}</span>
                          </>
                        ) : (
                          <>
                            <XCircle className="h-4 w-4 text-destructive" />
                            <span className="font-medium">{t("common.inactive")}</span>
                          </>
                        )}
                      </div>
                    </div>
                    
                    {/* Created At */}
                    {agent.createdAt && (
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">{t("common.createdAt")}</p>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <p className="font-medium">
                            {formatDate(agent.createdAt)}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {/* Updated At */}
                    {agent.updatedAt && (
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">{t("common.updatedAt")}</p>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <p className="font-medium">
                            {formatDate(agent.updatedAt)}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Actions Tab */}
            <TabsContent value="actions" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t("agents.actions")}</CardTitle>
                  <CardDescription>
                    {t("agents.actionsDescription")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {agent.actions.length > 0 ? (
                    <div className="space-y-4">
                      {agent.actions.map((actionId) => {
                        const action = getActionDetails(actionId);
                        return (
                          <div
                            key={actionId}
                            className="flex items-start justify-between p-3 border rounded-md"
                          >
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <Zap className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">{action.name}</span>
                              </div>
                              <p className="text-sm text-muted-foreground">{action.description}</p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {t("agents.noActions")}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Rules Tab */}
            <TabsContent value="rules" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t("agents.rules")}</CardTitle>
                  <CardDescription>
                    {t("agents.rulesDescription")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {agent.rules.length > 0 ? (
                    <div className="space-y-4">
                      {agent.rules.map((rule, index) => (
                        <div
                          key={index}
                          className="flex items-start justify-between p-3 border rounded-md"
                        >
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Scroll className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{rule}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {t("agents.noRules")}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter className="px-6 py-4 border-t">
          <Button variant="outline" onClick={onClose}>
            {t("common.close")}
          </Button>
          <Button onClick={onEdit} className="gap-1">
            <Edit className="h-4 w-4" />
            {t("common.edit")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
