"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Agent } from "@mug/models/agent";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
  Bo<PERSON>, 
  MoreVertical, 
  Eye, 
  Edit, 
  Trash2,
  Zap,
  Scroll
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import { Badge } from "@mug/components/ui/badge";
import { Button } from "@mug/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@mug/components/ui/tooltip";
import { getActionById } from "@mug/constants/mock-agents";

interface AgentCardProps {
  agent: Agent;
  onView: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

export function AgentCard({ agent, onView, onEdit, onDelete }: AgentCardProps) {
  const { t } = useTranslate();
  
  // Format the date
  const getFormattedDate = (dateString?: string) => {
    if (!dateString) return "";
    
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };
  
  // Get action names
  const getActionNames = (actionIds: string[]) => {
    return actionIds.map(id => {
      const action = getActionById(id);
      return action ? action.name : id;
    });
  };
  
  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <Badge 
              variant={agent.status ? "default" : "secondary"}
              className="h-6 px-2 text-xs font-medium"
            >
              {agent.status ? t("common.active") : t("common.inactive")}
            </Badge>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">{t("common.openMenu")}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onView}>
                <Eye className="mr-2 h-4 w-4" />
                {t("common.view")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t("common.edit")}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={onDelete}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {t("common.delete")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardTitle className="flex items-center gap-2 mt-2">
          <Bot className="h-5 w-5 text-primary" />
          <span className="truncate">{agent.name}</span>
        </CardTitle>
        <CardDescription className="line-clamp-2 h-10">
          {agent.description || t("agents.noDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-3">
          {/* Actions */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <Zap className="h-3.5 w-3.5 text-muted-foreground" />
              <span>{t("agents.actions")}</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {agent.actions.length > 0 ? (
                getActionNames(agent.actions).map((actionName, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {actionName}
                  </Badge>
                ))
              ) : (
                <span className="text-xs text-muted-foreground">
                  {t("agents.noActions")}
                </span>
              )}
            </div>
          </div>
          
          {/* Rules */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <Scroll className="h-3.5 w-3.5 text-muted-foreground" />
              <span>{t("agents.rules")}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {agent.rules.length > 0 ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="cursor-help">
                        {t("agents.rulesCount", { count: agent.rules.length })}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-80 p-4">
                      <ul className="list-disc pl-4 space-y-1">
                        {agent.rules.map((rule, index) => (
                          <li key={index} className="text-xs">{rule}</li>
                        ))}
                      </ul>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                t("agents.noRules")
              )}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="pt-2 text-xs text-muted-foreground">
        <div className="w-full flex justify-between items-center">
          <div>
            <Badge variant="outline" className="text-xs font-normal">
              {agent.model}
            </Badge>
          </div>
          <div>
            {agent.updatedAt && (
              <span>{t("common.updated")} {getFormattedDate(agent.updatedAt)}</span>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
