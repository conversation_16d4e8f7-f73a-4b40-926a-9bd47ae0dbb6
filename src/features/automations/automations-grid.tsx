"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { Automation } from "@mug/models/automation";
import { AutomationCard } from "./automation-card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@mug/components/ui/alert-dialog";

interface AutomationsGridProps {
  automations: Automation[];
  onAutomationDeleted?: () => void;
}

export function AutomationsGrid({
  automations,
  onAutomationDeleted,
}: AutomationsGridProps) {
  const { t } = useTranslate();
  
  // State for delete confirmation
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedAutomation, setSelectedAutomation] = useState<Automation | null>(null);
  
  // <PERSON>le opening the delete dialog
  const handleOpenDeleteDialog = (automation: Automation) => {
    setSelectedAutomation(automation);
    setIsDeleteDialogOpen(true);
  };
  
  // <PERSON>le deleting an automation
  const handleDeleteAutomation = async () => {
    if (selectedAutomation) {
      try {
        // In a real implementation, we would call a service to delete the automation
        console.log(`Deleting automation: ${selectedAutomation.id}`);
        
        if (onAutomationDeleted) {
          onAutomationDeleted();
        }
      } catch (error) {
        console.error("Error deleting automation:", error);
      } finally {
        setIsDeleteDialogOpen(false);
      }
    }
  };
  
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {automations.length > 0 ? (
          automations.map((automation) => (
            <AutomationCard
              key={automation.id}
              automation={automation}
              onView={() => console.log(`View automation: ${automation.id}`)}
              onEdit={() => console.log(`Edit automation: ${automation.id}`)}
              onDelete={() => handleOpenDeleteDialog(automation)}
            />
          ))
        ) : (
          <div className="col-span-full flex items-center justify-center p-8 border rounded-lg bg-muted/10">
            <p className="text-muted-foreground">{t("automations.noAutomationsFound")}</p>
          </div>
        )}
      </div>
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("automations.deleteConfirmation")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("automations.deleteWarning")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAutomation}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
