"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Automation } from "@mug/models/automation";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
  Zap, 
  MoreVertical, 
  Eye, 
  Edit, 
  Trash2,
  Play,
  Pause,
  Calendar,
  Webhook,
  FileInput,
  Database,
  Clock,
  AlertCircle
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@mug/components/ui/card";
import { Badge } from "@mug/components/ui/badge";
import { Button } from "@mug/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@mug/components/ui/tooltip";

interface AutomationCardProps {
  automation: Automation;
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export function AutomationCard({ automation, onView, onEdit, onDelete }: AutomationCardProps) {
  const { t } = useTranslate();
  
  // Format the date
  const getFormattedDate = (dateString?: string) => {
    if (!dateString) return "";
    
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: ptBR });
    } catch (error) {
      return dateString;
    }
  };
  
  // Get trigger icon based on type
  const getTriggerIcon = () => {
    switch (automation.trigger.type) {
      case "record_created":
      case "record_updated":
      case "record_deleted":
        return <Database className="h-4 w-4" />;
      case "field_changed":
      case "status_changed":
        return <FileInput className="h-4 w-4" />;
      case "scheduled":
        return <Clock className="h-4 w-4" />;
      case "webhook":
        return <Webhook className="h-4 w-4" />;
      case "form_submitted":
        return <FileInput className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };
  
  // Get trigger description
  const getTriggerDescription = () => {
    switch (automation.trigger.type) {
      case "record_created":
        return t("automations.triggers.recordCreated", { model: automation.trigger.config.model });
      case "record_updated":
        return t("automations.triggers.recordUpdated", { model: automation.trigger.config.model });
      case "record_deleted":
        return t("automations.triggers.recordDeleted", { model: automation.trigger.config.model });
      case "field_changed":
        return t("automations.triggers.fieldChanged", { 
          field: automation.trigger.config.field,
          model: automation.trigger.config.model
        });
      case "status_changed":
        return t("automations.triggers.statusChanged", { model: automation.trigger.config.model });
      case "scheduled":
        return t("automations.triggers.scheduled", { 
          frequency: automation.trigger.config.frequency,
          time: automation.trigger.config.time
        });
      case "webhook":
        return t("automations.triggers.webhook", { endpoint: automation.trigger.config.endpoint });
      case "form_submitted":
        return t("automations.triggers.formSubmitted", { formId: automation.trigger.config.formId });
      default:
        return t("automations.triggers.unknown");
    }
  };
  
  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <Badge 
              variant={automation.status ? "default" : "secondary"}
              className="h-6 px-2 text-xs font-medium"
            >
              {automation.status ? t("common.active") : t("common.inactive")}
            </Badge>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">{t("common.openMenu")}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={onView}>
                  <Eye className="mr-2 h-4 w-4" />
                  {t("common.view")}
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  {t("common.edit")}
                </DropdownMenuItem>
              )}
              <DropdownMenuItem>
                {automation.status ? (
                  <>
                    <Pause className="mr-2 h-4 w-4" />
                    {t("automations.pause")}
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    {t("automations.activate")}
                  </>
                )}
              </DropdownMenuItem>
              {onDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={onDelete}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    {t("common.delete")}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardTitle className="flex items-center gap-2 mt-2">
          <Zap className="h-5 w-5 text-primary" />
          <span className="truncate">{automation.name}</span>
        </CardTitle>
        <CardDescription className="line-clamp-2 h-10">
          {automation.description || t("automations.noDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-3">
          {/* Trigger */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <Play className="h-3.5 w-3.5 text-muted-foreground" />
              <span>{t("automations.trigger")}</span>
            </div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              {getTriggerIcon()}
              <span>{getTriggerDescription()}</span>
            </div>
          </div>
          
          {/* Actions */}
          <div className="space-y-1">
            <div className="text-sm font-medium flex items-center gap-1">
              <Zap className="h-3.5 w-3.5 text-muted-foreground" />
              <span>{t("automations.actions")}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {t("automations.actionsCount", { count: automation.actions.length })}
            </div>
          </div>
          
          {/* Conditions */}
          {automation.conditions && automation.conditions.length > 0 && (
            <div className="space-y-1">
              <div className="text-sm font-medium flex items-center gap-1">
                <AlertCircle className="h-3.5 w-3.5 text-muted-foreground" />
                <span>{t("automations.conditions")}</span>
              </div>
              <div className="text-xs text-muted-foreground">
                {t("automations.conditionsCount", { count: automation.conditions.length })}
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-2 text-xs text-muted-foreground">
        <div className="w-full flex justify-between items-center">
          <div>
            <Badge variant="outline" className="text-xs font-normal">
              {t(`automations.triggerTypes.${automation.trigger.type}`)}
            </Badge>
          </div>
          <div>
            {automation.updatedAt && (
              <span>{t("common.updated")} {getFormattedDate(automation.updatedAt)}</span>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
