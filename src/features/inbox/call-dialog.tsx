"use client";

import { useState, useEffect, useRef } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useInbox } from "@mug/contexts/inbox";
import { Contact, CallStatus } from "@mug/models/inbox";
import {
  Dialog,
  DialogContent,
} from "@mug/components/ui/dialog";
import { Button } from "@mug/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import { Textarea } from "@mug/components/ui/textarea";
import { 
  Phone, 
  PhoneOff, 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX,
  Loader2
} from "lucide-react";

interface CallDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contact: Contact | null;
}

export function CallDialog({ open, onOpenChange, contact }: CallDialogProps) {
  const { t } = useTranslate();
  const { activeConversationCalls, updateCallStatus } = useInbox();
  
  const [callStatus, setCallStatus] = useState<CallStatus>(CallStatus.RINGING);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(true);
  const [notes, setNotes] = useState("");
  const [isEnding, setIsEnding] = useState(false);
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const callStartTimeRef = useRef<Date | null>(null);
  
  // Get the current call
  const currentCall = activeConversationCalls[0];
  
  // Start call timer
  useEffect(() => {
    if (callStatus === CallStatus.IN_PROGRESS && !callStartTimeRef.current) {
      callStartTimeRef.current = new Date();
      
      timerRef.current = setInterval(() => {
        if (callStartTimeRef.current) {
          const now = new Date();
          const diff = Math.floor((now.getTime() - callStartTimeRef.current.getTime()) / 1000);
          setDuration(diff);
        }
      }, 1000);
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [callStatus]);
  
  // Simulate call connection after a delay
  useEffect(() => {
    if (callStatus === CallStatus.RINGING) {
      const timeout = setTimeout(() => {
        setCallStatus(CallStatus.IN_PROGRESS);
        
        // Update call status in the backend
        if (currentCall) {
          updateCallStatus(currentCall.id, CallStatus.IN_PROGRESS);
        }
      }, 3000);
      
      return () => clearTimeout(timeout);
    }
  }, [callStatus, currentCall, updateCallStatus]);
  
  // Format duration
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };
  
  // Handle end call
  const handleEndCall = async () => {
    setIsEnding(true);
    
    // Stop timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    // Update call status
    if (currentCall) {
      await updateCallStatus(
        currentCall.id,
        CallStatus.COMPLETED,
        duration,
        notes.trim() || undefined
      );
    }
    
    // Close dialog
    onOpenChange(false);
    
    // Reset state
    setCallStatus(CallStatus.RINGING);
    setDuration(0);
    setIsMuted(false);
    setIsSpeakerOn(true);
    setNotes("");
    setIsEnding(false);
    callStartTimeRef.current = null;
  };
  
  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      // Only allow closing through the end call button
      if (!isOpen && callStatus === CallStatus.IN_PROGRESS) {
        return;
      }
      onOpenChange(isOpen);
    }}>
      <DialogContent className="sm:max-w-[400px] p-0 overflow-hidden">
        <div className="flex flex-col items-center justify-center p-6 pt-10 space-y-6">
          {/* Contact info */}
          <Avatar className="h-24 w-24">
            <AvatarImage src={contact?.avatar} alt={contact?.name || ""} />
            <AvatarFallback className="text-2xl">
              {contact?.name?.substring(0, 2) || "?"}
            </AvatarFallback>
          </Avatar>
          
          <div className="text-center">
            <h3 className="text-xl font-medium">{contact?.name || t("inbox.unknownContact")}</h3>
            <p className="text-muted-foreground">{contact?.phone || ""}</p>
          </div>
          
          {/* Call status */}
          <div className="text-center">
            {callStatus === CallStatus.RINGING ? (
              <p className="text-lg animate-pulse">{t("inbox.calling")}</p>
            ) : (
              <p className="text-lg">{formatDuration(duration)}</p>
            )}
          </div>
          
          {/* Call controls */}
          <div className="flex items-center justify-center gap-4">
            {/* Mute button */}
            <Button
              variant="outline"
              size="icon"
              className="h-12 w-12 rounded-full"
              onClick={() => setIsMuted(!isMuted)}
              disabled={callStatus === CallStatus.RINGING}
            >
              {isMuted ? (
                <MicOff className="h-6 w-6" />
              ) : (
                <Mic className="h-6 w-6" />
              )}
            </Button>
            
            {/* End call button */}
            <Button
              variant="destructive"
              size="icon"
              className="h-16 w-16 rounded-full"
              onClick={handleEndCall}
              disabled={isEnding}
            >
              {isEnding ? (
                <Loader2 className="h-8 w-8 animate-spin" />
              ) : (
                <PhoneOff className="h-8 w-8" />
              )}
            </Button>
            
            {/* Speaker button */}
            <Button
              variant="outline"
              size="icon"
              className="h-12 w-12 rounded-full"
              onClick={() => setIsSpeakerOn(!isSpeakerOn)}
              disabled={callStatus === CallStatus.RINGING}
            >
              {isSpeakerOn ? (
                <Volume2 className="h-6 w-6" />
              ) : (
                <VolumeX className="h-6 w-6" />
              )}
            </Button>
          </div>
          
          {/* Call notes */}
          {callStatus === CallStatus.IN_PROGRESS && (
            <div className="w-full space-y-2">
              <p className="text-sm font-medium">{t("inbox.callNotes")}</p>
              <Textarea
                placeholder={t("inbox.callNotesPlaceholder")}
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
