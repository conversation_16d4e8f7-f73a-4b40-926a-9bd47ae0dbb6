"use client";

import { useTranslate } from "@mug/contexts/translate";
import { useInbox } from "@mug/contexts/inbox";
import { classNameBuilder as cn } from "@mug/lib/utils/class-name-builder";
import { ScrollArea } from "@mug/components/ui/scroll-area";
import { Badge } from "@mug/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import {
  MessageSquare,
  Mail,
  Phone,
  Send,
  MessageCircle,
  Users,
  AlertCircle,
  Clock,
  CheckCircle2,
  Archive,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import {
  ChannelType,
  ConversationStatus,
  PriorityLevel
} from "@mug/models/inbox";
import { formatDistanceToNow } from "date-fns";
import { ptBR, enUS, es } from "date-fns/locale";

interface ConversationListProps {
  className?: string;
}

export function ConversationList({ className }: ConversationListProps) {
  const { t, locale } = useTranslate();
  const {
    conversations,
    activeConversation,
    setActiveConversation,
    loading,
    contacts,
    state,
    updateSort
  } = useInbox();

  // Get date-fns locale
  const getDateLocale = () => {
    switch (locale) {
      case "pt-BR":
        return ptBR;
      case "es":
        return es;
      default:
        return enUS;
    }
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: getDateLocale() });
    } catch (error) {
      return dateString;
    }
  };

  // Get channel icon
  const getChannelIcon = (type: ChannelType) => {
    switch (type) {
      case ChannelType.WHATSAPP:
        return <MessageSquare className="h-4 w-4 text-green-500" />;
      case ChannelType.EMAIL:
        return <Mail className="h-4 w-4 text-blue-500" />;
      case ChannelType.CHAT:
        return <MessageCircle className="h-4 w-4 text-orange-500" />;
      case ChannelType.TELEGRAM:
        return <Send className="h-4 w-4 text-blue-500" />;
      case ChannelType.INTERNAL:
        return <Users className="h-4 w-4 text-purple-500" />;
      case ChannelType.VOIP:
        return <Phone className="h-4 w-4 text-blue-500" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  // Get status icon
  const getStatusIcon = (status: ConversationStatus) => {
    switch (status) {
      case ConversationStatus.NEW:
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case ConversationStatus.OPEN:
        return <MessageSquare className="h-4 w-4 text-green-500" />;
      case ConversationStatus.PENDING:
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case ConversationStatus.RESOLVED:
        return <CheckCircle2 className="h-4 w-4 text-blue-500" />;
      case ConversationStatus.ARCHIVED:
        return <Archive className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  // Get priority badge
  const getPriorityBadge = (priority: PriorityLevel) => {
    switch (priority) {
      case PriorityLevel.URGENT:
        return (
          <Badge variant="destructive" className="text-xs">
            {t("inbox.priorityLevels.urgent")}
          </Badge>
        );
      case PriorityLevel.HIGH:
        return (
          <Badge variant="destructive" className="text-xs bg-orange-500">
            {t("inbox.priorityLevels.high")}
          </Badge>
        );
      case PriorityLevel.MEDIUM:
        return (
          <Badge variant="outline" className="text-xs">
            {t("inbox.priorityLevels.medium")}
          </Badge>
        );
      case PriorityLevel.LOW:
        return (
          <Badge variant="outline" className="text-xs text-green-500 border-green-500">
            {t("inbox.priorityLevels.low")}
          </Badge>
        );
      default:
        return null;
    }
  };

  // Get contact by ID
  const getContactById = (id: string) => {
    return contacts.find(contact => contact.id === id);
  };

  // Toggle sort direction
  const toggleSortDirection = () => {
    updateSort(
      state.sortBy,
      state.sortDirection === "asc" ? "desc" : "asc"
    );
  };

  // Change sort field
  const changeSortField = (field: "date" | "priority" | "status") => {
    updateSort(
      field,
      field === state.sortBy ?
        (state.sortDirection === "asc" ? "desc" : "asc") :
        "desc"
    );
  };

  // Get sort icon
  const getSortIcon = (field: "date" | "priority" | "status") => {
    if (state.sortBy !== field) return null;

    return state.sortDirection === "asc" ? (
      <ArrowUp className="h-3 w-3" />
    ) : (
      <ArrowDown className="h-3 w-3" />
    );
  };

  return (
    <div className={cn("flex flex-col h-full border-r", className)}>
      {/* Sort options */}
      <div className="p-2 border-b flex items-center justify-between text-xs text-muted-foreground">
        <button
          className={cn(
            "flex items-center gap-1 px-2 py-1 rounded hover:bg-secondary",
            state.sortBy === "date" && "font-medium text-foreground"
          )}
          onClick={() => changeSortField("date")}
        >
          {t("inbox.date")}
          {getSortIcon("date")}
        </button>
        <button
          className={cn(
            "flex items-center gap-1 px-2 py-1 rounded hover:bg-secondary",
            state.sortBy === "priority" && "font-medium text-foreground"
          )}
          onClick={() => changeSortField("priority")}
        >
          {t("inbox.priority")}
          {getSortIcon("priority")}
        </button>
        <button
          className={cn(
            "flex items-center gap-1 px-2 py-1 rounded hover:bg-secondary",
            state.sortBy === "status" && "font-medium text-foreground"
          )}
          onClick={() => changeSortField("status")}
        >
          {t("inbox.status")}
          {getSortIcon("status")}
        </button>
      </div>

      {/* Conversation list */}
      {loading.conversations ? (
        <div className="flex items-center justify-center h-full p-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : conversations.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full p-4 text-center">
          <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="font-medium">{t("inbox.noConversations")}</h3>
          <p className="text-sm text-muted-foreground mt-1">
            {t("inbox.noConversationsDescription")}
          </p>
        </div>
      ) : (
        <ScrollArea className="flex-1">
          <div className="divide-y">
            {conversations.map((conversation) => {
              const contact = getContactById(conversation.contactId);

              return (
                <button
                  key={conversation.id}
                  className={classNameBuilder(
                    "w-full text-left p-3 hover:bg-accent/50 relative",
                    activeConversation?.id === conversation.id && "bg-accent"
                  )}
                  onClick={() => setActiveConversation(conversation)}
                >
                  {/* Unread indicator */}
                  {conversation.unreadCount > 0 && (
                    <div className="absolute left-0 top-0 bottom-0 w-1 bg-primary"></div>
                  )}

                  <div className="flex items-start gap-3">
                    {/* Contact avatar */}
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={contact?.avatar} alt={contact?.name || ""} />
                      <AvatarFallback>
                        {contact?.name?.substring(0, 2) || "?"}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 max-w-[70%]">
                          {/* Contact name */}
                          <span className="font-medium truncate">
                            {conversation.isGroup ?
                              conversation.subject :
                              contact?.name || t("inbox.unknownContact")}
                          </span>

                          {/* Channel icon */}
                          {getChannelIcon(conversation.channelType)}
                        </div>

                        {/* Time */}
                        <span className="text-xs text-muted-foreground">
                          {formatRelativeTime(conversation.lastMessageAt)}
                        </span>
                      </div>

                      {/* Subject or last message */}
                      <p className="text-sm truncate text-muted-foreground">
                        {conversation.subject || conversation.lastMessagePreview || ""}
                      </p>

                      <div className="flex items-center justify-between mt-1">
                        <div className="flex items-center gap-2">
                          {/* Status */}
                          <div className="flex items-center">
                            {getStatusIcon(conversation.status)}
                          </div>

                          {/* Priority */}
                          {getPriorityBadge(conversation.priority)}
                        </div>

                        {/* Unread count */}
                        {conversation.unreadCount > 0 && (
                          <Badge variant="secondary" className="ml-auto">
                            {conversation.unreadCount}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </ScrollArea>
      )}
    </div>
  );
}
