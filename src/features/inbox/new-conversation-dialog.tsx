"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useInbox } from "@mug/contexts/inbox";
import { ChannelType } from "@mug/models/inbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import { Textarea } from "@mug/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mug/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";

interface NewConversationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function NewConversationDialog({ open, onOpenChange }: NewConversationDialogProps) {
  const { t } = useTranslate();
  const {
    channels,
    contacts,
    startConversation,
    setActiveConversation,
    loading
  } = useInbox();

  const [channelId, setChannelId] = useState("");
  const [contactId, setContactId] = useState("");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [openContactCombobox, setOpenContactCombobox] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get active channels (exclude internal)
  const activeChannels = channels.filter(
    channel => channel.status === "active" && channel.type !== ChannelType.INTERNAL
  );

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!channelId || !contactId) return;

    setIsSubmitting(true);

    try {
      const { conversation, message: newMessage } = await startConversation(
        channelId,
        contactId,
        subject,
        message
      );

      // Set as active conversation
      setActiveConversation(conversation);

      // Close dialog
      onOpenChange(false);

      // Reset form
      setChannelId("");
      setContactId("");
      setSubject("");
      setMessage("");
    } catch (error) {
      console.error("Error starting conversation:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("inbox.newConversation")}</DialogTitle>
          <DialogDescription>
            {t("inbox.newConversationDescription")}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          {/* Channel selection */}
          <div className="space-y-2">
            <Label htmlFor="channel">{t("inbox.selectChannel")}</Label>
            <Select
              value={channelId}
              onValueChange={setChannelId}
            >
              <SelectTrigger id="channel">
                <SelectValue placeholder={t("inbox.selectChannel")} />
              </SelectTrigger>
              <SelectContent>
                {activeChannels.map((channel) => (
                  <SelectItem key={channel.id} value={channel.id}>
                    {channel.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Contact selection */}
          <div className="space-y-2">
            <Label htmlFor="contact">{t("inbox.selectContact")}</Label>
            <Popover open={openContactCombobox} onOpenChange={setOpenContactCombobox}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openContactCombobox}
                  className="w-full justify-between"
                >
                  {contactId
                    ? contacts.find((contact) => contact.id === contactId)?.name
                    : t("inbox.selectContact")}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[400px] p-0">
                <Command>
                  <CommandInput placeholder={t("inbox.searchContacts")} />
                  <CommandList>
                    <CommandEmpty>{t("inbox.noContactsFound")}</CommandEmpty>
                    <CommandGroup>
                      {contacts.map((contact) => (
                        <CommandItem
                          key={contact.id}
                          value={contact.id}
                          onSelect={(value) => {
                            setContactId(value);
                            setOpenContactCombobox(false);
                          }}
                        >
                          <Check
                            className={classNameBuilder(
                              "mr-2 h-4 w-4",
                              contactId === contact.id ? "opacity-100" : "opacity-0"
                            )}
                          />
                          {contact.name}
                          <span className="ml-2 text-muted-foreground">
                            {contact.email || contact.phone}
                          </span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          {/* Subject */}
          <div className="space-y-2">
            <Label htmlFor="subject">{t("inbox.subject")}</Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder={t("inbox.subjectPlaceholder")}
            />
          </div>

          {/* Initial message */}
          <div className="space-y-2">
            <Label htmlFor="message">{t("inbox.initialMessage")}</Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={t("inbox.initialMessagePlaceholder")}
              rows={4}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={!channelId || !contactId || isSubmitting}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t("inbox.startConversation")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
