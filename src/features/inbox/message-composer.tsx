"use client";

import { useState, useRef } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useInbox } from "@mug/contexts/inbox";
import { Attachment } from "@mug/models/inbox";
import { Button } from "@mug/components/ui/button";
import { Textarea } from "@mug/components/ui/textarea";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@mug/components/ui/tooltip";
import { ScrollArea } from "@mug/components/ui/scroll-area";
import { 
  Send, 
  Paperclip, 
  Smile, 
  FileText, 
  Image as ImageIcon, 
  File, 
  X,
  StickyNote
} from "lucide-react";

interface MessageComposerProps {
  conversationId: string;
}

export function MessageComposer({ conversationId }: MessageComposerProps) {
  const { t } = useTranslate();
  const { sendMessage, addNote, templates } = useInbox();
  
  const [message, setMessage] = useState("");
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [isNote, setIsNote] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Handle send message
  const handleSendMessage = async () => {
    if (!message.trim() && attachments.length === 0) return;
    
    if (isNote) {
      await addNote(conversationId, message);
    } else {
      await sendMessage(conversationId, message, attachments.length > 0 ? attachments : undefined);
    }
    
    // Clear form
    setMessage("");
    setAttachments([]);
    setIsNote(false);
  };
  
  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Send on Ctrl+Enter or Cmd+Enter
    if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    // Convert to attachments
    const newAttachments: Attachment[] = Array.from(files).map(file => ({
      id: `temp-${Date.now()}-${file.name}`,
      messageId: "",
      type: file.type.startsWith("image/") 
        ? "image" 
        : file.type.startsWith("video/")
        ? "video"
        : file.type.startsWith("audio/")
        ? "audio"
        : "file",
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size,
      mimeType: file.type
    }));
    
    setAttachments(prev => [...prev, ...newAttachments]);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };
  
  // Remove attachment
  const removeAttachment = (id: string) => {
    setAttachments(prev => prev.filter(attachment => attachment.id !== id));
  };
  
  // Toggle note mode
  const toggleNoteMode = () => {
    setIsNote(prev => !prev);
  };
  
  // Insert template
  const insertTemplate = (content: string) => {
    setMessage(prev => {
      // If there's already text, add a space
      if (prev.trim()) {
        return `${prev} ${content}`;
      }
      return content;
    });
  };
  
  return (
    <div className="border-t p-3 space-y-3">
      {/* Attachments */}
      {attachments.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {attachments.map((attachment) => (
            <div
              key={attachment.id}
              className="flex items-center gap-2 bg-accent rounded-md p-2 pr-3"
            >
              {attachment.type === "image" ? (
                <ImageIcon className="h-4 w-4" />
              ) : (
                <File className="h-4 w-4" />
              )}
              <span className="text-sm truncate max-w-[150px]">
                {attachment.name}
              </span>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5 rounded-full"
                onClick={() => removeAttachment(attachment.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}
      
      <div className="flex items-end gap-2">
        <div className="flex-1 relative">
          <Textarea
            placeholder={isNote ? t("inbox.addPrivateNote") : t("inbox.typeMessage")}
            className={`min-h-[80px] pr-10 resize-none ${isNote ? "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800" : ""}`}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyPress}
          />
          
          {/* Note indicator */}
          {isNote && (
            <div className="absolute top-2 right-2 bg-yellow-200 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-full p-1">
              <StickyNote className="h-4 w-4" />
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {/* Templates */}
          <Popover>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="icon" className="h-10 w-10">
                      <FileText className="h-5 w-5" />
                    </Button>
                  </PopoverTrigger>
                </TooltipTrigger>
                <TooltipContent side="top">
                  {t("inbox.templates")}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <PopoverContent className="w-80 p-0" align="end">
              <div className="p-3 border-b">
                <h4 className="font-medium">{t("inbox.quickReplies")}</h4>
              </div>
              
              <ScrollArea className="h-60">
                <div className="p-3 space-y-3">
                  {templates.length === 0 ? (
                    <p className="text-sm text-muted-foreground">
                      {t("inbox.noTemplates")}
                    </p>
                  ) : (
                    templates.map((template) => (
                      <div
                        key={template.id}
                        className="border rounded-md p-3 cursor-pointer hover:bg-accent"
                        onClick={() => insertTemplate(template.content)}
                      >
                        <h5 className="font-medium text-sm">{template.name}</h5>
                        <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                          {template.content}
                        </p>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </PopoverContent>
          </Popover>
          
          {/* Attachments */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Paperclip className="h-5 w-5" />
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    multiple
                    onChange={handleFileSelect}
                  />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                {t("inbox.attachFiles")}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          {/* Note toggle */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={isNote ? "secondary" : "outline"}
                  size="icon"
                  className={`h-10 w-10 ${isNote ? "bg-yellow-100 text-yellow-800 border-yellow-300 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800 dark:hover:bg-yellow-900/50" : ""}`}
                  onClick={toggleNoteMode}
                >
                  <StickyNote className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                {isNote ? t("inbox.cancelNote") : t("inbox.addNote")}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          {/* Send button */}
          <Button
            className="h-10"
            onClick={handleSendMessage}
            disabled={!message.trim() && attachments.length === 0}
          >
            <Send className="h-5 w-5 mr-2" />
            {isNote ? t("inbox.addNote") : t("inbox.send")}
          </Button>
        </div>
      </div>
      
      <p className="text-xs text-muted-foreground">
        {t("inbox.pressEnterToSend")}
      </p>
    </div>
  );
}
