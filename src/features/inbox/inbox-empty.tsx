"use client";

import { useTranslate } from "@mug/contexts/translate";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";
import { Button } from "@mug/components/ui/button";
import { MessageSquare, Phone, Users } from "lucide-react";
import { useState } from "react";
import { NewConversationDialog } from "./new-conversation-dialog";
import { NewCallDialog } from "./new-call-dialog";
import { NewInternalGroupDialog } from "./new-internal-group-dialog";

interface InboxEmptyProps {
  className?: string;
}

export function InboxEmpty({ className }: InboxEmptyProps) {
  const { t } = useTranslate();

  const [showNewConversation, setShowNewConversation] = useState(false);
  const [showNewCall, setShowNewCall] = useState(false);
  const [showNewGroup, setShowNewGroup] = useState(false);

  return (
    <div className={classNameBuilder("flex flex-col items-center justify-center h-full p-4", className)}>
      <MessageSquare className="h-16 w-16 text-muted-foreground mb-4" />
      <h3 className="text-xl font-medium">{t("inbox.noActiveConversation")}</h3>
      <p className="text-muted-foreground mt-2 text-center max-w-md">
        {t("inbox.selectConversationOrStart")}
      </p>

      <div className="flex flex-wrap gap-2 mt-6">
        <Button onClick={() => setShowNewConversation(true)}>
          <MessageSquare className="h-4 w-4 mr-2" />
          {t("inbox.newConversation")}
        </Button>

        <Button variant="outline" onClick={() => setShowNewCall(true)}>
          <Phone className="h-4 w-4 mr-2" />
          {t("inbox.newCall")}
        </Button>

        <Button variant="outline" onClick={() => setShowNewGroup(true)}>
          <Users className="h-4 w-4 mr-2" />
          {t("inbox.newInternalGroup")}
        </Button>
      </div>

      {/* Dialogs */}
      {showNewConversation && (
        <NewConversationDialog
          open={showNewConversation}
          onOpenChange={setShowNewConversation}
        />
      )}

      {showNewCall && (
        <NewCallDialog
          open={showNewCall}
          onOpenChange={setShowNewCall}
        />
      )}

      {showNewGroup && (
        <NewInternalGroupDialog
          open={showNewGroup}
          onOpenChange={setShowNewGroup}
        />
      )}
    </div>
  );
}
