"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useInbox } from "@mug/contexts/inbox";
import { ChannelType } from "@mug/models/inbox";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";
import { Button } from "@mug/components/ui/button";
import { ScrollArea } from "@mug/components/ui/scroll-area";
import { Separator } from "@mug/components/ui/separator";
import { Badge } from "@mug/components/ui/badge";
import {
  Inbox,
  MessageSquare,
  Mail,
  Phone,
  Send,
  MessageCircle,
  Users,
  ChevronDown,
  ChevronRight,
  CheckCircle2,
  Clock,
  AlertCircle,
  Archive
} from "lucide-react";

interface InboxSidebarProps {
  className?: string;
}

export function InboxSidebar({ className }: InboxSidebarProps) {
  const { t } = useTranslate();
  const {
    channels,
    conversations,
    state,
    updateFilters
  } = useInbox();

  const [expandedSections, setExpandedSections] = useState({
    status: true,
    channels: true
  });

  // Toggle section expansion
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Get channel icon
  const getChannelIcon = (type: ChannelType) => {
    switch (type) {
      case ChannelType.WHATSAPP:
        return <MessageSquare className="h-4 w-4" />;
      case ChannelType.EMAIL:
        return <Mail className="h-4 w-4" />;
      case ChannelType.CHAT:
        return <MessageCircle className="h-4 w-4" />;
      case ChannelType.TELEGRAM:
        return <Send className="h-4 w-4" />;
      case ChannelType.INTERNAL:
        return <Users className="h-4 w-4" />;
      case ChannelType.VOIP:
        return <Phone className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  // Count conversations by status
  const countByStatus = {
    new: conversations.filter(c => c.status === "new").length,
    open: conversations.filter(c => c.status === "open").length,
    pending: conversations.filter(c => c.status === "pending").length,
    resolved: conversations.filter(c => c.status === "resolved").length,
    archived: conversations.filter(c => c.status === "archived").length
  };

  // Count conversations by channel
  const countByChannel = channels.reduce((acc, channel) => {
    acc[channel.type] = conversations.filter(c => c.channelType === channel.type).length;
    return acc;
  }, {} as Record<ChannelType, number>);

  return (
    <div className={classNameBuilder("flex flex-col h-full bg-background", className)}>
      <div className="p-4">
        <h2 className="text-xl font-semibold flex items-center">
          <Inbox className="h-5 w-5 mr-2" />
          {t("inbox.inbox")}
        </h2>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* All conversations */}
          <Button
            variant={!state.filters.status && !state.filters.channels ? "secondary" : "ghost"}
            className="w-full justify-start"
            onClick={() => updateFilters({ status: undefined, channels: undefined })}
          >
            <Inbox className="h-4 w-4 mr-2" />
            {t("inbox.allConversations")}
            <Badge className="ml-auto">{conversations.length}</Badge>
          </Button>

          {/* Status section */}
          <div className="space-y-1">
            <button
              className="flex items-center justify-between w-full text-sm font-medium"
              onClick={() => toggleSection("status")}
            >
              <span>{t("inbox.byStatus")}</span>
              {expandedSections.status ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>

            {expandedSections.status && (
              <div className="space-y-1 pt-1">
                <Button
                  variant={state.filters.status?.includes("new") ? "secondary" : "ghost"}
                  size="sm"
                  className="w-full justify-start pl-4"
                  onClick={() => {
                    const isSelected = state.filters.status?.includes("new");
                    updateFilters({
                      status: isSelected ? undefined : ["new"],
                      channels: undefined
                    });
                  }}
                >
                  <AlertCircle className="h-4 w-4 mr-2 text-blue-500" />
                  {t("inbox.statusTypes.new")}
                  <Badge variant="outline" className="ml-auto">{countByStatus.new}</Badge>
                </Button>

                <Button
                  variant={state.filters.status?.includes("open") ? "secondary" : "ghost"}
                  size="sm"
                  className="w-full justify-start pl-4"
                  onClick={() => {
                    const isSelected = state.filters.status?.includes("open");
                    updateFilters({
                      status: isSelected ? undefined : ["open"],
                      channels: undefined
                    });
                  }}
                >
                  <MessageSquare className="h-4 w-4 mr-2 text-green-500" />
                  {t("inbox.statusTypes.open")}
                  <Badge variant="outline" className="ml-auto">{countByStatus.open}</Badge>
                </Button>

                <Button
                  variant={state.filters.status?.includes("pending") ? "secondary" : "ghost"}
                  size="sm"
                  className="w-full justify-start pl-4"
                  onClick={() => {
                    const isSelected = state.filters.status?.includes("pending");
                    updateFilters({
                      status: isSelected ? undefined : ["pending"],
                      channels: undefined
                    });
                  }}
                >
                  <Clock className="h-4 w-4 mr-2 text-yellow-500" />
                  {t("inbox.statusTypes.pending")}
                  <Badge variant="outline" className="ml-auto">{countByStatus.pending}</Badge>
                </Button>

                <Button
                  variant={state.filters.status?.includes("resolved") ? "secondary" : "ghost"}
                  size="sm"
                  className="w-full justify-start pl-4"
                  onClick={() => {
                    const isSelected = state.filters.status?.includes("resolved");
                    updateFilters({
                      status: isSelected ? undefined : ["resolved"],
                      channels: undefined
                    });
                  }}
                >
                  <CheckCircle2 className="h-4 w-4 mr-2 text-blue-500" />
                  {t("inbox.statusTypes.resolved")}
                  <Badge variant="outline" className="ml-auto">{countByStatus.resolved}</Badge>
                </Button>

                <Button
                  variant={state.filters.status?.includes("archived") ? "secondary" : "ghost"}
                  size="sm"
                  className="w-full justify-start pl-4"
                  onClick={() => {
                    const isSelected = state.filters.status?.includes("archived");
                    updateFilters({
                      status: isSelected ? undefined : ["archived"],
                      channels: undefined
                    });
                  }}
                >
                  <Archive className="h-4 w-4 mr-2 text-gray-500" />
                  {t("inbox.statusTypes.archived")}
                  <Badge variant="outline" className="ml-auto">{countByStatus.archived}</Badge>
                </Button>
              </div>
            )}
          </div>

          <Separator />

          {/* Channels section */}
          <div className="space-y-1">
            <button
              className="flex items-center justify-between w-full text-sm font-medium"
              onClick={() => toggleSection("channels")}
            >
              <span>{t("inbox.byChannel")}</span>
              {expandedSections.channels ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>

            {expandedSections.channels && (
              <div className="space-y-1 pt-1">
                {channels.map((channel) => (
                  <Button
                    key={channel.id}
                    variant={state.filters.channels?.includes(channel.type) ? "secondary" : "ghost"}
                    size="sm"
                    className="w-full justify-start pl-4"
                    onClick={() => {
                      const isSelected = state.filters.channels?.includes(channel.type);
                      updateFilters({
                        channels: isSelected ? undefined : [channel.type],
                        status: undefined
                      });
                    }}
                  >
                    {getChannelIcon(channel.type)}
                    <span className="ml-2">{channel.name}</span>
                    <Badge variant="outline" className="ml-auto">{countByChannel[channel.type] || 0}</Badge>
                  </Button>
                ))}
              </div>
            )}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
