"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useInbox } from "@mug/contexts/inbox";
import { useAuth } from "@mug/contexts/auth";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import { Textarea } from "@mug/components/ui/textarea";
import { Badge } from "@mug/components/ui/badge";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";
import { Check, ChevronsUpDown, Loader2, X } from "lucide-react";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";

interface NewInternalGroupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function NewInternalGroupDialog({ open, onOpenChange }: NewInternalGroupDialogProps) {
  const { t } = useTranslate();
  const { user } = useAuth();
  const { createInternalGroup } = useInbox();

  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [participants, setParticipants] = useState<string[]>([]);
  const [openParticipantCombobox, setOpenParticipantCombobox] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock users for participants (in a real app, this would come from an API)
  const mockUsers = [
    { id: "user-1", name: "João Silva", email: "<EMAIL>" },
    { id: "user-2", name: "Ana Costa", email: "<EMAIL>" },
    { id: "user-3", name: "Carlos Oliveira", email: "<EMAIL>" },
    { id: "user-4", name: "Maria Santos", email: "<EMAIL>" }
  ];

  // Filter out current user from participants list
  const availableUsers = mockUsers.filter(u => u.id !== user?.id);

  // Handle participant selection
  const handleParticipantSelect = (id: string) => {
    if (!participants.includes(id)) {
      setParticipants(prev => [...prev, id]);
    }
    setOpenParticipantCombobox(false);
  };

  // Remove participant
  const removeParticipant = (id: string) => {
    setParticipants(prev => prev.filter(p => p !== id));
  };

  // Get participant name
  const getParticipantName = (id: string) => {
    const user = mockUsers.find(u => u.id === id);
    return user ? user.name : id;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!subject || participants.length === 0) return;

    setIsSubmitting(true);

    try {
      // Add current user to participants if not already included
      const allParticipants = user?.id && !participants.includes(user.id)
        ? [user.id, ...participants]
        : participants;

      // Create internal group
      await createInternalGroup(
        subject,
        allParticipants,
        message
      );

      // Close dialog
      onOpenChange(false);

      // Reset form
      setSubject("");
      setMessage("");
      setParticipants([]);
    } catch (error) {
      console.error("Error creating internal group:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("inbox.newInternalGroup")}</DialogTitle>
          <DialogDescription>
            {t("inbox.newInternalGroupDescription")}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          {/* Subject */}
          <div className="space-y-2">
            <Label htmlFor="subject">{t("inbox.groupSubject")}</Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder={t("inbox.groupSubjectPlaceholder")}
              required
            />
          </div>

          {/* Participants */}
          <div className="space-y-2">
            <Label>{t("inbox.participants")}</Label>
            <Popover open={openParticipantCombobox} onOpenChange={setOpenParticipantCombobox}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openParticipantCombobox}
                  className="w-full justify-between"
                >
                  {t("inbox.addParticipants")}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[400px] p-0">
                <Command>
                  <CommandInput placeholder={t("inbox.searchUsers")} />
                  <CommandList>
                    <CommandEmpty>{t("inbox.noUsersFound")}</CommandEmpty>
                    <CommandGroup>
                      {availableUsers.map((user) => (
                        <CommandItem
                          key={user.id}
                          value={user.id}
                          onSelect={handleParticipantSelect}
                        >
                          <Check
                            className={classNameBuilder(
                              "mr-2 h-4 w-4",
                              participants.includes(user.id) ? "opacity-100" : "opacity-0"
                            )}
                          />
                          {user.name}
                          <span className="ml-2 text-muted-foreground">
                            {user.email}
                          </span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>

            {/* Selected participants */}
            {participants.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {participants.map((id) => (
                  <Badge key={id} variant="secondary" className="pl-2">
                    {getParticipantName(id)}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5 ml-1 rounded-full"
                      onClick={() => removeParticipant(id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Initial message */}
          <div className="space-y-2">
            <Label htmlFor="message">{t("inbox.initialMessage")}</Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={t("inbox.initialMessagePlaceholder")}
              rows={4}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={!subject || participants.length === 0 || isSubmitting}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t("inbox.createGroup")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
