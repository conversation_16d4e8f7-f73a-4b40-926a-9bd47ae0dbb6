"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useInbox } from "@mug/contexts/inbox";
import { CallDirection } from "@mug/models/inbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";
import { Button } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import { Label } from "@mug/components/ui/label";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";
import { Check, ChevronsUpDown, Loader2, Phone } from "lucide-react";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";
import { CallDialog } from "./call-dialog";

interface NewCallDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function NewCallDialog({ open, onOpenChange }: NewCallDialogProps) {
  const { t } = useTranslate();
  const { contacts, startCall } = useInbox();

  const [contactId, setContactId] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [openContactCombobox, setOpenContactCombobox] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCallDialog, setShowCallDialog] = useState(false);

  // Get contacts with phone numbers
  const contactsWithPhone = contacts.filter(contact => contact.phone);

  // Handle contact selection
  const handleContactSelect = (id: string) => {
    setContactId(id);
    setOpenContactCombobox(false);

    // Set phone number from contact
    const contact = contacts.find(c => c.id === id);
    if (contact?.phone) {
      setPhoneNumber(contact.phone);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!contactId || !phoneNumber) return;

    setIsSubmitting(true);

    try {
      // Start the call
      await startCall(
        contactId,
        CallDirection.OUTBOUND,
        phoneNumber
      );

      // Show call dialog
      setShowCallDialog(true);

      // Close this dialog
      onOpenChange(false);
    } catch (error) {
      console.error("Error starting call:", error);
      setIsSubmitting(false);
    }
  };

  // Get selected contact
  const selectedContact = contacts.find(c => c.id === contactId);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("inbox.newCall")}</DialogTitle>
            <DialogDescription>
              {t("inbox.newCallDescription")}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            {/* Contact selection */}
            <div className="space-y-2">
              <Label htmlFor="contact">{t("inbox.selectContact")}</Label>
              <Popover open={openContactCombobox} onOpenChange={setOpenContactCombobox}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={openContactCombobox}
                    className="w-full justify-between"
                  >
                    {contactId
                      ? contacts.find((contact) => contact.id === contactId)?.name
                      : t("inbox.selectContact")}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[400px] p-0">
                  <Command>
                    <CommandInput placeholder={t("inbox.searchContacts")} />
                    <CommandList>
                      <CommandEmpty>{t("inbox.noContactsFound")}</CommandEmpty>
                      <CommandGroup>
                        {contactsWithPhone.map((contact) => (
                          <CommandItem
                            key={contact.id}
                            value={contact.id}
                            onSelect={handleContactSelect}
                          >
                            <Check
                              className={classNameBuilder(
                                "mr-2 h-4 w-4",
                                contactId === contact.id ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {contact.name}
                            <span className="ml-2 text-muted-foreground">
                              {contact.phone}
                            </span>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {/* Phone number */}
            <div className="space-y-2">
              <Label htmlFor="phoneNumber">{t("inbox.phoneNumber")}</Label>
              <Input
                id="phoneNumber"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                placeholder={t("inbox.phoneNumberPlaceholder")}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                {t("common.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={!contactId || !phoneNumber || isSubmitting}
              >
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Phone className="mr-2 h-4 w-4" />
                )}
                {t("inbox.startCall")}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Call dialog */}
      {showCallDialog && selectedContact && (
        <CallDialog
          open={showCallDialog}
          onOpenChange={setShowCallDialog}
          contact={selectedContact}
        />
      )}
    </>
  );
}
