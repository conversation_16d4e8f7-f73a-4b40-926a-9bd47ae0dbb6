"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Conversation, Contact } from "@mug/models/inbox";
import { ScrollArea } from "@mug/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import { Badge } from "@mug/components/ui/badge";
import { Button } from "@mug/components/ui/button";
import { Separator } from "@mug/components/ui/separator";
import { 
  User, 
  Mail, 
  Phone, 
  Building, 
  Briefcase, 
  Calendar, 
  Tag,
  ExternalLink
} from "lucide-react";
import { format } from "date-fns";
import { ptBR, enUS, es } from "date-fns/locale";

interface ConversationInfoProps {
  conversation: Conversation;
  contact: Contact | null;
}

export function ConversationInfo({ conversation, contact }: ConversationInfoProps) {
  const { t, locale } = useTranslate();
  
  // Get date-fns locale
  const getDateLocale = () => {
    switch (locale) {
      case "pt-BR":
        return ptBR;
      case "es":
        return es;
      default:
        return enUS;
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "PPpp", { locale: getDateLocale() });
    } catch (error) {
      return dateString;
    }
  };
  
  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-6">
        {/* Contact information */}
        {contact && !conversation.isGroup && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("inbox.contactInfo")}</h3>
            
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={contact.avatar} alt={contact.name} />
                <AvatarFallback className="text-lg">
                  {contact.name.substring(0, 2)}
                </AvatarFallback>
              </Avatar>
              
              <div>
                <h4 className="font-medium text-lg">{contact.name}</h4>
                {contact.title && contact.company && (
                  <p className="text-muted-foreground">
                    {contact.title} {t("inbox.at")} {contact.company}
                  </p>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              {contact.email && (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{contact.email}</span>
                </div>
              )}
              
              {contact.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{contact.phone}</span>
                </div>
              )}
              
              {contact.company && (
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span>{contact.company}</span>
                </div>
              )}
              
              {contact.title && (
                <div className="flex items-center gap-2">
                  <Briefcase className="h-4 w-4 text-muted-foreground" />
                  <span>{contact.title}</span>
                </div>
              )}
            </div>
            
            {contact.tags && contact.tags.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4 text-muted-foreground" />
                  <span>{t("inbox.tags")}</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {contact.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {contact.crmId && (
              <Button variant="outline" className="w-full" asChild>
                <a href="#" target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  {t("inbox.viewInCrm")}
                </a>
              </Button>
            )}
            
            <Separator />
          </div>
        )}
        
        {/* Group information */}
        {conversation.isGroup && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t("inbox.groupInfo")}</h3>
            
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarFallback className="text-lg bg-purple-500">
                  <Users className="h-8 w-8 text-white" />
                </AvatarFallback>
              </Avatar>
              
              <div>
                <h4 className="font-medium text-lg">{conversation.subject}</h4>
                <p className="text-muted-foreground">
                  {conversation.participants?.length || 0} {t("inbox.participants")}
                </p>
              </div>
            </div>
            
            <Separator />
          </div>
        )}
        
        {/* Conversation details */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">{t("inbox.conversationDetails")}</h3>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">{t("inbox.channel")}</span>
              <Badge variant="outline">
                {t(`inbox.channelTypes.${conversation.channelType}`)}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">{t("inbox.status")}</span>
              <Badge variant="outline">
                {t(`inbox.statusTypes.${conversation.status}`)}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">{t("inbox.priority")}</span>
              <Badge variant="outline">
                {t(`inbox.priorityLevels.${conversation.priority}`)}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">{t("inbox.assignedTo")}</span>
              <span>
                {conversation.assignedTo ? 
                  t("inbox.userName", { name: conversation.assignedTo }) : 
                  t("inbox.unassigned")}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">{t("inbox.created")}</span>
              <span className="text-sm">{formatDate(conversation.createdAt)}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">{t("inbox.lastUpdated")}</span>
              <span className="text-sm">{formatDate(conversation.updatedAt)}</span>
            </div>
          </div>
          
          {conversation.tags && conversation.tags.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <span>{t("inbox.tags")}</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {conversation.tags.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </ScrollArea>
  );
}
