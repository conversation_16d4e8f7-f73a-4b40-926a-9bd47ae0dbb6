"use client";

import { useState, useRef, useEffect } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useInbox } from "@mug/contexts/inbox";
import { useAuth } from "@mug/contexts/auth";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";
import {
  MessageType,
  ConversationStatus,
  PriorityLevel,
  CallStatus,
  CallDirection
} from "@mug/models/inbox";
import { ScrollArea } from "@mug/components/ui/scroll-area";
import { Button } from "@mug/components/ui/button";
import { Textarea } from "@mug/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@mug/components/ui/avatar";
import { Badge } from "@mug/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@mug/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@mug/components/ui/tooltip";
import {
  Send,
  Paperclip,
  MoreVertical,
  CheckCircle2,
  Clock,
  Archive,
  AlertCircle,
  Phone,
  PhoneOff,
  PhoneIncoming,
  PhoneOutgoing,
  PhoneMissed,
  MessageSquare,
  Mail,
  Users,
  User,
  FileText,
  Image as ImageIcon,
  File,
  Play,
  ArrowLeft,
  StickyNote
} from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import { ptBR, enUS, es } from "date-fns/locale";
import { MessageComposer } from "./message-composer";
import { ConversationInfo } from "./conversation-info";
import { CallDialog } from "./call-dialog";

interface ConversationViewProps {
  className?: string;
}

export function ConversationView({ className }: ConversationViewProps) {
  const { t, locale } = useTranslate();
  const { user } = useAuth();
  const {
    activeConversation,
    activeConversationMessages,
    activeConversationCalls,
    setActiveConversation,
    contacts,
    updateConversationStatus,
    updateConversationPriority,
    assignConversation,
    startCall,
    state,
    updateView
  } = useInbox();

  const [activeTab, setActiveTab] = useState("messages");
  const [message, setMessage] = useState("");
  const [showCallDialog, setShowCallDialog] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [activeConversationMessages]);

  // Get date-fns locale
  const getDateLocale = () => {
    switch (locale) {
      case "pt-BR":
        return ptBR;
      case "es":
        return es;
      default:
        return enUS;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "PPpp", { locale: getDateLocale() });
    } catch (error) {
      return dateString;
    }
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: getDateLocale() });
    } catch (error) {
      return dateString;
    }
  };

  // Get contact by ID
  const getContactById = (id: string) => {
    return contacts.find(contact => contact.id === id);
  };

  // Get contact for active conversation
  const getActiveContact = () => {
    if (!activeConversation) return null;
    return getContactById(activeConversation.contactId);
  };

  // Handle back button (mobile)
  const handleBack = () => {
    setActiveConversation(null);
  };

  // Handle call button
  const handleCall = async () => {
    if (!activeConversation || !user) return;

    setShowCallDialog(true);

    // Start the call
    const contact = getActiveContact();
    if (contact?.phone) {
      await startCall(
        activeConversation.contactId,
        CallDirection.OUTBOUND,
        contact.phone
      );
    }
  };

  // Get call status icon
  const getCallStatusIcon = (status: CallStatus) => {
    switch (status) {
      case CallStatus.COMPLETED:
        return <Phone className="h-4 w-4 text-green-500" />;
      case CallStatus.MISSED:
        return <PhoneMissed className="h-4 w-4 text-red-500" />;
      case CallStatus.RINGING:
        return <Phone className="h-4 w-4 text-amber-500 animate-pulse" />;
      case CallStatus.IN_PROGRESS:
        return <Phone className="h-4 w-4 text-green-500 animate-pulse" />;
      case CallStatus.BUSY:
        return <PhoneOff className="h-4 w-4 text-red-500" />;
      case CallStatus.FAILED:
        return <PhoneOff className="h-4 w-4 text-red-500" />;
      case CallStatus.VOICEMAIL:
        return <Phone className="h-4 w-4 text-blue-500" />;
      case CallStatus.CANCELED:
        return <PhoneOff className="h-4 w-4 text-gray-500" />;
      default:
        return <Phone className="h-4 w-4" />;
    }
  };

  // Get call direction icon
  const getCallDirectionIcon = (direction: CallDirection) => {
    switch (direction) {
      case CallDirection.INBOUND:
        return <PhoneIncoming className="h-4 w-4 text-blue-500" />;
      case CallDirection.OUTBOUND:
        return <PhoneOutgoing className="h-4 w-4 text-green-500" />;
      default:
        return null;
    }
  };

  // Format call duration
  const formatCallDuration = (seconds?: number) => {
    if (!seconds) return "0:00";

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // Get attachment icon
  const getAttachmentIcon = (type: string) => {
    switch (type) {
      case "image":
        return <ImageIcon className="h-4 w-4" />;
      case "video":
        return <Play className="h-4 w-4" />;
      case "audio":
        return <Play className="h-4 w-4" />;
      case "file":
        return <File className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  // If no active conversation, show empty state
  if (!activeConversation) {
    return (
      <div className={classNameBuilder("flex flex-col items-center justify-center h-full p-4", className)}>
        <MessageSquare className="h-16 w-16 text-muted-foreground mb-4" />
        <h3 className="text-xl font-medium">{t("inbox.noActiveConversation")}</h3>
        <p className="text-muted-foreground mt-2 text-center max-w-md">
          {t("inbox.selectConversation")}
        </p>
      </div>
    );
  }

  const contact = getActiveContact();

  return (
    <div className={classNameBuilder("flex flex-col h-full", className)}>
      {/* Conversation header */}
      <div className="border-b p-3 flex items-center justify-between">
        <div className="flex items-center">
          {/* Back button (mobile) */}
          <Button
            variant="ghost"
            size="icon"
            className="mr-2 md:hidden"
            onClick={handleBack}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>

          {/* Contact info */}
          <div className="flex items-center">
            <Avatar className="h-10 w-10 mr-3">
              <AvatarImage src={contact?.avatar} alt={contact?.name || ""} />
              <AvatarFallback>
                {contact?.name?.substring(0, 2) || "?"}
              </AvatarFallback>
            </Avatar>

            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-medium">
                  {activeConversation.isGroup ?
                    activeConversation.subject :
                    contact?.name || t("inbox.unknownContact")}
                </h3>

                {/* Status badge */}
                <Badge
                  variant="outline"
                  className={cn(
                    "text-xs",
                    activeConversation.status === ConversationStatus.NEW && "border-blue-500 text-blue-500",
                    activeConversation.status === ConversationStatus.OPEN && "border-green-500 text-green-500",
                    activeConversation.status === ConversationStatus.PENDING && "border-yellow-500 text-yellow-500",
                    activeConversation.status === ConversationStatus.RESOLVED && "border-blue-500 text-blue-500",
                    activeConversation.status === ConversationStatus.ARCHIVED && "border-gray-500 text-gray-500"
                  )}
                >
                  {t(`inbox.statusTypes.${activeConversation.status}`)}
                </Badge>
              </div>

              {contact && (
                <p className="text-sm text-muted-foreground">
                  {contact.email || contact.phone || ""}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Call button (if contact has phone) */}
          {contact?.phone && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleCall}
                  >
                    <Phone className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {t("inbox.call")} {contact.name}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Actions menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreVertical className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t("inbox.conversationActions")}</DropdownMenuLabel>
              <DropdownMenuSeparator />

              {/* Status actions */}
              <DropdownMenuItem
                onClick={() => updateConversationStatus(activeConversation.id, ConversationStatus.OPEN)}
                disabled={activeConversation.status === ConversationStatus.OPEN}
              >
                <MessageSquare className="h-4 w-4 mr-2 text-green-500" />
                {t("inbox.markAsOpen")}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => updateConversationStatus(activeConversation.id, ConversationStatus.PENDING)}
                disabled={activeConversation.status === ConversationStatus.PENDING}
              >
                <Clock className="h-4 w-4 mr-2 text-yellow-500" />
                {t("inbox.markAsPending")}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => updateConversationStatus(activeConversation.id, ConversationStatus.RESOLVED)}
                disabled={activeConversation.status === ConversationStatus.RESOLVED}
              >
                <CheckCircle2 className="h-4 w-4 mr-2 text-blue-500" />
                {t("inbox.markAsResolved")}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => updateConversationStatus(activeConversation.id, ConversationStatus.ARCHIVED)}
                disabled={activeConversation.status === ConversationStatus.ARCHIVED}
              >
                <Archive className="h-4 w-4 mr-2 text-gray-500" />
                {t("inbox.archive")}
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {/* Priority actions */}
              <DropdownMenuLabel>{t("inbox.setPriority")}</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => updateConversationPriority(activeConversation.id, PriorityLevel.LOW)}
                disabled={activeConversation.priority === PriorityLevel.LOW}
              >
                <Badge variant="outline" className="mr-2 text-green-500 border-green-500">
                  {t("inbox.priorityLevels.low")}
                </Badge>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => updateConversationPriority(activeConversation.id, PriorityLevel.MEDIUM)}
                disabled={activeConversation.priority === PriorityLevel.MEDIUM}
              >
                <Badge variant="outline" className="mr-2">
                  {t("inbox.priorityLevels.medium")}
                </Badge>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => updateConversationPriority(activeConversation.id, PriorityLevel.HIGH)}
                disabled={activeConversation.priority === PriorityLevel.HIGH}
              >
                <Badge variant="destructive" className="mr-2 bg-orange-500">
                  {t("inbox.priorityLevels.high")}
                </Badge>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => updateConversationPriority(activeConversation.id, PriorityLevel.URGENT)}
                disabled={activeConversation.priority === PriorityLevel.URGENT}
              >
                <Badge variant="destructive" className="mr-2">
                  {t("inbox.priorityLevels.urgent")}
                </Badge>
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {/* Assignment actions */}
              <DropdownMenuLabel>{t("inbox.assign")}</DropdownMenuLabel>
              {user && (
                <DropdownMenuItem
                  onClick={() => assignConversation(activeConversation.id, user.id)}
                  disabled={activeConversation.assignedTo === user.id}
                >
                  <User className="h-4 w-4 mr-2" />
                  {t("inbox.assignToMe")}
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={() => assignConversation(activeConversation.id, "")}
                disabled={!activeConversation.assignedTo}
              >
                <Users className="h-4 w-4 mr-2" />
                {t("inbox.unassign")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Conversation content */}
      <div className="flex-1 flex overflow-hidden">
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="px-4 pt-2 bg-background border-b rounded-none justify-start h-auto">
              <TabsTrigger value="messages" className="rounded-b-none data-[state=active]:border-b-2 data-[state=active]:border-primary">
                {t("inbox.messages")}
              </TabsTrigger>
              <TabsTrigger value="calls" className="rounded-b-none data-[state=active]:border-b-2 data-[state=active]:border-primary">
                {t("inbox.calls")}
              </TabsTrigger>
              <TabsTrigger value="info" className="rounded-b-none data-[state=active]:border-b-2 data-[state=active]:border-primary">
                {t("inbox.info")}
              </TabsTrigger>
            </TabsList>

            {/* Messages tab */}
            <TabsContent value="messages" className="flex-1 flex flex-col overflow-hidden mt-0 p-0">
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {activeConversationMessages.map((message) => {
                    const isUser = message.senderType === "user";
                    const isNote = message.type === MessageType.NOTE;
                    const isSystem = message.senderType === "system";

                    // System message
                    if (isSystem) {
                      return (
                        <div key={message.id} className="flex justify-center">
                          <div className="bg-muted px-3 py-1 rounded-full text-xs text-muted-foreground">
                            {message.content}
                          </div>
                        </div>
                      );
                    }

                    // Private note
                    if (isNote) {
                      return (
                        <div key={message.id} className="flex justify-center">
                          <div className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 px-4 py-2 rounded-md text-sm max-w-[80%] flex items-start">
                            <StickyNote className="h-4 w-4 mr-2 mt-0.5 shrink-0" />
                            <div>
                              <p className="font-medium text-xs mb-1">{t("inbox.privateNote")}</p>
                              <p>{message.content}</p>
                              <p className="text-xs mt-1 text-yellow-700 dark:text-yellow-400">
                                {formatRelativeTime(message.createdAt)}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    }

                    return (
                      <div
                        key={message.id}
                        className={classNameBuilder(
                          "flex",
                          isUser ? "justify-end" : "justify-start"
                        )}
                      >
                        <div className="flex items-start gap-2 max-w-[80%]">
                          {/* Avatar for contact messages */}
                          {!isUser && (
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={contact?.avatar} alt={contact?.name || ""} />
                              <AvatarFallback>
                                {contact?.name?.substring(0, 2) || "?"}
                              </AvatarFallback>
                            </Avatar>
                          )}

                          <div
                            className={classNameBuilder(
                              "rounded-lg px-4 py-2 space-y-2",
                              isUser
                                ? "bg-primary text-primary-foreground"
                                : "bg-accent"
                            )}
                          >
                            {/* Message content */}
                            <p>{message.content}</p>

                            {/* Attachments */}
                            {message.attachments && message.attachments.length > 0 && (
                              <div className="space-y-2">
                                {message.attachments.map((attachment) => (
                                  <div
                                    key={attachment.id}
                                    className={classNameBuilder(
                                      "flex items-center gap-2 p-2 rounded",
                                      isUser
                                        ? "bg-primary-foreground/10"
                                        : "bg-background"
                                    )}
                                  >
                                    {getAttachmentIcon(attachment.type)}
                                    <div className="overflow-hidden">
                                      <p className="text-sm font-medium truncate">
                                        {attachment.name}
                                      </p>
                                      <p className="text-xs opacity-70">
                                        {(attachment.size / 1024 / 1024).toFixed(2)} MB
                                      </p>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="ml-auto h-6 w-6"
                                      onClick={() => window.open(attachment.url, "_blank")}
                                    >
                                      <FileText className="h-4 w-4" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            )}

                            {/* Timestamp */}
                            <p
                              className={classNameBuilder(
                                "text-xs",
                                isUser
                                  ? "text-primary-foreground/70"
                                  : "text-muted-foreground"
                              )}
                            >
                              {formatRelativeTime(message.createdAt)}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* Scroll anchor */}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Message composer */}
              <MessageComposer conversationId={activeConversation.id} />
            </TabsContent>

            {/* Calls tab */}
            <TabsContent value="calls" className="flex-1 overflow-hidden mt-0 p-0">
              <ScrollArea className="h-full p-4">
                <div className="space-y-4">
                  {activeConversationCalls.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <Phone className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="font-medium">{t("inbox.noCalls")}</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {t("inbox.noCallsDescription")}
                      </p>

                      {contact?.phone && (
                        <Button
                          className="mt-4"
                          onClick={handleCall}
                        >
                          <Phone className="h-4 w-4 mr-2" />
                          {t("inbox.startCall")}
                        </Button>
                      )}
                    </div>
                  ) : (
                    activeConversationCalls.map((call) => (
                      <div
                        key={call.id}
                        className="border rounded-lg p-4 space-y-2"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getCallStatusIcon(call.status)}
                            <span className="font-medium">
                              {t(`inbox.callStatus.${call.status}`)}
                            </span>
                            {getCallDirectionIcon(call.direction)}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(call.createdAt)}
                          </span>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm">
                            {call.phoneNumber}
                          </span>
                          {call.duration !== undefined && (
                            <span className="text-sm">
                              {formatCallDuration(call.duration)}
                            </span>
                          )}
                        </div>

                        {call.notes && (
                          <div className="pt-2 border-t mt-2">
                            <p className="text-sm text-muted-foreground">
                              {call.notes}
                            </p>
                          </div>
                        )}

                        {call.recordingUrl && (
                          <div className="pt-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(call.recordingUrl, "_blank")}
                            >
                              <Play className="h-4 w-4 mr-2" />
                              {t("inbox.playRecording")}
                            </Button>
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Info tab */}
            <TabsContent value="info" className="flex-1 overflow-hidden mt-0 p-0">
              <ConversationInfo
                conversation={activeConversation}
                contact={contact}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Call dialog */}
      {showCallDialog && (
        <CallDialog
          open={showCallDialog}
          onOpenChange={setShowCallDialog}
          contact={contact}
        />
      )}
    </div>
  );
}
