"use client";

import { useState, useEffect } from "react";
import { useInbox } from "@mug/contexts/inbox";
import { ConversationList } from "./conversation-list";
import { ConversationView } from "./conversation-view";
import { InboxHeader } from "./inbox-header";
import { InboxSidebar } from "./inbox-sidebar";
import { InboxEmpty } from "./inbox-empty";
import { Loader2 } from "lucide-react";

export function InboxLayout() {
  const { 
    activeConversation, 
    loading, 
    state,
    updateView
  } = useInbox();
  
  // Handle resize for responsive layout
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        updateView("list");
      } else {
        updateView("split");
      }
    };
    
    // Set initial view
    handleResize();
    
    // Add event listener
    window.addEventListener("resize", handleResize);
    
    // Cleanup
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [updateView]);
  
  // Show loading state
  if (loading.conversations && loading.channels) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="flex h-full overflow-hidden">
      {/* Sidebar with filters and channels */}
      <InboxSidebar className="hidden md:block w-64 border-r shrink-0" />
      
      <div className="flex flex-col flex-1 h-full overflow-hidden">
        {/* Header with search and filters */}
        <InboxHeader />
        
        <div className="flex flex-1 overflow-hidden">
          {/* Conversation list */}
          {(state.view === "list" || state.view === "split") && (
            <ConversationList 
              className={`${state.view === "split" ? "w-80 border-r" : "flex-1"} shrink-0`} 
            />
          )}
          
          {/* Conversation view */}
          {(state.view === "detail" || (state.view === "split" && activeConversation)) && (
            <ConversationView className="flex-1" />
          )}
          
          {/* Empty state */}
          {state.view === "split" && !activeConversation && (
            <InboxEmpty className="flex-1" />
          )}
        </div>
      </div>
    </div>
  );
}
