"use client";

import { useState } from "react";
import { useTranslate } from "@mug/contexts/translate";
import { useInbox } from "@mug/contexts/inbox";
import { useAuth } from "@mug/contexts/auth";
import { 
  ChannelType, 
  ConversationStatus, 
  PriorityLevel,
  OperatorStatus
} from "@mug/models/inbox";
import { But<PERSON> } from "@mug/components/ui/button";
import { Input } from "@mug/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";
import { Badge } from "@mug/components/ui/badge";
import { Separator } from "@mug/components/ui/separator";
import { 
  Search, 
  Filter, 
  Plus, 
  List, 
  LayoutGrid, 
  SplitSquareVertical,
  Phone,
  MessageSquare,
  Users,
  CheckCircle2,
  Clock,
  AlertCircle,
  Circle,
  X
} from "lucide-react";
import { NewConversationDialog } from "./new-conversation-dialog";
import { NewCallDialog } from "./new-call-dialog";
import { NewInternalGroupDialog } from "./new-internal-group-dialog";

export function InboxHeader() {
  const { t } = useTranslate();
  const { user } = useAuth();
  const { 
    state, 
    updateView, 
    updateFilters, 
    operatorSettings,
    updateOperatorStatus,
    channels
  } = useInbox();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [showNewConversation, setShowNewConversation] = useState(false);
  const [showNewCall, setShowNewCall] = useState(false);
  const [showNewGroup, setShowNewGroup] = useState(false);
  
  // Handle search
  const handleSearch = () => {
    updateFilters({ search: searchQuery });
  };
  
  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };
  
  // Handle status change
  const handleStatusChange = (status: OperatorStatus) => {
    updateOperatorStatus(status);
  };
  
  // Get active filters count
  const getActiveFiltersCount = () => {
    let count = 0;
    
    if (state.filters.status && state.filters.status.length > 0) count++;
    if (state.filters.channels && state.filters.channels.length > 0) count++;
    if (state.filters.assignedTo && state.filters.assignedTo.length > 0) count++;
    if (state.filters.priority && state.filters.priority.length > 0) count++;
    if (state.filters.tags && state.filters.tags.length > 0) count++;
    if (state.filters.dateRange) count++;
    
    return count;
  };
  
  // Get status color
  const getStatusColor = (status: OperatorStatus) => {
    switch (status) {
      case OperatorStatus.AVAILABLE:
        return "text-green-500";
      case OperatorStatus.BUSY:
        return "text-amber-500";
      case OperatorStatus.AWAY:
        return "text-orange-500";
      case OperatorStatus.OFFLINE:
        return "text-gray-500";
      default:
        return "text-gray-500";
    }
  };
  
  return (
    <div className="border-b p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 flex-1">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("inbox.searchConversations")}
              className="pl-10 pr-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyPress}
            />
            {searchQuery && (
              <button
                className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
                onClick={() => {
                  setSearchQuery("");
                  if (state.filters.search) {
                    updateFilters({ search: undefined });
                  }
                }}
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
          
          {/* Filters */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="relative">
                <Filter className="h-4 w-4 mr-2" />
                {t("inbox.filters")}
                {getActiveFiltersCount() > 0 && (
                  <Badge className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                    {getActiveFiltersCount()}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h4 className="font-medium">{t("inbox.filterBy")}</h4>
                
                {/* Status filter */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">{t("inbox.status")}</h5>
                  <div className="flex flex-wrap gap-2">
                    {Object.values(ConversationStatus).map((status) => (
                      <Badge
                        key={status}
                        variant={state.filters.status?.includes(status) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => {
                          const currentStatus = state.filters.status || [];
                          const newStatus = currentStatus.includes(status)
                            ? currentStatus.filter(s => s !== status)
                            : [...currentStatus, status];
                          
                          updateFilters({ status: newStatus.length > 0 ? newStatus : undefined });
                        }}
                      >
                        {t(`inbox.statusTypes.${status}`)}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {/* Channel filter */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">{t("inbox.channels")}</h5>
                  <div className="flex flex-wrap gap-2">
                    {channels.map((channel) => (
                      <Badge
                        key={channel.id}
                        variant={state.filters.channels?.includes(channel.type) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => {
                          const currentChannels = state.filters.channels || [];
                          const newChannels = currentChannels.includes(channel.type)
                            ? currentChannels.filter(c => c !== channel.type)
                            : [...currentChannels, channel.type];
                          
                          updateFilters({ channels: newChannels.length > 0 ? newChannels : undefined });
                        }}
                      >
                        {channel.name}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {/* Priority filter */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">{t("inbox.priority")}</h5>
                  <div className="flex flex-wrap gap-2">
                    {Object.values(PriorityLevel).map((priority) => (
                      <Badge
                        key={priority}
                        variant={state.filters.priority?.includes(priority) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => {
                          const currentPriority = state.filters.priority || [];
                          const newPriority = currentPriority.includes(priority)
                            ? currentPriority.filter(p => p !== priority)
                            : [...currentPriority, priority];
                          
                          updateFilters({ priority: newPriority.length > 0 ? newPriority : undefined });
                        }}
                      >
                        {t(`inbox.priorityLevels.${priority}`)}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {/* Assignment filter */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">{t("inbox.assignedTo")}</h5>
                  <div className="flex flex-wrap gap-2">
                    <Badge
                      variant={state.filters.assignedTo?.includes("me") ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const isSelected = state.filters.assignedTo?.includes("me");
                        updateFilters({ assignedTo: isSelected ? undefined : ["me"] });
                      }}
                    >
                      {t("inbox.me")}
                    </Badge>
                    <Badge
                      variant={state.filters.assignedTo?.includes("unassigned") ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const isSelected = state.filters.assignedTo?.includes("unassigned");
                        updateFilters({ assignedTo: isSelected ? undefined : ["unassigned"] });
                      }}
                    >
                      {t("inbox.unassigned")}
                    </Badge>
                  </div>
                </div>
                
                {/* Clear filters */}
                <div className="pt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full"
                    onClick={() => updateFilters({
                      status: undefined,
                      channels: undefined,
                      assignedTo: undefined,
                      priority: undefined,
                      tags: undefined,
                      dateRange: undefined,
                      search: undefined
                    })}
                    disabled={getActiveFiltersCount() === 0}
                  >
                    {t("inbox.clearFilters")}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
        
        <div className="flex items-center space-x-2 ml-4">
          {/* View toggle */}
          <div className="hidden md:flex items-center border rounded-md p-1">
            <Button
              variant={state.view === "list" ? "secondary" : "ghost"}
              size="icon"
              className="h-8 w-8"
              onClick={() => updateView("list")}
            >
              <List className="h-4 w-4" />
              <span className="sr-only">{t("inbox.listView")}</span>
            </Button>
            <Button
              variant={state.view === "split" ? "secondary" : "ghost"}
              size="icon"
              className="h-8 w-8"
              onClick={() => updateView("split")}
            >
              <SplitSquareVertical className="h-4 w-4" />
              <span className="sr-only">{t("inbox.splitView")}</span>
            </Button>
          </div>
          
          <Separator orientation="vertical" className="h-8 hidden md:block" />
          
          {/* Operator status */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="gap-2">
                <Circle className={`h-3 w-3 ${getStatusColor(operatorSettings?.status || OperatorStatus.OFFLINE)}`} />
                {operatorSettings?.status ? t(`inbox.operatorStatus.${operatorSettings.status}`) : t("inbox.operatorStatus.offline")}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t("inbox.setStatus")}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleStatusChange(OperatorStatus.AVAILABLE)}>
                <Circle className="h-3 w-3 text-green-500 mr-2" />
                {t("inbox.operatorStatus.available")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleStatusChange(OperatorStatus.BUSY)}>
                <Circle className="h-3 w-3 text-amber-500 mr-2" />
                {t("inbox.operatorStatus.busy")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleStatusChange(OperatorStatus.AWAY)}>
                <Circle className="h-3 w-3 text-orange-500 mr-2" />
                {t("inbox.operatorStatus.away")}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleStatusChange(OperatorStatus.OFFLINE)}>
                <Circle className="h-3 w-3 text-gray-500 mr-2" />
                {t("inbox.operatorStatus.offline")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Separator orientation="vertical" className="h-8" />
          
          {/* New conversation/call */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                {t("inbox.new")}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuGroup>
                <DropdownMenuItem onClick={() => setShowNewConversation(true)}>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  {t("inbox.newConversation")}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setShowNewCall(true)}>
                  <Phone className="h-4 w-4 mr-2" />
                  {t("inbox.newCall")}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setShowNewGroup(true)}>
                  <Users className="h-4 w-4 mr-2" />
                  {t("inbox.newInternalGroup")}
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      {/* Dialogs */}
      {showNewConversation && (
        <NewConversationDialog 
          open={showNewConversation} 
          onOpenChange={setShowNewConversation} 
        />
      )}
      
      {showNewCall && (
        <NewCallDialog 
          open={showNewCall} 
          onOpenChange={setShowNewCall} 
        />
      )}
      
      {showNewGroup && (
        <NewInternalGroupDialog 
          open={showNewGroup} 
          onOpenChange={setShowNewGroup} 
        />
      )}
    </div>
  );
}
