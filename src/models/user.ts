import { TwoFactorMethod } from "@mug/models/auth";
import { Theme, Language } from "@mug/models/core";

export interface GoogleProfile {
  id: string;
  email: string;
  name: string;
  picture?: string;
  locale?: string;
}

export interface NotificationPreferences {
  sms: boolean;
  whatsapp: boolean;
  telegram: boolean;
  email: boolean;
  push: boolean;
  inApp: boolean;
  marketing: boolean;
}

export interface SecuritySettings {
  loginNotifications: boolean;
  unusualActivityAlerts: boolean;
  sessionTimeout: number;
}

export interface LoginHistory {
  id: string;
  userID: string;
  timestamp: string;
  ip: string;
  device: string;
  browser: string;
  location?: string;
  success: boolean;
}

export interface ConnectedDevice {
  id: string;
  userID: string;
  name: string;
  type: string;
  lastActive: string;
  browser?: string;
  os?: string;
  ip?: string;
  location?: string;
  isCurrent: boolean;
}

export interface User {
  id: string;
  name: string;
  email: string;
  username?: string;
  mobile?: string;
  avatar?: string;
  role?: string;
  roles?: string[];
  phone?: string;
  timezone?: string;
  language?: Language;
  theme?: Theme;
  lastLogin?: string;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  twoFactorEnabled?: boolean;
  twoFactorMethod?: TwoFactorMethod;
  googleConnected?: boolean;
  googleProfile?: GoogleProfile;
  notificationPreferences?: NotificationPreferences;
  securitySettings?: SecuritySettings;
  status?: boolean;
  tenantId?: string;
  createdAt: string;
  updatedAt: string;
}
