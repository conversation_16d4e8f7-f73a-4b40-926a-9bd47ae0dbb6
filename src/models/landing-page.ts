/**
 * Conversion Mode - defines the type of conversion for landing pages and forms
 */
export type ConvertMode =
  | "lead"
  | "service"
  | "product"
  | "subscribe"
  | "schedule";

/**
 * Landing Page model - represents a landing page in the system
 */
export interface LandingPage {
  id: string;
  name: string;
  description?: string;
  slug: string;
  template?: LandingPageTemplate;
  blocks: LandingPageBlock[];
  conversionMode: ConvertMode;
  seo?: SEOSettings;
  status: boolean;
  laneId?: string; // ID of the lane this landing page is linked to
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
}

/**
 * Landing Page Block - represents a content block in a landing page
 */
export interface LandingPageBlock {
  id: string;
  type: LandingPageBlockType;
  content: Record<string, unknown>;
  settings?: Record<string, unknown>;
  order: number;
}

/**
 * Landing Page Block Type - defines the type of block
 */
export type LandingPageBlockType =
  | "header"
  | "text"
  | "image"
  | "video"
  | "gallery"
  | "cta"
  | "form"
  | "testimonial"
  | "pricing"
  | "faq"
  | "contact"
  | "custom";

/**
 * Landing Page Template - predefined landing page templates
 */
export type LandingPageTemplate =
  | "blank"
  | "product"
  | "capture"
  | "event"
  | "contact"
  | "thank-you";

/**
 * SEO Settings - search engine optimization settings
 */
export interface SEOSettings {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  ogTitle?: string;
  ogDescription?: string;
}

/**
 * Form values for Landing Page creation/editing
 */
export interface LandingPageFormValues {
  name: string;
  description?: string;
  slug: string;
  template?: LandingPageTemplate;
  blocks: LandingPageBlock[];
  conversionMode: ConvertMode;
  seo?: SEOSettings;
  status: boolean;
  laneId?: string;
}