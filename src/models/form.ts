import { ConvertMode } from "./landing-page";

/**
 * Form model - represents a form in the system
 */
export interface Form {
  id: string;
  name: string;
  description?: string;
  slug: string;
  template?: FormTemplate;
  fields: FormField[];
  settings: FormSettings;
  conversionMode: ConvertMode;
  status: boolean;
  laneId?: string; // ID of the lane this form is linked to
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
}

/**
 * Form Field - represents a field in a form
 */
export interface FormField {
  id: string;
  type: FormFieldType;
  label: string;
  placeholder?: string;
  helpText?: string;
  required: boolean;
  validation?: FormFieldValidation;
  options?: FormFieldOption[];
  settings?: Record<string, unknown>;
  order: number;
}

/**
 * Form Field Type - defines the type of field
 */
export type FormFieldType =
  | "text"
  | "textarea"
  | "email"
  | "phone"
  | "number"
  | "radio"
  | "checkbox"
  | "select"
  | "file"
  | "date"
  | "time"
  | "datetime"
  | "hidden"
  | "custom";

/**
 * Form Field Validation - defines validation rules for a field
 */
export interface FormFieldValidation {
  type?: string;
  pattern?: string;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  customMessage?: string;
}

/**
 * Form Field Option - defines an option for select, radio, or checkbox fields
 */
export interface FormFieldOption {
  label: string;
  value: string;
  default?: boolean;
}

/**
 * Form Settings - defines settings for a form
 */
export interface FormSettings {
  submitButtonText: string;
  successMessage: string;
  errorMessage: string;
  redirectUrl?: string;
  captcha: boolean;
  storeSubmissions: boolean;
  sendNotifications: boolean;
  notificationEmails?: string[];
  customStyles?: Record<string, unknown>;
}

/**
 * Form Template - predefined form templates
 */
export type FormTemplate =
  | "blank"
  | "contact"
  | "registration"
  | "survey"
  | "feedback"
  | "order";

/**
 * Form Submission - represents a submission of a form
 */
export interface FormSubmission {
  id: string;
  formId: string;
  data: Record<string, unknown>;
  createdAt: string;
  ip?: string;
  userAgent?: string;
  referrer?: string;
}

/**
 * Form values for Form creation/editing
 */
export interface FormFormValues {
  name: string;
  description?: string;
  slug: string;
  template?: FormTemplate;
  fields: FormField[];
  settings: FormSettings;
  conversionMode: ConvertMode;
  status: boolean;
  laneId?: string;
}
