import { Item } from "@mug/models/item";
import { Flow } from "./flow";

export type LaneRestriction = "none" | "only-left" | "only-right" | "new";

export type LaneModel =
  | "backlog"
  | "new"
  | "nurture"
  | "potential"
  | "opportunity"
  | "done"
  | "lost"
  | "won"
  | "pending"
  | "attention";

export interface Lane {
  id: string;
  flow: Flow;
  title: string;
  items: Item[];
  model?: LaneModel;
  allowDrop?: LaneRestriction;
}
