import { Person } from "@mug/models/person";
import { User } from "@mug/models/user";

export type EventType =
  | "meeting"
  | "call"
  | "task"
  | "reminder"
  | "deadline"
  | "appointment"
  | "other";

export type EventStatus =
  | "scheduled"
  | "confirmed"
  | "cancelled"
  | "completed"
  | "pending";

export type EventPriority =
  | "high"
  | "medium"
  | "low";

export type CalendarView =
  | "month"
  | "week"
  | "day"
  | "agenda";

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  start: string; 
  end: string; 
  allDay?: boolean;
  location?: string;
  url?: string;
  type: EventType;
  status: EventStatus;
  priority?: EventPriority;
  user?: User;
  person?: Person;
  flowId?: string; 
  recordId?: string; 
  createdAt?: string;
  updatedAt?: string;
}