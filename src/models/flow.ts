import { Action } from "@mug/models/action";
import { Lane } from "@mug/models/lane";
import { LandingPage } from "@mug/models/landing-page";
import { User } from "@mug/constants/mock-lanes";
import { MockLane } from "@mug/constants/mock-lanes";

export interface FlowStats {
  totalItems: number;
  completedItems: number;
  conversionRate: number;
  avgCycleTime: number;
}

export enum FlowMode {
  ATTRACT = "attract",
  CONVERT = "convert",
  ENGAGE = "engage",
  CLOSE = "close",
  DELIGHT = "delight",
  ANALYZE = "analyze",
}

/**
 * Flow type enum - defines the category of the flow
 */
export type FlowType =
  | "sales"
  | "marketing"
  | "support"
  | "development"
  | "onboarding"
  | "recruitment"
  | "other";

/**
 * Flow template enum - defines the initial template for a flow
 */
export type FlowTemplate =
  | "empty"
  | "basic"
  | "sales"
  | "marketing"
  | "support";

/**
 * Flow visibility enum - defines who can see and access the flow
 */
export type FlowVisibility =
  | "public"
  | "private"
  | "shared";

/**
 * Lane restriction type - defines how cards can move between lanes
 */
export type LaneRestriction =
  | "none"
  | "only-left"
  | "only-right";

/**
 * Lane model type - defines the semantic meaning of a lane
 */
export type LaneModelType =
  | "backlog"
  | "new"
  | "nurture"
  | "potential"
  | "opportunity"
  | "done"
  | "lost"
  | "won"
  | "pending"
  | "attention"
  | "default";

/**
 * Lane position - defines where to place a new lane
 */
export type LanePosition = "start" | "end" | { afterLaneId: string };

// Base Flow model for the application
export interface Flow {
  id: string;
  name: string;
  description?: string;
  mode: FlowMode;
  isArchived?: boolean;
  createdAt?: string;
  updatedAt: string;
  createdBy?: User;
  visibility?: FlowVisibility;
  members?: { id: string }[];
  lanes?: MockLane[];
  tags?: string[];
  stats?: FlowStats;
  viewMode?: "kanban" | "list";
}

// Form values for Flow creation/editing
export interface FlowFormValues {
  title: string;
  description?: string;
  type: FlowType;
  template?: FlowTemplate;
  mode?: FlowMode;
  visibility: FlowVisibility;
  members?: string[];
}

// Flow with members interface for edit flow component
export interface FlowWithMembers {
  id: string;
  name: string;
  description?: string;
  mode: FlowMode;
  isArchived?: boolean;
  members?: { id: string }[];
  tags?: string[];
}

// Domain-specific Flow models
export type DomainFlow =
  | ({ mode: FlowMode.ATTRACT } & AttractFlow)
  | ({ mode: FlowMode.CONVERT } & BaseFlow)
  | ({ mode: FlowMode.ENGAGE } & BaseFlow)
  | ({ mode: FlowMode.CLOSE } & BaseFlow)
  | ({ mode: FlowMode.DELIGHT } & BaseFlow)
  | ({ mode: FlowMode.ANALYZE } & BaseFlow);

interface AttractFlow {
  id: string;
  name: string;
  lanes: Lane[];
  actions: Action[];
  landingPage: LandingPage;
}

interface BaseFlow {
  id: string;
  name: string;
  lanes: Lane[];
  actions: Action[];
}
