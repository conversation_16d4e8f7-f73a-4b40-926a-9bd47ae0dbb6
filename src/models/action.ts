export type ActionType =
  | "email"
  | "notification"
  | "webhook"
  | "api_call"
  | "database"
  | "file"
  | "integration"
  | "custom";

export type ActionCategory =
  | "communication"
  | "data_processing"
  | "integration"
  | "notification"
  | "automation"
  | "reporting"
  | "utility";

export interface Action {
  id: string;
  name: string;
  description?: string;
  type: ActionType;
  category: ActionCategory;
  configs: string;
  status: boolean;
  createdAt: string;
  updatedAt: string;
}
