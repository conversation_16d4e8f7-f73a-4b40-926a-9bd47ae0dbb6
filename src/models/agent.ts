export interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  status: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum AgentModelType {
  GPT35 = "gpt-3.5",
  GPT4 = "gpt-4",
  CLAUDE3_OPUS = "claude-3-opus",
  CLAUDE3_SONNET = "claude-3-sonnet",
  CLAUDE3_HAIKU = "claude-3-haiku",
  GEMINI_PRO = "gemini-pro",
  MISTRAL_LARGE = "mistral-large",
  MISTRAL_MEDIUM = "mistral-medium",
  LLAMA3 = "llama-3",
}

export interface Agent {
  id: string;
  name: string;
  description: string;
  actions: string[];
  rules: string[];
  status: boolean;
  model: AgentModelType;
  createdAt: string;
  updatedAt: string;
}
