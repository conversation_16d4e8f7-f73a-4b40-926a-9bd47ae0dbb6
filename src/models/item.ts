import { History } from "@mug/models/history";
import { Person } from "@mug/models/person";
import { Agent } from "@mug/models/agent";
import { Tag } from "@mug/models/tag";
import { Lane } from "@mug/models/lane";
import { Flow } from "@mug/models/flow";

export interface Item {
  id: string;
  title: string;
  description?: string;
  flow: Flow;
  lane: Lane;
  Person: Person[];
  Agent?: Agent;
  history: History[];
  tag?: Tag[];
}
