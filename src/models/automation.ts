export enum AutomationTriggerType {
  RECORD_CREATED = "record_created",
  RECORD_UPDATED = "record_updated",
  RECORD_DELETED = "record_deleted",
  FIELD_CHANGED = "field_changed",
  STATUS_CHANGED = "status_changed",
  SCHEDULED = "scheduled",
  WEBHOOK = "webhook",
  FORM_SUBMITTED = "form_submitted",
}

export interface AutomationTrigger {
  id: string;
  type: AutomationTriggerType;
  config: string;
}

export enum AutomationActionType {
  SEND_EMAIL = "send_email",
  SEND_NOTIFICATION = "send_notification",
  UPDATE_RECORD = "update_record",
  CREATE_RECORD = "create_record",
  ASSIGN_TASK = "assign_task",
  EXECUTE_WEBHOOK = "execute_webhook",
  RUN_AGENT = "run_agent"
}

export interface AutomationAction {
  id: string;
  type: AutomationActionType;
  config: string;
}

export enum AutomationConditionOperator {
  EQUALS = "equals",
  NOT_EQUALS = "not_equals",
  CONTAINS = "contains",
  NOT_CONTAINS = "not_contains",
  GREATER_THAN = "greater_than",
  LESS_THAN = "less_than",
  IS_EMPTY = "is_empty",
  IS_NOT_EMPTY = "is_not_empty"
}

export interface AutomationCondition {
  id: string;
  field: string;
  operator: AutomationConditionOperator;
  value: string;
}

export interface Automation {
  id: string;
  name: string;
  description?: string;
  trigger: AutomationTrigger;
  actions: AutomationAction[];
  conditions?: AutomationCondition[];
  status: boolean;
  createdAt?: string;
  updatedAt?: string;
}
