export type PersonType = "person" | "company";

export type Person =
  | {
      id: string;
      type: "person";
      name: string;
      email?: string;
      phone?: string;
      documents?: PersonDocument[];
      contacts?: PersonContact[];
      addresses?: PersonAddress[];
      status: boolean;
      createdAt?: string;
      updatedAt?: string;
    }
  | {
      id: string;
      type: "company";
      name: string;
      email?: string;
      phone?: string;
      documents?: PersonDocument[];
      contacts?: PersonContact[];
      addresses?: PersonAddress[];
      partners?: Person[];
      status: boolean;
      createdAt?: string;
      updatedAt?: string;
    };

export interface PersonDocument {
  id: string;
  name: string;
  value: string;
}

export type PersonContactType = "email" | "phone" | "whatsapp" | "telegram";

export interface PersonContact {
  id: string;
  type: PersonContactType;
  name: string;
  value: string;
}

export interface PersonAddress {
  id: string;
  name: string;
  street: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}
