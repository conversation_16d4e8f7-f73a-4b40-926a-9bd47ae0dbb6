/**
 * Plan model - represents a subscription plan in the system
 */
export interface Plan {
  id: string;
  name: string;
  description: string;
  price: number;
  billingCycle: BillingCycle;
  features: PlanFeature[];
  limits: PlanLimits;
  isPopular?: boolean;
  isEnterprise?: boolean;
}

/**
 * Billing Cycle - defines the billing cycle of a plan
 */
export type BillingCycle = "monthly" | "yearly" | "custom";

/**
 * Plan Feature - represents a feature in a plan
 */
export interface PlanFeature {
  id: string;
  name: string;
  description?: string;
  included: boolean;
}

/**
 * Plan Limits - defines the limits of a plan
 */
export interface PlanLimits {
  users: number;
  flows: number;
  records: number;
  storage: number; // in GB
  landingPages: number;
  forms: number;
  automations: number;
  customDomain: boolean;
  api: boolean;
  whiteLabel: boolean;
}

/**
 * Subscription - represents a user's subscription to a plan
 */
export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  plan: Plan;
  status: SubscriptionStatus;
  startDate: string;
  endDate?: string;
  trialEndDate?: string;
  isInTrial: boolean;
  autoRenew: boolean;
  paymentMethod?: PaymentMethod;
  billingInfo?: BillingInfo;
  usage: UsageData;
  invoices: Invoice[];
  users: SubscriptionUser[];
}

/**
 * Subscription Status - defines the status of a subscription
 */
export type SubscriptionStatus = 
  | "active" 
  | "trialing" 
  | "past_due" 
  | "canceled" 
  | "unpaid" 
  | "expired";

/**
 * Payment Method - represents a payment method
 */
export interface PaymentMethod {
  id: string;
  type: "credit_card" | "bank_account" | "paypal" | "other";
  lastFour?: string;
  expiryDate?: string;
  isDefault: boolean;
}

/**
 * Billing Info - represents billing information
 */
export interface BillingInfo {
  name: string;
  email: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  taxId?: string;
  companyName?: string;
}

/**
 * Usage Data - represents the usage data of a subscription
 */
export interface UsageData {
  users: UsageItem;
  flows: UsageItem;
  records: UsageItem;
  storage: UsageItem;
  landingPages: UsageItem;
  forms: UsageItem;
  automations: UsageItem;
}

/**
 * Usage Item - represents a usage item
 */
export interface UsageItem {
  used: number;
  limit: number;
  percentage: number;
  warningLevel: WarningLevel;
}

/**
 * Warning Level - defines the warning level of a usage item
 */
export type WarningLevel = "safe" | "warning" | "critical";

/**
 * Invoice - represents an invoice
 */
export interface Invoice {
  id: string;
  subscriptionId: string;
  amount: number;
  status: "paid" | "unpaid" | "void" | "draft";
  issueDate: string;
  dueDate: string;
  paidDate?: string;
  items: InvoiceItem[];
  pdf?: string;
}

/**
 * Invoice Item - represents an item in an invoice
 */
export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

/**
 * Subscription User - represents a user in a subscription
 */
export interface SubscriptionUser {
  id: string;
  userId: string;
  name: string;
  email: string;
  role: UserRole;
  status: "active" | "invited" | "suspended";
  invitedAt?: string;
  joinedAt?: string;
}

/**
 * User Role - defines the role of a user in a subscription
 */
export type UserRole = 
  | "owner" 
  | "admin" 
  | "manager" 
  | "member" 
  | "viewer";

/**
 * Role Permission - defines the permissions of a role
 */
export interface RolePermission {
  role: UserRole;
  permissions: {
    manageSubscription: boolean;
    manageUsers: boolean;
    manageBilling: boolean;
    manageFlows: boolean;
    viewReports: boolean;
    manageRecords: boolean;
    manageLandingPages: boolean;
    manageForms: boolean;
    manageAutomations: boolean;
  };
}
