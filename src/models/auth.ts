import { User } from "@mug/models/user";
import {
  LoginCredentialsDto,
  TwoFactorVerificationDto,
  PasswordResetCompletionDto,
  InvitationAcceptanceDto,
  AuthSessionDto
} from "@mug/services/dtos";

// Keep this type here as it's used in multiple places
export type TwoFactorMethod = "app";

export interface MagicLinkToken {
  token: string;
  email: string;
  expiresAt: string;
  used: boolean;
}

// Use AuthSessionDto from @mug/services/dtos instead
export type AuthSession = AuthSessionDto;

export interface PasswordResetToken {
  token: string;
  userId: string;
  expiresAt: string;
  used: boolean;
}

export interface UserInvitation {
  id: string;
  email: string;
  name?: string;
  role: string;
  token: string;
  invitedBy: string;
  invitedAt: string;
  expiresAt: string;
  accepted: boolean;
  acceptedAt?: string;
}

export interface GoogleAuthResponse {
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
    picture?: string;
  };
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  requiresTwoFactor: boolean;
  session: AuthSession | null;
  error: string | null;
}

export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  requiresTwoFactor: boolean;
  login: (credentials: LoginCredentialsDto) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  requestMagicLink: (email: string) => Promise<void>;
  verifyMagicLink: (token: string) => Promise<void>;
  verifyTwoFactor: (verification: TwoFactorVerificationDto) => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (reset: PasswordResetCompletionDto) => Promise<void>;
  acceptInvitation: (acceptance: InvitationAcceptanceDto) => Promise<void>;
  logout: () => Promise<void>;
  error: string | null;
  clearError: () => void;
}
