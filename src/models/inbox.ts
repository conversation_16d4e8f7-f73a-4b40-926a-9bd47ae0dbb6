/**
 * Inbox Models
 */

import { User } from "./user";

/**
 * Channel types
 */
export enum ChannelType {
  WHATSAPP = "whatsapp",
  EMAIL = "email",
  CHAT = "chat",
  TELEGRAM = "telegram",
  SMS = "sms",
  FACEBOOK = "facebook",
  INSTAGRAM = "instagram",
  TWITTER = "twitter",
  LINKEDIN = "linkedin",
  GOOGLE_BUSINESS = "google_business",
  INTERNAL = "internal",
  VOIP = "voip"
}

/**
 * Channel status
 */
export enum ChannelStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  CONFIGURING = "configuring",
  ERROR = "error"
}

/**
 * Message status
 */
export enum MessageStatus {
  SENT = "sent",
  DELIVERED = "delivered",
  READ = "read",
  FAILED = "failed",
  PENDING = "pending"
}

/**
 * Conversation status
 */
export enum ConversationStatus {
  NEW = "new",
  OPEN = "open",
  PENDING = "pending",
  RESOLVED = "resolved",
  ARCHIVED = "archived"
}

/**
 * Priority levels
 */
export enum PriorityLevel {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent"
}

/**
 * Message type
 */
export enum MessageType {
  TEXT = "text",
  IMAGE = "image",
  VIDEO = "video",
  AUDIO = "audio",
  FILE = "file",
  LOCATION = "location",
  CONTACT = "contact",
  TEMPLATE = "template",
  SYSTEM = "system",
  NOTE = "note"
}

/**
 * Call direction
 */
export enum CallDirection {
  INBOUND = "inbound",
  OUTBOUND = "outbound"
}

/**
 * Call status
 */
export enum CallStatus {
  RINGING = "ringing",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  MISSED = "missed",
  BUSY = "busy",
  FAILED = "failed",
  VOICEMAIL = "voicemail",
  CANCELED = "canceled"
}

/**
 * Channel configuration
 */
export interface Channel {
  id: string;
  type: ChannelType;
  name: string;
  status: ChannelStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  icon?: string;
  color?: string;
  config: Record<string, any>;
  isDefault?: boolean;
  businessHours?: BusinessHours;
  autoReply?: AutoReply;
}

/**
 * Business hours configuration
 */
export interface BusinessHours {
  timezone: string;
  enabled: boolean;
  schedule: {
    day: number; // 0-6 (Sunday-Saturday)
    start: string; // HH:MM format
    end: string; // HH:MM format
  }[];
  outOfOfficeMessage?: string;
}

/**
 * Auto reply configuration
 */
export interface AutoReply {
  enabled: boolean;
  message: string;
  delay?: number; // Delay in seconds
  conditions?: {
    type: string;
    value: any;
  }[];
}

/**
 * Contact information
 */
export interface Contact {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  avatar?: string;
  company?: string;
  title?: string;
  createdAt: string;
  updatedAt: string;
  channels: {
    type: ChannelType;
    value: string; // Phone number, email, username, etc.
    isDefault?: boolean;
  }[];
  tags?: string[];
  customFields?: Record<string, any>;
  notes?: string;
  crmId?: string; // Reference to CRM system
}

/**
 * Conversation (thread)
 */
export interface Conversation {
  id: string;
  channelId: string;
  channelType: ChannelType;
  contactId: string;
  subject?: string;
  status: ConversationStatus;
  priority: PriorityLevel;
  assignedTo?: string; // User ID
  createdAt: string;
  updatedAt: string;
  lastMessageAt: string;
  lastMessagePreview?: string;
  unreadCount: number;
  tags?: string[];
  customFields?: Record<string, any>;
  isGroup?: boolean; // For internal conversations
  participants?: string[]; // User IDs for group conversations
  metadata?: Record<string, any>;
}

/**
 * Message
 */
export interface Message {
  id: string;
  conversationId: string;
  channelId: string;
  channelType: ChannelType;
  type: MessageType;
  content: string;
  senderId: string;
  senderType: "user" | "contact" | "system";
  status: MessageStatus;
  createdAt: string;
  updatedAt: string;
  deliveredAt?: string;
  readAt?: string;
  attachments?: Attachment[];
  metadata?: Record<string, any>;
  replyToId?: string; // ID of the message being replied to
  isPrivate?: boolean; // For internal notes
  mentions?: string[]; // User IDs mentioned in the message
}

/**
 * Attachment
 */
export interface Attachment {
  id: string;
  messageId: string;
  type: "image" | "video" | "audio" | "file";
  url: string;
  name: string;
  size: number;
  mimeType: string;
  thumbnailUrl?: string;
  metadata?: Record<string, any>;
}

/**
 * Call record
 */
export interface Call {
  id: string;
  conversationId: string;
  channelId: string;
  contactId: string;
  direction: CallDirection;
  status: CallStatus;
  startTime?: string;
  endTime?: string;
  duration?: number; // In seconds
  recordingUrl?: string;
  transcriptionUrl?: string;
  notes?: string;
  assignedTo?: string; // User ID
  createdAt: string;
  updatedAt: string;
  phoneNumber: string;
  metadata?: Record<string, any>;
}

/**
 * Template for quick replies
 */
export interface MessageTemplate {
  id: string;
  name: string;
  content: string;
  channelTypes: ChannelType[];
  tags?: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  isGlobal: boolean;
  variables?: string[]; // Placeholders like {{name}}, {{company}}
}

/**
 * Tag for organization
 */
export interface Tag {
  id: string;
  name: string;
  color: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

/**
 * Metrics for reporting
 */
export interface InboxMetrics {
  period: {
    start: string;
    end: string;
  };
  conversations: {
    total: number;
    new: number;
    resolved: number;
    averageResolutionTime: number; // In minutes
  };
  messages: {
    total: number;
    sent: number;
    received: number;
    byChannel: Record<ChannelType, number>;
  };
  calls: {
    total: number;
    inbound: number;
    outbound: number;
    missed: number;
    averageDuration: number; // In seconds
  };
  performance: {
    averageResponseTime: number; // In minutes
    averageFirstResponseTime: number; // In minutes
    averageResolutionTime: number; // In minutes
  };
  operators: {
    id: string;
    name: string;
    conversationsHandled: number;
    messagesHandled: number;
    callsHandled: number;
    averageResponseTime: number;
    averageResolutionTime: number;
  }[];
}

/**
 * Operator availability status
 */
export enum OperatorStatus {
  AVAILABLE = "available",
  BUSY = "busy",
  AWAY = "away",
  OFFLINE = "offline"
}

/**
 * Operator settings
 */
export interface OperatorSettings {
  userId: string;
  status: OperatorStatus;
  autoReply?: {
    enabled: boolean;
    message: string;
  };
  notifications: {
    email: boolean;
    desktop: boolean;
    mobile: boolean;
  };
  channels: string[]; // Channel IDs the operator can handle
  signature?: string;
  workingHours?: BusinessHours;
}

/**
 * Inbox state for UI
 */
export interface InboxState {
  activeConversation?: string;
  selectedMessages: string[];
  filters: {
    status?: ConversationStatus[];
    channels?: ChannelType[];
    assignedTo?: string[];
    priority?: PriorityLevel[];
    tags?: string[];
    dateRange?: {
      start: string;
      end: string;
    };
    search?: string;
  };
  view: "list" | "split" | "detail";
  sortBy: "date" | "priority" | "status";
  sortDirection: "asc" | "desc";
}
