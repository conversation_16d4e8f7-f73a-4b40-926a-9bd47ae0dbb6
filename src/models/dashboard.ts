/**
 * Dashboard model - represents a dashboard in the system
 */
export interface Dashboard {
  id: string;
  name: string;
  description?: string;
  flowId?: string; // If null, it's a general dashboard
  widgets: DashboardWidget[];
  isDefault?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Dashboard Widget - represents a visual component in a dashboard
 */
export interface DashboardWidget {
  id: string;
  type: WidgetType;
  title: string;
  description?: string;
  size: WidgetSize;
  position: {
    x: number;
    y: number;
  };
  config: Record<string, unknown>;
  data?: any; // The actual data to display
}

/**
 * Widget Type - defines the type of widget
 */
export type WidgetType =
  | "bar_chart"
  | "line_chart"
  | "pie_chart"
  | "area_chart"
  | "donut_chart"
  | "counter"
  | "gauge"
  | "table"
  | "list"
  | "kanban"
  | "funnel"
  | "heatmap"
  | "custom";

/**
 * Widget Size - defines the size of widget
 */
export type WidgetSize =
  | "small" // 1x1
  | "medium" // 2x1
  | "large" // 2x2
  | "wide" // 3x1
  | "tall" // 1x2
  | "full" // 3x2
  | "custom"; // Custom size

/**
 * Dashboard Metric - represents a metric in a dashboard
 */
export interface DashboardMetric {
  id: string;
  name: string;
  value: number;
  previousValue?: number;
  change?: number;
  changeType?: "positive" | "negative" | "neutral";
  unit?: string;
  format?: string;
}

/**
 * Dashboard Chart Data - represents data for a chart widget
 */
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

/**
 * Dashboard Table Data - represents data for a table widget
 */
export interface TableData {
  headers: string[];
  rows: any[][];
}

/**
 * Dashboard List Data - represents data for a list widget
 */
export interface ListData {
  items: {
    id: string;
    title: string;
    subtitle?: string;
    value?: number;
    icon?: string;
    color?: string;
  }[];
}

/**
 * Dashboard Funnel Data - represents data for a funnel widget
 */
export interface FunnelData {
  stages: {
    name: string;
    value: number;
    percentage?: number;
  }[];
}

/**
 * Dashboard Kanban Data - represents data for a kanban widget
 */
export interface KanbanData {
  columns: {
    id: string;
    title: string;
    items: {
      id: string;
      title: string;
      subtitle?: string;
    }[];
  }[];
}
