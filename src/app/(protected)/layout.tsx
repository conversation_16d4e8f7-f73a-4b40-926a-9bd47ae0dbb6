import { ReactNode } from "react";
import type { Metadata } from "next";

import { ProtectedRoute } from "@mug/contexts/auth";
import { Header } from "@mug/components/header";
import { AppSidebar } from "@mug/components/sidebar";
import { ScrollArea } from "@mug/components/ui/scroll-area";
import { AppNavBar } from "@mug/components/navbar";

export const metadata: Metadata = {
  title: "Flows",
  description: "Sistema de gerenciamento de fluxos de trabalho",
};

export default function ProtectedLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <ProtectedRoute>
      <div className="flex flex-col max-h-screen max-w-screen w-screen h-screen dark:bg-neutral-950 overflow-hidden">
        <Header />
        <div className="flex flex-1 overflow-hidden">
          <main className="flex-1 flex relative bg-card overflow-hidden">
            <ScrollArea className="w-full h-full overflow-y-auto overflow-x-hidden">
              <div className="h-20"/>
              {children}
            </ScrollArea>
          </main>
        </div>
        <AppNavBar />
      </div>
    </ProtectedRoute>
  );
}
