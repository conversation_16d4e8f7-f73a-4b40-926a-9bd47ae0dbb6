import { ReactNode } from "react";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Flows",
  description: "Sistema de gerenciamento de fluxos de trabalho",
};

interface AuthLayoutProps {
  children: ReactNode;
}
   
export default function RootLayout(props: Readonly<AuthLayoutProps>) {
  const { children } = props;
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-neutral-100 dark:bg-neutral-950 overflow-hidden">
      <svg
        className="h-12 mb-8 w-auto select-none"
        xmlns="http://www.w3.org/2000/svg"
        width="1018"
        height="472"
        viewBox="0 0 1018 472"
        fill="none"
      >
        <path
          className="fill-black dark:fill-white"
          d="M109.866 49.5146C128.133 13.3813 168.4 -5.15195 214 1.24805C219.2 2.04805 226.133 3.51439 229.467 4.58106C238.133 7.51438 239.2 10.4478 236.8 24.3145C235.733 30.3145 234.533 39.7814 234 45.248C232.533 61.1144 230.533 62.048 209.733 56.9814C198.8 54.4481 196.8 54.3146 190 55.7812C180 58.0479 170.934 66.581 166.934 77.3809C163.467 86.8474 159.867 110.447 161.467 113.381C163.067 116.581 168.533 117.248 191.467 117.248C208.133 117.248 212.134 117.648 213.867 119.381C216.534 121.914 216.533 120.715 213.333 145.381C209.6 173.381 211.333 171.914 179.867 171.914C157.734 171.914 155.866 172.048 153.333 174.581C151.866 176.048 150.667 177.915 150.667 178.715C150.666 179.389 148 196.985 144.667 217.647C141.467 238.314 134.533 282.581 129.333 315.914C124.133 349.247 119.067 380.181 118 384.581C115.467 394.448 107.333 411.914 100.267 422.181C94.5333 430.714 79.7338 445.248 70.1338 451.781C58.8005 459.648 42.667 466.315 28.4004 469.248C13.0671 472.315 3.99961 472.714 1.59961 470.314C0.533054 469.247 0 461.78 0 445.647C0 422.848 1.4782e-05 422.448 3.33301 419.248C5.06634 417.381 8.00006 415.914 9.7334 415.914C16.1334 415.914 28.933 408.048 37.5996 398.848C47.3329 388.448 53.5998 377.381 56.5332 365.248C58.2665 358.181 77.4669 229.647 82.9336 187.647C85.0669 171.781 85.1996 171.914 65.0664 171.914C56.2665 171.914 47.9999 171.248 46.5332 170.581C44.4 169.381 44 167.781 44 160.181C44.1334 146.981 47.333 121.65 49.333 119.248C50.6663 117.648 54.2666 117.248 67.333 117.248C84.3997 117.248 91.1997 115.914 93.333 111.914C93.9997 110.58 95.8664 101.914 97.333 92.4473C100.133 74.8476 104.4 60.1812 109.866 49.5146ZM385.467 127.381C410.667 114.048 438.267 110.715 467.467 117.248C524.133 130.181 546.934 184.181 527.867 260.181C519.867 291.647 508.266 314.314 491.333 331.381C466.533 356.447 437.467 367.647 400.667 366.181C380.667 365.381 371.6 363.247 356.4 355.914C344.134 349.914 330.8 337.915 324.8 327.515C308.667 299.915 308.934 250.981 325.467 202.981C338.667 164.581 355.733 143.114 385.467 127.381ZM266.667 3.64746C268.535 3.1142 280.667 2.84813 293.6 2.98145C316.132 3.24811 317.467 3.38089 320.267 6.18067L323.2 9.11426L318.934 38.1807C304.667 133.247 288.534 238.98 283.2 271.914C276.134 315.781 275.467 328.448 279.867 340.448C284.8 354.181 283.6 357.915 273.066 362.715C266.933 365.381 263.866 365.914 250 365.914C232.4 365.914 228.667 364.848 220.4 356.848C213.467 350.314 211.467 342.048 212.533 324.715C214.4 293.915 256.932 18.1883 261.066 9.24805C262.4 6.58138 264.667 4.1808 266.667 3.64746ZM928.134 116.581C940.801 113.648 977.467 114.714 990.267 118.181C1002.8 121.647 1011.87 126.314 1015.2 130.848C1017.6 134.314 1017.73 135.248 1016.13 147.515C1013.33 170.848 1009.6 181.248 1004 181.248C1002.67 181.248 996.533 178.581 990.4 175.248C971.6 165.248 950.667 161.514 935.2 165.381C918.4 169.648 913.6 189.114 926.4 200.848C928.934 203.248 942.267 210.848 955.867 217.781C998.933 239.781 1009.33 251.648 1009.33 278.581C1009.33 290.848 1005.6 306.714 999.733 318.581C993.467 331.648 977.334 347.248 962.667 354.448C943.067 363.915 933.866 365.781 905.333 365.781C882.667 365.781 879.6 365.381 867.601 362.181C854.934 358.714 840.134 352.582 835.867 348.981C834.801 348.048 833.467 346.181 832.934 344.715C831.334 340.448 839.467 301.781 842.934 297.647C845.334 294.714 849.067 295.648 857.2 301.248C873.733 312.581 890.134 317.648 910.667 317.781C923.333 317.915 925.333 317.514 932 314.314C943.733 308.581 948 303.647 948 295.914C948 283.914 941.333 278.714 890 250.314C885.6 247.914 877.733 241.648 872.533 236.581C864.133 228.315 862.667 226.048 860.134 218.048C852.267 192.715 860.534 160.448 879.867 141.114C892.667 128.448 906.134 121.514 928.134 116.581ZM826.8 117.248C855.466 117.248 855.467 117.248 854.267 127.781C853.866 131.516 841.066 166.982 824.533 210.181C808.667 251.781 789.733 301.514 782.533 320.581C772 348.181 768.533 356.048 765.333 358.848L761.333 362.581H736.533C711.467 362.581 707.866 362.048 705.333 357.381C704.133 354.979 701.333 324.311 696.533 259.914C694.267 228.981 693.333 222.581 691.333 222.314C688.8 222.049 685.332 230.184 662.533 289.914C650 322.847 638.667 352.047 637.2 354.848C635.734 357.648 633.067 360.581 631.2 361.248C629.334 362.048 616.134 362.581 601.867 362.581C576.267 362.581 575.867 362.581 572.667 359.381C568.934 355.648 568.8 354.447 565.333 311.914C563.866 293.914 562 272.581 561.333 264.581C556 203.914 550.667 137.114 550.667 130.581C550.667 117.248 550.8 117.248 583.333 117.248C610.132 117.248 610.667 117.248 612.533 120.181C614.133 122.714 614.801 135.248 615.867 184.581C616.667 218.314 617.333 250.447 617.333 255.914C617.333 272.314 621.2 273.514 626.8 258.848C632.133 244.714 656.933 175.382 665.333 151.248C674.666 124.181 676.267 120.581 680 118.581C681.6 117.648 691.734 117.381 706.934 117.515L731.333 117.914L732.8 123.248C734.133 127.782 736.4 161.249 741.2 247.647C741.867 261.114 742.667 265.781 744.267 267.515C746.266 269.381 746.667 269.114 749.066 264.448C751.6 259.515 778.667 177.782 790.667 138.981C794.533 126.315 796.933 120.715 799.066 119.248C801.333 117.648 806.667 117.248 826.8 117.248ZM445.2 167.381C436.667 164.714 430.667 164.848 422.134 167.515C407.867 171.781 398 182.581 389.467 203.114C381.2 222.981 374.8 259.381 376.667 276.581C378.667 295.781 384.133 306.848 394.533 312.981C400.133 316.181 401.867 316.581 414 316.581C429.733 316.581 434.533 314.714 444.267 304.181C458.133 289.381 468.4 258.714 470.134 226.581C472.134 192.048 464 172.848 445.2 167.381Z"
          fill="black"
        />
      </svg>
      {children}
    </div>
  );
}
