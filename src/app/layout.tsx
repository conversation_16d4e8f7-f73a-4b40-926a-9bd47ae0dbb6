import '@mug/app/globals.css';

import { ReactNode } from "react";
import { DM_Sans } from "next/font/google";
import type { Metadata } from "next";

import { Toaster } from "@mug/components/ui/sonner";
import { TranslateProvider } from "@mug/contexts/translate";
import { ThemeProvider } from "@mug/contexts/theme";
import { AuthProvider } from "@mug/contexts/auth";

import { classNameBuilder } from "@mug/lib/utils/class-name-builder";


const dmSans = DM_Sans({
  variable: "--font-title",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Flows",
  description: "Sistema de gerenciamento de fluxos de trabalho",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <html lang="pt-BR" className="light">
      <body
        className={classNameBuilder(
          "antialiased",
          dmSans.variable,
        )}
      >
        <TranslateProvider>
          <ThemeProvider>
            <AuthProvider>
              <Toaster position="bottom-right" richColors />
              {children}
            </AuthProvider>
          </ThemeProvider>
        </TranslateProvider>
      </body>
    </html>
  );
}
