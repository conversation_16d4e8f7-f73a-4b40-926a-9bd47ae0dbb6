"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { useAuth } from "@mug/contexts/auth";

import { TwoFactorForm } from "@mug/features/auth";

export function TwoFactorVerifyPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnUrl = searchParams.get("returnUrl") || undefined;

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login");
    }
    // Não redirecionamos se requiresTwoFactor for false após uma verificação bem-sucedida
    // O contexto de autenticação já cuida do redirecionamento para o dashboard
  }, [isAuthenticated, isLoading, router]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-background">
        <div className="w-full max-w-md space-y-8">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  // Don't render anything if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Se o usuário está autenticado mas não requer 2FA, o redirecionamento
  // será tratado pelo contexto de autenticação

  return <TwoFactorForm returnUrl={returnUrl} />;
}
