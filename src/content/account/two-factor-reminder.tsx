"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

import { useAuth } from "@mug/contexts/auth";

import { TwoFactorReminder } from "@mug/features/auth/two-factor-reminder";

export function TwoFactorReminderPage() {
  const { user, isAuthenticated, isLoading, dismissTwoFactorReminder } =
    useAuth();
  const router = useRouter();

  // Redirect if not authenticated or 2FA is already enabled
  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push("/auth/login");
      } else if (user?.twoFactorEnabled) {
        router.push("/dashboard");
      }
    }
  }, [isAuthenticated, isLoading, router, user]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-background">
        <div className="w-full max-w-md space-y-8">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  // Don't render anything if not authenticated or 2FA is already enabled
  if (!isAuthenticated || user?.twoFactorEnabled) {
    return null;
  }

  const handleSetupNow = () => {
    // Redirect to profile page with 2FA setup section
    router.push("/profile?section=security");
  };

  const handleRemindLater = () => {
    // Use the dismissTwoFactorReminder function from auth context
    // This will store the current date and update the state
    dismissTwoFactorReminder();

    // Redirect to dashboard
    router.push("/dashboard");
  };

  return (
    <TwoFactorReminder
      onSetupNow={handleSetupNow}
      onRemindLater={handleRemindLater}
    />
  );
}
