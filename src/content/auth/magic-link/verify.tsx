"use client";

import { useSearchParams } from "next/navigation";
import { MagicLinkVerify } from "@mug/features/auth";

interface VerifyMagicLinkPageProps {
  params: {
    token: string;
  };
}
export function MagicLinkVerifyPage({ params }: VerifyMagicLinkPageProps) {
  const { token } = params;
  const searchParams = useSearchParams();
  const returnUrl = searchParams.get("returnUrl") || undefined;

  return <MagicLinkVerify token={token} returnUrl={returnUrl} />;
}
