/**
 * Data transfer object for message
 */
export interface MessageDto {
  id: string;
  conversationId: string;
  sender: {
    id: string;
    name: string;
    avatar?: string;
    type: 'user' | 'contact' | 'system';
  };
  content: string;
  attachments?: AttachmentDto[];
  timestamp: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  type: 'text' | 'image' | 'file' | 'audio' | 'video' | 'location' | 'contact' | 'system';
  metadata?: Record<string, unknown>;
}

/**
 * Data transfer object for conversation
 */
export interface ConversationDto {
  id: string;
  title?: string;
  participants: ParticipantDto[];
  lastMessage?: MessageDto;
  unreadCount: number;
  channel: ChannelTypeDto;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'archived' | 'deleted';
  metadata?: Record<string, unknown>;
}

/**
 * Data transfer object for participant
 */
export interface ParticipantDto {
  id: string;
  name: string;
  avatar?: string;
  type: 'user' | 'contact';
  role?: 'owner' | 'admin' | 'member';
  status?: 'active' | 'inactive' | 'left';
}

/**
 * Data transfer object for attachment
 */
export interface AttachmentDto {
  id: string;
  name: string;
  type: string;
  url: string;
  size?: number;
  thumbnail?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Channel type enum
 */
export type ChannelTypeDto = 'whatsapp' | 'email' | 'chat' | 'telegram' | 'voip' | 'internal';

/**
 * Data transfer object for sending a message
 */
export interface SendMessageDto {
  conversationId: string;
  content: string;
  attachments?: Omit<AttachmentDto, 'id'>[];
  type?: 'text' | 'image' | 'file' | 'audio' | 'video' | 'location' | 'contact';
  metadata?: Record<string, unknown>;
}

/**
 * Data transfer object for creating a conversation
 */
export interface CreateConversationDto {
  title?: string;
  participants: Omit<ParticipantDto, 'status'>[];
  channel: ChannelTypeDto;
  metadata?: Record<string, unknown>;
}
