/**
 * Data transfer object for notification preferences
 */
export interface NotificationPreferencesDto {
  sms?: boolean;
  whatsapp?: boolean;
  telegram?: boolean;
  email?: boolean;
  push?: boolean;
  inApp?: boolean;
  marketing?: boolean;
}

/**
 * Data transfer object for notification
 */
export interface NotificationDto {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  link?: string;
  read: boolean;
  createdAt: string;
  expiresAt?: string;
  priority?: 'low' | 'normal' | 'high';
  data?: Record<string, unknown>;
}

/**
 * Data transfer object for marking notifications as read
 */
export interface MarkNotificationsReadDto {
  notificationIds: string[];
}

/**
 * Data transfer object for notification count
 */
export interface NotificationCountDto {
  total: number;
  unread: number;
}
