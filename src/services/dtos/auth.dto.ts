/**
 * Login credentials DTO
 */
export interface LoginCredentialsDto {
  username: string;
  password: string;
}

/**
 * Magic link request DTO
 */
export interface MagicLinkRequestDto {
  email: string;
}

/**
 * Two-factor authentication method
 */
export type TwoFactorMethodDto = "app";

/**
 * Two-factor verification DTO
 */
export interface TwoFactorVerificationDto {
  method: TwoFactorMethodDto;
  code: string;
  userId: string;
  sessionId: string;
}

/**
 * Password reset request DTO
 */
export interface PasswordResetRequestDto {
  email: string;
}

/**
 * Password reset completion DTO
 */
export interface PasswordResetCompletionDto {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Invitation acceptance DTO
 */
export interface InvitationAcceptanceDto {
  token: string;
  name: string;
  password: string;
  confirmPassword: string;
}

/**
 * Authentication response DTO
 */
export interface AuthResponseDto {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

/**
 * Google login request DTO
 */
export interface GoogleLoginRequestDto {
  googleId: string;
  email: string;
  name: string;
  picture?: string;
  locale?: string;
}

/**
 * Authentication session DTO
 */
export interface AuthSessionDto {
  id: string;
  userId: string;
  token: string;
  refreshToken: string;
  expiresAt: string;
  lastActiveAt: string;
  ip?: string;
  userAgent?: string;
  device?: string;
  requiresTwoFactor?: boolean;
  twoFactorVerified?: boolean;
  tenantId?: string;
  createdAt: string;
}

/**
 * Enable two-factor authentication DTO
 */
export interface EnableTwoFactorDto {
  twoFactorKey: string;
  method: TwoFactorMethodDto;
  code: string;
}
