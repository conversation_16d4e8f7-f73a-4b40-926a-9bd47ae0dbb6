import { Language, Theme } from "@mug/models/core";
import { NotificationPreferences } from "@mug/models/user";
import { TwoFactorMethod } from "@mug/models/auth";

/**
 * Data transfer object for updating user profile
 */
export interface UpdateProfileDto {
  name?: string;
  email?: string;
  phone?: string;
  timezone?: string;
  language?: Language;
  theme?: Theme;
  notificationPreferences?: Partial<NotificationPreferences>;
}

/**
 * Data transfer object for changing password
 */
export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Data transfer object for setting up two-factor authentication
 */
export interface SetupTwoFactorDto {
  method: TwoFactorMethod;
}

/**
 * Data transfer object for verifying two-factor authentication
 */
export interface VerifyTwoFactorDto {
  code: string;
  method: TwoFactorMethod;
}

/**
 * Data transfer object for connecting Google account
 */
export interface ConnectGoogleDto {
  token: string;
}
