/**
 * Data transfer object for flow
 */
export interface FlowDto {
  id: string;
  name: string;
  description?: string;
  type: string;
  mode?: FlowModeDto;
  lanes: LaneDto[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'inactive' | 'archived';
  tenantId?: string;
}

/**
 * Flow mode enum
 */
export type FlowModeDto = 'ATTRACT' | 'CONVERT' | 'ENGAGE' | 'CLOSE' | 'DELIGHT' | 'ANALYZE';

/**
 * Data transfer object for lane
 */
export interface LaneDto {
  id: string;
  name: string;
  description?: string;
  order: number;
  flowId: string;
  wipLimit?: number;
  color?: string;
  restrictions?: LaneRestrictionDto[];
  modelTypes?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Data transfer object for lane restriction
 */
export interface LaneRestrictionDto {
  type: 'min_days' | 'max_days' | 'required_fields' | 'required_actions';
  value: string | number | string[];
}

/**
 * Data transfer object for creating a flow
 */
export interface CreateFlowDto {
  name: string;
  description?: string;
  type: string;
  mode?: FlowModeDto;
  lanes?: Partial<LaneDto>[];
  status?: 'active' | 'inactive';
}

/**
 * Data transfer object for updating a flow
 */
export interface UpdateFlowDto {
  name?: string;
  description?: string;
  type?: string;
  mode?: FlowModeDto;
  status?: 'active' | 'inactive' | 'archived';
}

/**
 * Data transfer object for creating a lane
 */
export interface CreateLaneDto {
  name: string;
  description?: string;
  order: number;
  flowId: string;
  wipLimit?: number;
  color?: string;
  restrictions?: LaneRestrictionDto[];
  modelTypes?: string[];
}

/**
 * Data transfer object for updating a lane
 */
export interface UpdateLaneDto {
  name?: string;
  description?: string;
  order?: number;
  wipLimit?: number;
  color?: string;
  restrictions?: LaneRestrictionDto[];
  modelTypes?: string[];
}
