import { Language, Theme } from "@mug/models/core";
import { NotificationPreferencesDto } from "./notification.dto";
import { SecuritySettingsDto } from "./security.dto";
import { TwoFactorMethod } from "@mug/models/auth";

/**
 * Data transfer object for user
 */
export interface UserDto {
  id: string;
  name: string;
  email: string;
  username?: string;
  avatar?: string;
  role?: string;
  roles?: string[];
  phone?: string;
  timezone?: string;
  language?: Language;
  theme?: Theme;
  lastLogin?: string;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  twoFactorEnabled?: boolean;
  twoFactorMethod?: TwoFactorMethod;
  googleConnected?: boolean;
  googleProfile?: GoogleProfileDto;
  notificationPreferences?: NotificationPreferencesDto;
  securitySettings?: SecuritySettingsDto;
  status?: boolean;
  tenantId?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Data transfer object for Google profile
 */
export interface GoogleProfileDto {
  id?: string;
  email?: string;
  name?: string;
  picture?: string;
  locale?: string;
}

/**
 * Data transfer object for creating a user
 */
export interface CreateUserDto {
  name: string;
  email: string;
  username?: string;
  password: string;
  role?: string;
  phone?: string;
  timezone?: string;
  language?: Language;
  theme?: Theme;
}

/**
 * Data transfer object for updating a user
 */
export interface UpdateUserDto {
  name?: string;
  email?: string;
  username?: string;
  role?: string;
  roles?: string[];
  phone?: string;
  timezone?: string;
  language?: Language;
  theme?: Theme;
  status?: boolean;
}
