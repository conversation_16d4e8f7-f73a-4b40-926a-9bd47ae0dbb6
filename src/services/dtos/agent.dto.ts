/**
 * Data transfer object for agent
 */
export interface AgentDto {
  id: string;
  name: string;
  description?: string;
  actions: AgentActionDto[];
  rules: AgentRuleDto[];
  model: string;
  status: 'active' | 'inactive' | 'training';
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lastRun?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Data transfer object for agent action
 */
export interface AgentActionDto {
  id: string;
  name: string;
  description?: string;
  type: 'api' | 'function' | 'workflow' | 'message';
  parameters?: AgentActionParameterDto[];
  enabled: boolean;
  metadata?: Record<string, unknown>;
}

/**
 * Data transfer object for agent action parameter
 */
export interface AgentActionParameterDto {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  required: boolean;
  default?: unknown;
}

/**
 * Data transfer object for agent rule
 */
export interface AgentRuleDto {
  id: string;
  description: string;
  condition: string;
  action: string;
  priority: number;
  enabled: boolean;
}

/**
 * Data transfer object for creating an agent
 */
export interface CreateAgentDto {
  name: string;
  description?: string;
  actions: Omit<AgentActionDto, 'id'>[];
  rules: Omit<AgentRuleDto, 'id'>[];
  model: string;
  metadata?: Record<string, unknown>;
}

/**
 * Data transfer object for updating an agent
 */
export interface UpdateAgentDto {
  name?: string;
  description?: string;
  actions?: Partial<AgentActionDto>[];
  rules?: Partial<AgentRuleDto>[];
  model?: string;
  status?: 'active' | 'inactive';
  metadata?: Record<string, unknown>;
}

/**
 * Data transfer object for agent execution
 */
export interface AgentExecutionDto {
  id: string;
  agentId: string;
  input: string;
  output?: string;
  actions: AgentExecutionActionDto[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  error?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Data transfer object for agent execution action
 */
export interface AgentExecutionActionDto {
  actionId: string;
  name: string;
  parameters: Record<string, unknown>;
  result?: unknown;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  error?: string;
}
