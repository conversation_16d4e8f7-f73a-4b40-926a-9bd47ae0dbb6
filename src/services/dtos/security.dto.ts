/**
 * Data transfer object for security settings
 */
export interface SecuritySettingsDto {
  loginNotifications?: boolean;
  unusualActivityAlerts?: boolean;
  sessionTimeout?: number;
}

/**
 * Data transfer object for session information
 */
export interface SessionInfoDto {
  id: string;
  device?: string;
  browser?: string;
  os?: string;
  ip?: string;
  location?: string;
  lastActive: string;
  current: boolean;
}

/**
 * Data transfer object for revoking a session
 */
export interface RevokeSessionDto {
  sessionId: string;
}

/**
 * Data transfer object for activity log entry
 */
export interface ActivityLogEntryDto {
  id: string;
  userId: string;
  action: string;
  details: string;
  ip?: string;
  userAgent?: string;
  location?: string;
  timestamp: string;
}
