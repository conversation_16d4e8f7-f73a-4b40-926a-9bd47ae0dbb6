import { Pagination } from "@mug/models/core";

/**
 * Error response from API
 */
export interface ErrorResponseDto {
  success?: false;
  id?: string;
  code?: string;
  details?: string;
  error?: string;
  error_description?: string; // OAuth style error description
}

/**
 * Success response with a single item
 */
export interface SuccessResponseSingleDto<T> {
  success: true;
  data: T;
}

/**
 * Success response with multiple items and pagination
 */
export interface SuccessResponseManyDto<T> {
  success: true;
  data: T[];
  pagination: Pagination;
}
