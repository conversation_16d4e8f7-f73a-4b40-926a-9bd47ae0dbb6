import { toast } from "sonner";
import { LaneModelType, LanePosition, LaneRestriction } from "@mug/models/flow";
import { MockLane } from "@mug/constants/mock-lanes";
import {
  SuccessResponseMany,
  SuccessResponseSingle,
  ErrorResponse,
} from "@mug/services/request";

// DTOs for Lane service
export interface CreateLaneDTO {
  title: string;
  description?: string;
  wipLimit?: number;
  allowCreate?: boolean;
  restriction?: LaneRestriction;
  model?: LaneModelType;
  position: LanePosition;
  flowId: string;
  // Background color for the lane header
  backgroundColor?: string;
  // Conversion models (landing pages or forms) associated with this lane
  conversionModelIds?: string[]; // IDs of associated conversion models
  // Actions to be triggered when an item is created or moved to this lane
  actionIds?: string[]; // IDs of associated actions
}

export interface UpdateLaneDTO {
  title: string;
  description?: string;
  wipLimit?: number;
  allowCreate?: boolean;
  restriction?: LaneRestriction;
  model?: LaneModelType;
  // Background color for the lane header
  backgroundColor?: string;
  // Conversion models (landing pages or forms) associated with this lane
  conversionModelIds?: string[]; // IDs of associated conversion models
  // Actions to be triggered when an item is created or moved to this lane
  actionIds?: string[]; // IDs of associated actions
}

export const createLane = async (data: CreateLaneDTO): Promise<MockLane> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/flows/${data.flowId}/lanes`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        const { data: responseData }: SuccessResponseSingle<MockLane> = await res.json();
        resolve(responseData);
      }),
      {
        loading: "Creating lane...",
        success: (): string => "Lane created successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

export const updateLane = async (laneId: string, flowId: string, data: UpdateLaneDTO): Promise<MockLane> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/flows/${flowId}/lanes/${laneId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        const { data: responseData }: SuccessResponseSingle<MockLane> = await res.json();
        resolve(responseData);
      }),
      {
        loading: "Updating lane...",
        success: (): string => "Lane updated successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

export const deleteLane = async (laneId: string, flowId: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/flows/${flowId}/lanes/${laneId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting lane...",
        success: (): string => "Lane deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

export const moveLane = async (laneId: string, flowId: string, position: LanePosition): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/flows/${flowId}/lanes/${laneId}/move`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ position }),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Moving lane...",
        success: (): string => "Lane moved successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
