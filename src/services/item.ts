import { toast } from "sonner";
import { MockItem } from "@mug/constants/mock-lanes";
import {
  SuccessResponseMany,
  SuccessResponseSingle,
  ErrorResponse,
} from "@mug/services/request";

// DTOs for Item service
export interface CreateItemDTO {
  title: string;
  description?: string;
  priority?: "high" | "medium" | "low";
  laneId: string;
  assigneeId?: string;
  personId?: string;
  dueDate?: string;
  tags?: string[];
}

export interface UpdateItemDTO {
  title: string;
  description?: string;
  priority?: "high" | "medium" | "low";
  laneId: string;
  assigneeId?: string;
  personId?: string;
  dueDate?: string;
  tags?: string[];
}

export const createItem = async (data: CreateItemDTO): Promise<MockItem> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/lanes/${data.laneId}/items`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        const { data: responseData }: SuccessResponseSingle<MockItem> = await res.json();
        resolve(responseData);
      }),
      {
        loading: "Creating item...",
        success: (): string => "Item created successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

export const updateItem = async (itemId: string, laneId: string, data: UpdateItemDTO): Promise<MockItem> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/lanes/${laneId}/items/${itemId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        const { data: responseData }: SuccessResponseSingle<MockItem> = await res.json();
        resolve(responseData);
      }),
      {
        loading: "Updating item...",
        success: (): string => "Item updated successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

export const deleteItem = async (itemId: string, laneId: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/lanes/${laneId}/items/${itemId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting item...",
        success: (): string => "Item deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

export const moveItem = async (itemId: string, sourceLaneId: string, targetLaneId: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/lanes/${sourceLaneId}/items/${itemId}/move`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ targetLaneId }),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Moving item...",
        success: (): string => "Item moved successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
