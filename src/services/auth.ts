// Models
import { User } from "@mug/models/user";

// Services
import apiClient from "@mug/services/api-client";
import authMapper, { UserJwtPayload } from "@mug/mappers/auth-mapper";

/**
 * Interface for JWT token payload
 */
export interface JwtPayload {
  [key: string]: unknown;
  exp?: number;
  iat?: number;
  user_id?: string;
}

export function decodeToken(token: string): JwtPayload {
  try {
    const base64Url = token.split('.')[1];
    if (!base64Url) {
      throw new Error('Invalid token format: missing payload section');
    }

    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload) as JwtPayload;
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    throw new Error('Invalid token format');
  }
}

export function validateTokenPayload(payload: JwtPayload, requiredFields: string[]): void {
  for (const field of requiredFields) {
    if (payload[field] === undefined) {
      throw new Error(`Token missing required field: ${field}`);
    }
  }
}

export function isTokenExpired(payload: JwtPayload): boolean {
  if (!payload.exp) return true;
  const expirationDate = new Date(payload.exp * 1000);
  return expirationDate <= new Date();
}

export const jwtService = {
  decodeToken,
  validateTokenPayload,
  isTokenExpired
};

import {
  AuthResponseDto,
  AuthSessionDto,
  InvitationAcceptanceDto,
  LoginCredentialsDto,
  PasswordResetCompletionDto,
  TwoFactorVerificationDto,
} from "@mug/services/dtos";

export const login = async (
  credentials: LoginCredentialsDto
): Promise<{ user: User; session: AuthSessionDto }> => {
  try {
    const headers = {
      "X-Tenant-ID": "131b3588-5090-473a-bf32-2b69bf3ce1b6",
      "Content-Type": "application/json",
    };

    const authResponse = await apiClient.post<AuthResponseDto>(
      "/v1/auth/login",
      credentials as unknown as Record<string, unknown>,
      {
        loading: "Authenticating...",
        success: "Login successful!",
        error: (err) => err.message || "Login failed",
      },
      { headers, withCredentials: true }
    );

    const tokenData = decodeToken(authResponse.accessToken);
    validateTokenPayload(tokenData, ["user_id", "exp"]);

    const user = authMapper.mapTokenToUser(tokenData as UserJwtPayload);
    const session = authMapper.mapTokenToSession(
      tokenData as UserJwtPayload,
      authResponse.accessToken,
      authResponse.refreshToken
    );

    return { user, session };
  } catch (error) {
    console.error("Login error:", error);
    throw error;
  }
};

const generateState = (): string => {
  if (typeof window !== "undefined" && window.crypto) {
    const array = new Uint8Array(16);
    window.crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
      ""
    );
  }

  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
};

const storeOAuthState = (state: string): void => {
  if (process.env.NODE_ENV === "development") {
    console.log("Storing OAuth state");
  }
  localStorage.setItem("oauth_state", state);
  localStorage.setItem("oauth_state_timestamp", Date.now().toString());
};

const verifyOAuthState = (state: string | null): boolean => {
  if (!state) {
    if (process.env.NODE_ENV === "development") {
      console.warn("OAuth state verification failed: No state provided");
    }
    return false;
  }

  const storedState = localStorage.getItem("oauth_state");
  const timestamp = localStorage.getItem("oauth_state_timestamp");

  localStorage.removeItem("oauth_state");
  localStorage.removeItem("oauth_state_timestamp");

  if (!storedState) {
    if (process.env.NODE_ENV === "development") {
      console.warn("OAuth state verification failed: No stored state found");
    }
    return false;
  }

  if (!timestamp) {
    if (process.env.NODE_ENV === "development") {
      console.warn("OAuth state verification failed: No timestamp found");
    }
    return false;
  }

  if (storedState !== state) {
    if (process.env.NODE_ENV === "development") {
      console.warn("OAuth state verification failed: States don't match");
    }
    return false;
  }

  const stateTime = parseInt(timestamp, 10);
  const now = Date.now();
  const tenMinutes = 10 * 60 * 1000;

  if (now - stateTime >= tenMinutes) {
    if (process.env.NODE_ENV === "development") {
      console.warn("OAuth state verification failed: State expired");
    }
    return false;
  }

  return true;
};

export const getGoogleOAuthUrl = (): string => {
  const state = generateState();

  storeOAuthState(state);

  const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
  const redirectUri = `${window.location.origin}/auth/google/callback`;

  const params = new URLSearchParams({
    client_id: clientId || "",
    redirect_uri: redirectUri,
    response_type: "code",
    scope: "openid email profile",
    state,
    prompt: "select_account",
  });

  const url = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;

  if (process.env.NODE_ENV === "development") {
    console.log("Google OAuth URL generated");
  }

  return url;
};

export const loginWithGoogle = (returnUrl?: string): void => {
  localStorage.removeItem("oauth_state");
  localStorage.removeItem("oauth_state_timestamp");

  const url = getGoogleOAuthUrl();

  // Add returnUrl to the URL if provided
  if (returnUrl) {
    // Store returnUrl in localStorage to be used after callback
    localStorage.setItem("google_auth_return_url", returnUrl);
  }

  window.location.href = url;
};

export const exchangeGoogleCode = async (
  code: string,
  state?: string | null
): Promise<{ user: User; session: AuthSessionDto }> => {
  try {
    const stateValid = verifyOAuthState(state || null);

    if (!stateValid && process.env.NODE_ENV === "production") {
      throw new Error(
        "Security validation failed. Please try logging in again."
      );
    }

    const redirectUri =
      process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI ||
      `${window.location.origin}/auth/google/callback`;

    const headers = {
      "X-Tenant-ID": "131b3588-5090-473a-bf32-2b69bf3ce1b6",
      "Content-Type": "application/json",
    };

    const authResponse = await apiClient.post<AuthResponseDto>(
      "/v1/auth/google-login",
      {
        code,
        redirect_uri: redirectUri,
      },
      {
        loading: "Authenticating with Google...",
        success: "Google login successful!",
        error: (err: Error) => err.message || "Google login failed",
      },
      {
        headers,
        withCredentials: true,
      }
    );

    const tokenData = decodeToken(authResponse.accessToken);
    validateTokenPayload(tokenData, ["user_id", "exp"]);

    const user = authMapper.mapTokenToUser(tokenData as UserJwtPayload);
    const session = authMapper.mapTokenToSession(
      tokenData as UserJwtPayload,
      authResponse.accessToken,
      authResponse.refreshToken
    );

    return { user, session };
  } catch (error) {
    if (error instanceof Error) {
      if (
        error.message.includes("Failed to fetch") ||
        error.message.includes("NetworkError")
      ) {
        throw new Error(
          "Could not connect to the authentication server. Please check your internet connection and try again."
        );
      }

      if (error.message.includes("invalid_grant")) {
        throw new Error(
          "The authorization code has expired or has already been used. Please try logging in again."
        );
      }

      if (error.message.includes("invalid_client")) {
        throw new Error(
          "OAuth client configuration error. Please contact support."
        );
      }
    }

    throw error;
  }
};

export const requestMagicLink = async (email: string): Promise<void> => {
  try {
    const headers = {
      "X-Tenant-ID": "131b3588-5090-473a-bf32-2b69bf3ce1b6",
      "Content-Type": "application/json",
    };

    await apiClient.post<void>(
      "/v1/auth/magic-link",
      { email } as unknown,
      {
        loading: "Sending magic link...",
        success: "Magic link sent! Check your email.",
        error: (err: Error) => err.message || "Failed to send magic link",
      },
      { headers, withCredentials: true }
    );
  } catch (error) {
    console.error("Magic link request error:", error);
    throw error;
  }
};

export const verifyMagicLink = async (
  token: string
): Promise<{ user: User; session: AuthSessionDto }> => {
  try {
    const headers = {
      "X-Tenant-ID": "131b3588-5090-473a-bf32-2b69bf3ce1b6",
      "Content-Type": "application/json",
    };

    const authResponse = await apiClient.post<AuthResponseDto>(
      "/v1/auth/validate-magic-link",
      { token } as unknown,
      {
        loading: "Verifying magic link...",
        success: "Magic link verified successfully!",
        error: (err: Error) => err.message || "Invalid or expired magic link",
      },
      { headers, withCredentials: true }
    );

    const tokenData = decodeToken(authResponse.accessToken);
    validateTokenPayload(tokenData, ["user_id", "exp"]);

    const user = authMapper.mapTokenToUser(tokenData as UserJwtPayload);
    const session = authMapper.mapTokenToSession(
      tokenData as UserJwtPayload,
      authResponse.accessToken,
      authResponse.refreshToken
    );

    return { user, session };
  } catch (error) {
    console.error("Magic link verification error:", error);
    throw error;
  }
};

export const verifyTwoFactor = async (
  verification: TwoFactorVerificationDto
): Promise<{ user: User; session: AuthSessionDto }> => {
  try {
   const headers = {
      "X-Tenant-ID": "131b3588-5090-473a-bf32-2b69bf3ce1b6",
      "Content-Type": "application/json",
    };

    const authResponse = await apiClient.post<AuthResponseDto>(
      "/v1/auth/validate-two-factor",
      verification as unknown,
      {
        loading: "Verifying code...",
        success: "Two-factor authentication verified!",
        error: (err: Error) => err.message || "Invalid verification code",
      },
      { headers, withCredentials: true }
    );

    // Decode and validate token
    const tokenData = decodeToken(authResponse.accessToken);
    validateTokenPayload(tokenData, ["user_id", "exp"]);

    // Map token data to domain models
    const user = authMapper.mapTokenToUser(tokenData as UserJwtPayload);
    const session = authMapper.mapTokenToSession(
      tokenData as UserJwtPayload,
      authResponse.accessToken,
      authResponse.refreshToken
    );

    return { user, session };
  } catch (error) {
    console.error("Two-factor verification error:", error);
    throw error;
  }
};

// Request password reset
export const requestPasswordReset = async (email: string): Promise<void> => {
  try {
    await apiClient.post<void>(
      "/v1/auth/password-reset",
      { email } as unknown,
      {
        loading: "Requesting password reset...",
        success: "Password reset instructions sent to your email!",
        error: (err: Error) =>
          err.message || "Failed to request password reset",
      },
      { withCredentials: true }
    );
  } catch (error) {
    console.error("Password reset request error:", error);
    throw error;
  }
};

// Reset password
export const resetPassword = async (
  reset: PasswordResetCompletionDto
): Promise<void> => {
  try {
    await apiClient.post<void>(
      "/v1/auth/password-reset/complete",
      reset as unknown,
      {
        loading: "Resetting password...",
        success: "Password reset successfully!",
        error: (err: Error) => err.message || "Failed to reset password",
      },
      { withCredentials: true }
    );
  } catch (error) {
    console.error("Password reset error:", error);
    throw error;
  }
};

// Accept invitation
export const acceptInvitation = async (
  acceptance: InvitationAcceptanceDto
): Promise<{ user: User; session: AuthSessionDto }> => {
  try {
    // Make API request
    const authResponse = await apiClient.post<AuthResponseDto>(
      "/v1/auth/invitation/accept",
      acceptance as unknown,
      {
        loading: "Creating account...",
        success: "Account created successfully!",
        error: (err: Error) => err.message || "Failed to accept invitation",
      },
      { withCredentials: true }
    );

    // Decode and validate token
    const tokenData = decodeToken(authResponse.accessToken);
    validateTokenPayload(tokenData, ["user_id", "exp"]);

    // Map token data to domain models
    const user = authMapper.mapTokenToUser(tokenData as UserJwtPayload);
    const session = authMapper.mapTokenToSession(
      tokenData as UserJwtPayload,
      authResponse.accessToken,
      authResponse.refreshToken
    );

    return { user, session };
  } catch (error) {
    console.error("Invitation acceptance error:", error);
    throw error;
  }
};

// Refresh token
export const refreshToken = async (): Promise<{ user: User; session: AuthSessionDto }> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    // Get the refresh token from localStorage
    let refreshToken = "";
    if (typeof window !== "undefined") {
      const sessionData = localStorage.getItem("auth_session");
      if (sessionData) {
        const { session } = JSON.parse(sessionData);
        refreshToken = session.refreshToken;
      }
    }

    if (!refreshToken) {
      throw new Error("No refresh token available");
    }

    // Use direct fetch to avoid toast notifications
    const response = await fetch(new URL("/v1/auth/refresh-token", process.env.NEXT_PUBLIC_API_URL), {
      method: "POST",
      headers: headers,
      body: JSON.stringify({ refreshToken }),
      credentials: "include"
    });

    if (!response.ok) {
      throw new Error("Failed to refresh token");
    }

    const data = await response.json();
    const authResponse = data.success && data.data ? data.data : data;

    const tokenData = decodeToken(authResponse.accessToken);
    validateTokenPayload(tokenData, ["user_id", "exp"]);

    const user = authMapper.mapTokenToUser(tokenData as UserJwtPayload);
    const session = authMapper.mapTokenToSession(
      tokenData as UserJwtPayload,
      authResponse.accessToken,
      authResponse.refreshToken
    );

    return { user, session };
  } catch (error) {
    console.error("Token refresh error:", error);
    throw error;
  }
};

// Logout
export const logout = async (sessionId?: string): Promise<void> => {
  try {
    await apiClient.post<void>(
      "/v1/auth/logout",
      sessionId ? ({ sessionId } as unknown) : ({} as unknown),
      {
        loading: "Logging out...",
        success: "Logged out successfully!",
        error: (err: Error) => err.message || "Failed to logout",
      },
      { withCredentials: true }
    );
  } catch (error) {
    console.error("Logout error:", error);
    throw error;
  }
};
