import { toast } from "sonner";
import { Dashboard } from "@mug/models/dashboard";
import { 
  getAllDashboards, 
  getDashboardById, 
  getDashboardByFlowId, 
  getGeneralDashboard 
} from "@mug/constants/mock-dashboards";
import {
  ErrorResponse,
} from "@mug/services/request";

// Read all dashboards
export const readDashboards = async (): Promise<Dashboard[]> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/dashboards`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, return all dashboards
        const dashboards = getAllDashboards();
        
        resolve(dashboards);
      }),
      {
        loading: "Loading dashboards...",
        success: (): string => "Dashboards loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read a single dashboard by ID
export const readDashboard = async (id: string): Promise<Dashboard> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/dashboards/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the dashboard by ID
        const dashboard = getDashboardById(id);
        if (!dashboard) {
          throw new Error("Dashboard not found");
        }
        
        resolve(dashboard);
      }),
      {
        loading: "Loading dashboard...",
        success: (): string => "Dashboard loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read dashboard by flow ID
export const readDashboardByFlowId = async (flowId: string): Promise<Dashboard> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/dashboards/flow/${flowId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the dashboard by flow ID
        const dashboard = getDashboardByFlowId(flowId);
        if (!dashboard) {
          throw new Error("Dashboard not found for this flow");
        }
        
        resolve(dashboard);
      }),
      {
        loading: "Loading flow dashboard...",
        success: (): string => "Flow dashboard loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read general dashboard
export const readGeneralDashboard = async (): Promise<Dashboard> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/dashboards/general`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the general dashboard
        const dashboard = getGeneralDashboard();
        
        resolve(dashboard);
      }),
      {
        loading: "Loading general dashboard...",
        success: (): string => "General dashboard loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
