import { toast } from "sonner";
import { Plan, Subscription, SubscriptionUser, UserRole } from "@mug/models/plan";
import {
  mockPlans,
  getPlanById,
  getSubscription,
  mockSubscriptionUsers
} from "@mug/constants/mock-plans";
import {
  ErrorResponse,
} from "@mug/services/request";
import { v4 as uuidv4 } from "uuid";

// Read all plans
export const readPlans = async (): Promise<Plan[]> => {
  return new Promise((resolve, reject) => {
    try {
      // For development/mock implementation, return the mock plans directly
      if (process.env.NODE_ENV === 'development') {
        console.log("Using mock plans data");
        // Short timeout to simulate API call but avoid getting stuck
        setTimeout(() => {
          resolve(mockPlans);
        }, 500);
        return;
      }

      // For production, make the actual API call
      toast.promise(
        fetch(new URL(`/v1/plans`, process.env.NEXT_PUBLIC_API_URL), {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }).then(async (res: Response): Promise<void> => {
          if (!res.ok) {
            const errorData: ErrorResponse = await res.json();
            console.error(errorData);
            throw new Error(errorData.details || "Failed to load plans");
          }

          try {
            const data = await res.json();
            // Check if the response has the expected format
            const plans = data.success && data.data ? data.data : data;
            resolve(plans);
          } catch (parseError) {
            console.error("Error parsing plans data:", parseError);
            // Fallback to mock data if parsing fails
            resolve(mockPlans);
          }
        }).catch(error => {
          console.error("Error fetching plans:", error);
          // Fallback to mock data if fetch fails
          resolve(mockPlans);
        }),
        {
          loading: "Loading plans...",
          success: (): string => "Plans loaded successfully!",
          error: (err: Error): string => err.message,
        }
      );
    } catch (error) {
      console.error("Unexpected error in readPlans:", error);
      // Final fallback to ensure we always return something
      resolve(mockPlans);
    }
  });
};

// Read a single plan by ID
export const readPlan = async (id: string): Promise<Plan> => {
  return new Promise((resolve, reject) => {
    try {
      // For development/mock implementation, return the mock plan directly
      if (process.env.NODE_ENV === 'development') {
        console.log("Using mock plan data for ID:", id);
        const plan = getPlanById(id);
        if (!plan) {
          reject(new Error("Plan not found"));
          return;
        }

        // Short timeout to simulate API call but avoid getting stuck
        setTimeout(() => {
          resolve(plan);
        }, 500);
        return;
      }

      // For production, make the actual API call
      toast.promise(
        fetch(new URL(`/v1/plans/${id}`, process.env.NEXT_PUBLIC_API_URL), {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }).then(async (res: Response): Promise<void> => {
          if (!res.ok) {
            const errorData: ErrorResponse = await res.json();
            console.error(errorData);
            throw new Error(errorData.details || "Failed to load plan");
          }

          try {
            const data = await res.json();
            // Check if the response has the expected format
            const plan = data.success && data.data ? data.data : data;
            resolve(plan);
          } catch (parseError) {
            console.error("Error parsing plan data:", parseError);
            // Fallback to mock data if parsing fails
            const plan = getPlanById(id);
            if (!plan) {
              throw new Error("Plan not found");
            }
            resolve(plan);
          }
        }).catch(error => {
          console.error("Error fetching plan:", error);
          // Fallback to mock data if fetch fails
          const plan = getPlanById(id);
          if (!plan) {
            reject(new Error("Plan not found"));
            return;
          }
          resolve(plan);
        }),
        {
          loading: "Loading plan details...",
          success: (): string => "Plan details loaded successfully!",
          error: (err: Error): string => err.message,
        }
      );
    } catch (error) {
      console.error("Unexpected error in readPlan:", error);
      // Try to fallback to mock data
      try {
        const plan = getPlanById(id);
        if (!plan) {
          reject(new Error("Plan not found"));
          return;
        }
        resolve(plan);
      } catch (fallbackError) {
        reject(fallbackError);
      }
    }
  });
};

// Read current subscription
export const readCurrentSubscription = async (): Promise<Subscription> => {
  return new Promise((resolve, reject) => {
    try {
      // For development/mock implementation, return the mock subscription directly
      if (process.env.NODE_ENV === 'development') {
        console.log("Using mock subscription data");
        const subscription = getSubscription();
        // Short timeout to simulate API call but avoid getting stuck
        setTimeout(() => {
          resolve(subscription);
        }, 500);
        return;
      }

      // For production, make the actual API call
      toast.promise(
        fetch(new URL(`/v1/subscriptions/current`, process.env.NEXT_PUBLIC_API_URL), {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }).then(async (res: Response): Promise<void> => {
          if (!res.ok) {
            const errorData: ErrorResponse = await res.json();
            console.error(errorData);
            throw new Error(errorData.details || "Failed to load subscription");
          }

          try {
            const data = await res.json();
            // Check if the response has the expected format
            const subscription = data.success && data.data ? data.data : data;
            resolve(subscription);
          } catch (parseError) {
            console.error("Error parsing subscription data:", parseError);
            // Fallback to mock data if parsing fails
            const subscription = getSubscription();
            resolve(subscription);
          }
        }).catch(error => {
          console.error("Error fetching subscription:", error);
          // Fallback to mock data if fetch fails
          const subscription = getSubscription();
          resolve(subscription);
        }),
        {
          loading: "Loading subscription details...",
          success: (): string => "Subscription details loaded successfully!",
          error: (err: Error): string => err.message,
        }
      );
    } catch (error) {
      console.error("Unexpected error in readCurrentSubscription:", error);
      // Final fallback to ensure we always return something
      const subscription = getSubscription();
      resolve(subscription);
    }
  });
};

// Change plan
export const changePlan = async (planId: string): Promise<Subscription> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/subscriptions/change-plan`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ planId }),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }

        // For mock implementation, update the subscription with the new plan
        const plan = getPlanById(planId);
        if (!plan) {
          throw new Error("Plan not found");
        }

        const subscription = getSubscription();
        const updatedSubscription: Subscription = {
          ...subscription,
          planId,
          plan,
        };

        resolve(updatedSubscription);
      }),
      {
        loading: "Changing plan...",
        success: (): string => "Plan changed successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Get subscription users
export const getSubscriptionUsers = async (): Promise<SubscriptionUser[]> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/subscriptions/users`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }

        // For mock implementation, return the mock subscription users
        const users = mockSubscriptionUsers;

        resolve(users);
      }),
      {
        loading: "Loading subscription users...",
        success: (): string => "Subscription users loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Invite user to subscription
export interface InviteUserDTO {
  email: string;
  name: string;
  role: UserRole;
}

export const inviteUser = async (data: InviteUserDTO): Promise<SubscriptionUser> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/subscriptions/users/invite`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }

        // For mock implementation, create a new subscription user
        const newUser: SubscriptionUser = {
          id: `user-${uuidv4()}`,
          userId: `user-${uuidv4()}`,
          name: data.name,
          email: data.email,
          role: data.role,
          status: "invited",
          invitedAt: new Date().toISOString(),
        };

        resolve(newUser);
      }),
      {
        loading: "Inviting user...",
        success: (): string => "User invited successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Update user role
export const updateUserRole = async (userId: string, role: UserRole): Promise<SubscriptionUser> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/subscriptions/users/${userId}/role`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ role }),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }

        // For mock implementation, update the user role
        const user = mockSubscriptionUsers.find(user => user.userId === userId);
        if (!user) {
          throw new Error("User not found");
        }

        const updatedUser: SubscriptionUser = {
          ...user,
          role,
        };

        resolve(updatedUser);
      }),
      {
        loading: "Updating user role...",
        success: (): string => "User role updated successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Remove user from subscription
export const removeUser = async (userId: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/subscriptions/users/${userId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Removing user...",
        success: (): string => "User removed successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
