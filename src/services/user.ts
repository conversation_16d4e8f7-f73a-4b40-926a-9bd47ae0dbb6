import { User, LoginHistory, ConnectedDevice } from "@mug/models/user";
import apiClient from "@mug/services/api-client";
import {
  UpdateProfileDto,
  ChangePasswordDto,
  SetupTwoFactorDto,
  VerifyTwoFactorDto,
  ConnectGoogleDto,
  UserDto,
  EnableTwoFactorDto,
} from "@mug/services/dtos";

// Helper function to get the current user ID from localStorage
const getCurrentUserId = (): string => {
  if (typeof window === "undefined") return "";

  const sessionData = localStorage.getItem("auth_session");
  if (!sessionData) return "";

  try {
    const { user } = JSON.parse(sessionData);
    return user?.id || "";
  } catch (error) {
    console.error("Error parsing auth session:", error);
    return "";
  }
};

// Get current user profile
export const getUserProfile = async (): Promise<User> => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      throw new Error("User ID not found. Please log in again.");
    }

    const headers = {
      "Content-Type": "application/json",
    };

    const userData = await apiClient.get<UserDto>(
      `/v1/users/${userId}`,
      {
        loading: "Loading profile...",
        success: "Profile loaded successfully!",
        error: (err: Error) => err.message || "Failed to load profile",
      },
      { headers, withCredentials: true }
    );

    return userData as User;
  } catch (error) {
    console.error("Error loading profile:", error);
    throw error;
  }
};

// Update user profile
export const updateUserProfile = async (
  data: UpdateProfileDto
): Promise<User> => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      throw new Error("User ID not found. Please log in again.");
    }

    const headers = {
      "Content-Type": "application/json",
    };

    const updatedUser = await apiClient.put<UserDto>(
      `/v1/users/${userId}`,
      data,
      {
        loading: "Updating profile...",
        success: "Profile updated successfully!",
        error: (err: Error) => err.message || "Failed to update profile",
      },
      { headers, withCredentials: true }
    );

    return updatedUser as User;
  } catch (error) {
    console.error("Error updating profile:", error);
    throw error;
  }
};

// Change password
export const changePassword = async (
  data: ChangePasswordDto
): Promise<void> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    await apiClient.post<void>(
      "/v1/users/password",
      data,
      {
        loading: "Changing password...",
        success: "Password changed successfully!",
        error: (err: Error) => err.message || "Failed to change password",
      },
      { headers, withCredentials: true }
    );
  } catch (error) {
    console.error("Error changing password:", error);
    throw error;
  }
};

// Interface for the 2FA setup response
interface TwoFactorSetupResponse {
  setupKey?: string;
  qrCodeUrl?: string;
  secret?: string;
  qrCode?: string;
  [key: string]: any; // Allow any other properties
}

// Setup two-factor authentication
export const setup2FA = async (
  data: SetupTwoFactorDto
): Promise<{ setupKey: string; qrCodeUrl: string }> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    const setupResult = await apiClient.post<TwoFactorSetupResponse>(
      "/v1/users/setup-two-factor",
      data,
      {
        loading: "Setting up two-factor authentication...",
        success: "Two-factor authentication setup initiated!",
        error: (err: Error) =>
          err.message || "Failed to setup two-factor authentication",
      },
      { headers, withCredentials: true }
    );

    console.log("API response for setup2FA:", setupResult);

    // If the API returns different field names, map them here
    const result = {
      setupKey: setupResult.setupKey || setupResult.secret || "",
      qrCodeUrl: setupResult.qrCodeUrl || setupResult.qrCode || ""
    };

    console.log("Mapped API response:", result);

    return result;
  } catch (error) {
    console.error("Error setting up 2FA:", error);
    throw error;
  }
};

// Disable two-factor authentication
export const disable2FA = async (): Promise<void> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    const userId = getCurrentUserId();
    if (!userId) {
      throw new Error("User ID not found. Please log in again.");
    }

    // Use del method for DELETE
    await apiClient.del<void>(
      `/v1/users/${userId}/two-factor`,
      {
        loading: "Disabling two-factor authentication...",
        success: "Two-factor authentication disabled successfully!",
        error: (err: Error) =>
          err.message || "Failed to disable two-factor authentication",
      },
      { headers, withCredentials: true }
    );
  } catch (error) {
    console.error("Error disabling 2FA:", error);
    throw error;
  }
};

// Enable two-factor authentication
export const enable2FA = async (
  twoFactorKey: string,
  method: "app",
  code: string
): Promise<void> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    const userId = getCurrentUserId();
    if (!userId) {
      throw new Error("User ID not found. Please log in again.");
    }

    const data: EnableTwoFactorDto = {
      twoFactorKey,
      method,
      code,
    };

    // Use post method for POST
    await apiClient.post<void>(
      `/v1/users/${userId}/two-factor`,
      data,
      {
        loading: "Enabling two-factor authentication...",
        success: "Two-factor authentication enabled successfully!",
        error: (err: Error) =>
          err.message || "Failed to enable two-factor authentication",
      },
      { headers, withCredentials: true }
    );
  } catch (error) {
    console.error("Error enabling 2FA:", error);
    throw error;
  }
};

// Generate backup codes
export const generateBackupCodes = async (): Promise<string[]> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    const backupCodes = await apiClient.post<string[]>(
      "/v1/users/2fa/backup-codes",
      {},
      {
        loading: "Generating backup codes...",
        success: "Backup codes generated successfully!",
        error: (err: Error) => err.message || "Failed to generate backup codes",
      },
      { headers, withCredentials: true }
    );

    return backupCodes;
  } catch (error) {
    console.error("Error generating backup codes:", error);
    throw error;
  }
};

// Connect Google account
export const connectGoogle = async (data: ConnectGoogleDto): Promise<void> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    await apiClient.post<void>(
      "/v1/users/connect/google",
      data,
      {
        loading: "Connecting Google account...",
        success: "Google account connected successfully!",
        error: (err: Error) =>
          err.message || "Failed to connect Google account",
      },
      { headers, withCredentials: true }
    );
  } catch (error) {
    console.error("Error connecting Google account:", error);
    throw error;
  }
};

// Disconnect Google account
export const disconnectGoogle = async (): Promise<void> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    // Use request method for DELETE since apiClient doesn't have a delete method
    await apiClient.request<void>(
      "/v1/users/connect/google",
      {
        method: "DELETE",
        headers,
        withCredentials: true,
      },
      {
        loading: "Disconnecting Google account...",
        success: "Google account disconnected successfully!",
        error: (err: Error) =>
          err.message || "Failed to disconnect Google account",
      }
    );
  } catch (error) {
    console.error("Error disconnecting Google account:", error);
    throw error;
  }
};

// Get login history
export const getLoginHistory = async (): Promise<LoginHistory[]> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    const loginHistory = await apiClient.get<LoginHistory[]>(
      "/v1/users/login-history",
      {
        loading: "Loading login history...",
        success: "Login history loaded successfully!",
        error: (err: Error) => err.message || "Failed to load login history",
      },
      { headers, withCredentials: true }
    );

    return loginHistory;
  } catch (error) {
    console.error("Error loading login history:", error);
    throw error;
  }
};

// Get connected devices
export const getConnectedDevices = async (): Promise<ConnectedDevice[]> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    const connectedDevices = await apiClient.get<ConnectedDevice[]>(
      "/v1/users/devices",
      {
        loading: "Loading connected devices...",
        success: "Connected devices loaded successfully!",
        error: (err: Error) =>
          err.message || "Failed to load connected devices",
      },
      { headers, withCredentials: true }
    );

    return connectedDevices;
  } catch (error) {
    console.error("Error loading connected devices:", error);
    throw error;
  }
};

// Revoke device access
export const revokeDevice = async (deviceId: string): Promise<void> => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    // Use request method for DELETE since apiClient doesn't have a delete method
    await apiClient.request<void>(
      `/v1/users/devices/${deviceId}`,
      {
        method: "DELETE",
        headers,
        withCredentials: true,
      },
      {
        loading: "Revoking device access...",
        success: "Device access revoked successfully!",
        error: (err: Error) => err.message || "Failed to revoke device access",
      }
    );
  } catch (error) {
    console.error("Error revoking device access:", error);
    throw error;
  }
};

// Export user data
export const exportUserData = async (): Promise<Blob> => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      throw new Error("User ID not found. Please log in again.");
    }

    const headers = {
      "Content-Type": "application/json",
      Accept: "application/json",
    };

    // Use request method directly to get the blob response
    const response = await fetch(
      new URL(`/v1/users/${userId}/export`, process.env.NEXT_PUBLIC_API_URL),
      {
        credentials: "include",
        method: "GET",
        headers,
      }
    );

    if (!response.ok) {
      throw new Error("Failed to export user data");
    }

    // Get the blob from the response
    const blob = await response.blob();

    // Show success message using apiClient's toast handling
    const toastMessages = {
      loading: "Exporting user data...",
      success: "User data exported successfully!",
      error: "Failed to export user data",
    };

    // Use apiClient's toast handling
    if (typeof window !== "undefined") {
      // Import toast from sonner
      const { toast } = await import("sonner");
      toast.success(toastMessages.success);
    }

    return blob;
  } catch (error) {
    console.error("Error exporting user data:", error);

    // Show error toast
    if (typeof window !== "undefined") {
      // Import toast from sonner
      const { toast } = await import("sonner");
      toast.error("Failed to export user data");
    }

    throw error;
  }
};

// Request account deletion
export const requestAccountDeletion = async (): Promise<void> => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      throw new Error("User ID not found. Please log in again.");
    }

    const headers = {
      "Content-Type": "application/json",
    };

    await apiClient.post<void>(
      `/v1/users/${userId}/delete-account`,
      {},
      {
        loading: "Requesting account deletion...",
        success: "Account deletion requested successfully!",
        error: (err: Error) =>
          err.message || "Failed to request account deletion",
      },
      { headers, withCredentials: true }
    );
  } catch (error) {
    console.error("Error requesting account deletion:", error);
    throw error;
  }
};
