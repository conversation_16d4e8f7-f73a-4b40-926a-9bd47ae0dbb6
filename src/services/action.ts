import { toast } from "sonner";
import { Action, ActionType, ActionCategory } from "@mug/models/action";
import { 
  mockActions, 
  getActionById, 
  getActionsByCategory 
} from "@mug/constants/mock-actions";
import {
  SuccessResponseMany,
  SuccessResponse<PERSON>ingle,
  ErrorResponse,
} from "@mug/services/request";
import { Pagination } from "@mug/models/core";
import { v4 as uuidv4 } from "uuid";

// DTOs for Action service
export interface CreateActionDTO {
  name: string;
  description?: string;
  type: ActionType;
  category: ActionCategory;
  config?: Record<string, unknown>;
  status: boolean;
}

export interface UpdateActionDTO {
  name: string;
  description?: string;
  type: ActionType;
  category: ActionCategory;
  config?: Record<string, unknown>;
  status: boolean;
}

// Read all actions with pagination
export const readActions = async (
  page: number = 1,
  limit: number = 10,
  filters?: {
    search?: string;
    status?: boolean;
    category?: string;
    type?: string;
  }
): Promise<{
  data: Action[];
  pagination: Pagination;
}> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(
        new URL(
          `/v1/actions?page=${page}&limit=${limit}${
            filters?.status !== undefined ? `&status=${filters.status}` : ""
          }${filters?.search ? `&search=${filters.search}` : ""}${
            filters?.category ? `&category=${filters.category}` : ""
          }${filters?.type ? `&type=${filters.type}` : ""}`,
          process.env.NEXT_PUBLIC_API_URL
        ),
        {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      ).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, filter the data here
        let filteredData = [...mockActions];
        
        if (filters?.status !== undefined) {
          filteredData = filteredData.filter(action => action.status === filters.status);
        }
        
        if (filters?.category) {
          filteredData = filteredData.filter(action => action.category === filters.category);
        }
        
        if (filters?.type) {
          filteredData = filteredData.filter(action => action.type === filters.type);
        }
        
        if (filters?.search) {
          const searchLower = filters.search.toLowerCase();
          filteredData = filteredData.filter(action => 
            action.name.toLowerCase().includes(searchLower) || 
            action.description?.toLowerCase().includes(searchLower)
          );
        }
        
        // Calculate pagination
        const totalRecords = filteredData.length;
        const totalPages = Math.ceil(totalRecords / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedData = filteredData.slice(startIndex, endIndex);
        
        // Generate page numbers array
        const pages = Array.from({ length: totalPages }, (_, i) => i + 1);
        
        const pagination: Pagination = {
          page,
          pages,
          records_per_page: limit,
          total_pages: totalPages,
          total_records: totalRecords,
        };
        
        resolve({ 
          data: paginatedData, 
          pagination 
        });
      }),
      {
        loading: "Loading actions...",
        success: (): string => "Actions loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read a single action by ID
export const readAction = async (id: string): Promise<Action> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/actions/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the action by ID
        const action = getActionById(id);
        if (!action) {
          throw new Error("Action not found");
        }
        
        resolve(action);
      }),
      {
        loading: "Loading action details...",
        success: (): string => "Action details loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Create a new action
export const createAction = async (data: CreateActionDTO): Promise<Action> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL("/v1/actions", process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, create a new action with the provided data
        const newAction: Action = {
          id: `action-${uuidv4()}`,
          name: data.name,
          description: data.description,
          type: data.type,
          category: data.category,
          config: data.config,
          status: data.status,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        resolve(newAction);
      }),
      {
        loading: "Creating action...",
        success: (): string => "Action created successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Update an existing action
export const updateAction = async (id: string, data: UpdateActionDTO): Promise<Action> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/actions/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, update the action with the provided data
        const action = getActionById(id);
        if (!action) {
          throw new Error("Action not found");
        }
        
        // Update the action with the new data
        const updatedAction: Action = {
          ...action,
          name: data.name,
          description: data.description,
          type: data.type,
          category: data.category,
          config: data.config,
          status: data.status,
          updatedAt: new Date().toISOString(),
        };
        
        resolve(updatedAction);
      }),
      {
        loading: "Updating action...",
        success: (): string => "Action updated successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete an action
export const deleteAction = async (id: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/actions/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting action...",
        success: (): string => "Action deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete multiple actions
export const deleteActions = async (ids: string[]): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/actions/batch`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids }),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting actions...",
        success: (): string => "Actions deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
