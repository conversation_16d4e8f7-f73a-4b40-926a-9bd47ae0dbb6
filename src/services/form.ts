import { toast } from "sonner";
import { Form, FormTemplate } from "@mug/models/form";
import { 
  mockForms, 
  getFormById, 
  getFormTemplate 
} from "@mug/constants/mock-forms";
import {
  SuccessResponseMany,
  SuccessResponseSingle,
  ErrorResponse,
} from "@mug/services/request";
import { Pagination } from "@mug/models/core";
import { v4 as uuidv4 } from "uuid";

// DTOs for Form service
export interface CreateFormDTO {
  name: string;
  description?: string;
  slug: string;
  template?: FormTemplate;
  fields: any[];
  settings: any;
  conversionMode: string;
  status: boolean;
  laneId?: string;
}

export interface UpdateFormDTO {
  name: string;
  description?: string;
  slug: string;
  fields: any[];
  settings: any;
  conversionMode: string;
  status: boolean;
  laneId?: string;
}

// Read all forms with pagination
export const readForms = async (
  page: number = 1,
  limit: number = 10,
  filters?: {
    search?: string;
    status?: boolean;
  }
): Promise<{
  data: Form[];
  pagination: Pagination;
}> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(
        new URL(
          `/v1/forms?page=${page}&limit=${limit}${
            filters?.status !== undefined ? `&status=${filters.status}` : ""
          }${filters?.search ? `&search=${filters.search}` : ""}`,
          process.env.NEXT_PUBLIC_API_URL
        ),
        {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      ).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, filter the data here
        let filteredData = [...mockForms];
        
        if (filters?.status !== undefined) {
          filteredData = filteredData.filter(form => form.status === filters.status);
        }
        
        if (filters?.search) {
          const searchLower = filters.search.toLowerCase();
          filteredData = filteredData.filter(form => 
            form.name.toLowerCase().includes(searchLower) || 
            form.description?.toLowerCase().includes(searchLower)
          );
        }
        
        // Calculate pagination
        const totalRecords = filteredData.length;
        const totalPages = Math.ceil(totalRecords / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedData = filteredData.slice(startIndex, endIndex);
        
        // Generate page numbers array
        const pages = Array.from({ length: totalPages }, (_, i) => i + 1);
        
        const pagination: Pagination = {
          page,
          pages,
          records_per_page: limit,
          total_pages: totalPages,
          total_records: totalRecords,
        };
        
        resolve({ 
          data: paginatedData, 
          pagination 
        });
      }),
      {
        loading: "Loading forms...",
        success: (): string => "Forms loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read a single form by ID
export const readForm = async (id: string): Promise<Form> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/forms/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the form by ID
        const form = getFormById(id);
        if (!form) {
          throw new Error("Form not found");
        }
        
        resolve(form);
      }),
      {
        loading: "Loading form details...",
        success: (): string => "Form details loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Create a new form
export const createForm = async (data: CreateFormDTO): Promise<Form> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL("/v1/forms", process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, create a new form with the provided data
        const newForm: Form = {
          id: `form-${uuidv4()}`,
          name: data.name,
          description: data.description,
          slug: data.slug,
          template: data.template,
          fields: data.fields,
          settings: data.settings,
          conversionMode: data.conversionMode as any,
          status: data.status,
          laneId: data.laneId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          publishedAt: data.status ? new Date().toISOString() : undefined,
        };
        
        resolve(newForm);
      }),
      {
        loading: "Creating form...",
        success: (): string => "Form created successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Update an existing form
export const updateForm = async (id: string, data: UpdateFormDTO): Promise<Form> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/forms/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, update the form with the provided data
        const form = getFormById(id);
        if (!form) {
          throw new Error("Form not found");
        }
        
        // Check if status changed from false to true
        const wasPublished = !form.status && data.status;
        
        // Update the form with the new data
        const updatedForm: Form = {
          ...form,
          name: data.name,
          description: data.description,
          slug: data.slug,
          fields: data.fields,
          settings: data.settings,
          conversionMode: data.conversionMode as any,
          status: data.status,
          laneId: data.laneId,
          updatedAt: new Date().toISOString(),
          publishedAt: wasPublished ? new Date().toISOString() : form.publishedAt,
        };
        
        resolve(updatedForm);
      }),
      {
        loading: "Updating form...",
        success: (): string => "Form updated successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete a form
export const deleteForm = async (id: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/forms/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting form...",
        success: (): string => "Form deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete multiple forms
export const deleteForms = async (ids: string[]): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/forms/batch`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids }),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting forms...",
        success: (): string => "Forms deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Get form template
export const getFormTemplateData = async (template: FormTemplate): Promise<Partial<Form>> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/forms/templates/${template}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the template
        const templateData = getFormTemplate(template);
        
        resolve(templateData);
      }),
      {
        loading: "Loading template...",
        success: (): string => "Template loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
