import { toast } from "sonner";
import { LandingPage, LandingPageTemplate } from "@mug/models/landing-page";
import { 
  mockLandingPages, 
  getLandingPageById, 
  getLandingPageTemplate 
} from "@mug/constants/mock-landing-pages";
import {
  SuccessResponseMany,
  SuccessResponse<PERSON>ingle,
  ErrorResponse,
} from "@mug/services/request";
import { Pagination } from "@mug/models/core";
import { v4 as uuidv4 } from "uuid";

// DTOs for Landing Page service
export interface CreateLandingPageDTO {
  name: string;
  description?: string;
  slug: string;
  template?: LandingPageTemplate;
  blocks: any[];
  conversionMode: string;
  seo?: Record<string, unknown>;
  status: boolean;
  laneId?: string;
}

export interface UpdateLandingPageDTO {
  name: string;
  description?: string;
  slug: string;
  blocks: any[];
  conversionMode: string;
  seo?: Record<string, unknown>;
  status: boolean;
  laneId?: string;
}

// Read all landing pages with pagination
export const readLandingPages = async (
  page: number = 1,
  limit: number = 10,
  filters?: {
    search?: string;
    status?: boolean;
  }
): Promise<{
  data: LandingPage[];
  pagination: Pagination;
}> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(
        new URL(
          `/v1/landing-pages?page=${page}&limit=${limit}${
            filters?.status !== undefined ? `&status=${filters.status}` : ""
          }${filters?.search ? `&search=${filters.search}` : ""}`,
          process.env.NEXT_PUBLIC_API_URL
        ),
        {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      ).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, filter the data here
        let filteredData = [...mockLandingPages];
        
        if (filters?.status !== undefined) {
          filteredData = filteredData.filter(landingPage => landingPage.status === filters.status);
        }
        
        if (filters?.search) {
          const searchLower = filters.search.toLowerCase();
          filteredData = filteredData.filter(landingPage => 
            landingPage.name.toLowerCase().includes(searchLower) || 
            landingPage.description?.toLowerCase().includes(searchLower)
          );
        }
        
        // Calculate pagination
        const totalRecords = filteredData.length;
        const totalPages = Math.ceil(totalRecords / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedData = filteredData.slice(startIndex, endIndex);
        
        // Generate page numbers array
        const pages = Array.from({ length: totalPages }, (_, i) => i + 1);
        
        const pagination: Pagination = {
          page,
          pages,
          records_per_page: limit,
          total_pages: totalPages,
          total_records: totalRecords,
        };
        
        resolve({ 
          data: paginatedData, 
          pagination 
        });
      }),
      {
        loading: "Loading landing pages...",
        success: (): string => "Landing pages loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read a single landing page by ID
export const readLandingPage = async (id: string): Promise<LandingPage> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/landing-pages/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the landing page by ID
        const landingPage = getLandingPageById(id);
        if (!landingPage) {
          throw new Error("Landing page not found");
        }
        
        resolve(landingPage);
      }),
      {
        loading: "Loading landing page details...",
        success: (): string => "Landing page details loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Create a new landing page
export const createLandingPage = async (data: CreateLandingPageDTO): Promise<LandingPage> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL("/v1/landing-pages", process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, create a new landing page with the provided data
        const newLandingPage: LandingPage = {
          id: `landing-page-${uuidv4()}`,
          name: data.name,
          description: data.description,
          slug: data.slug,
          template: data.template,
          blocks: data.blocks,
          conversionMode: data.conversionMode as any,
          seo: data.seo as any,
          status: data.status,
          laneId: data.laneId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          publishedAt: data.status ? new Date().toISOString() : undefined,
        };
        
        resolve(newLandingPage);
      }),
      {
        loading: "Creating landing page...",
        success: (): string => "Landing page created successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Update an existing landing page
export const updateLandingPage = async (id: string, data: UpdateLandingPageDTO): Promise<LandingPage> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/landing-pages/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, update the landing page with the provided data
        const landingPage = getLandingPageById(id);
        if (!landingPage) {
          throw new Error("Landing page not found");
        }
        
        // Check if status changed from false to true
        const wasPublished = !landingPage.status && data.status;
        
        // Update the landing page with the new data
        const updatedLandingPage: LandingPage = {
          ...landingPage,
          name: data.name,
          description: data.description,
          slug: data.slug,
          blocks: data.blocks,
          conversionMode: data.conversionMode as any,
          seo: data.seo as any,
          status: data.status,
          laneId: data.laneId,
          updatedAt: new Date().toISOString(),
          publishedAt: wasPublished ? new Date().toISOString() : landingPage.publishedAt,
        };
        
        resolve(updatedLandingPage);
      }),
      {
        loading: "Updating landing page...",
        success: (): string => "Landing page updated successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete a landing page
export const deleteLandingPage = async (id: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/landing-pages/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting landing page...",
        success: (): string => "Landing page deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete multiple landing pages
export const deleteLandingPages = async (ids: string[]): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/landing-pages/batch`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids }),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting landing pages...",
        success: (): string => "Landing pages deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Get landing page template
export const getLandingPageTemplateData = async (template: LandingPageTemplate): Promise<Partial<LandingPage>> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/landing-pages/templates/${template}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the template
        const templateData = getLandingPageTemplate(template);
        
        resolve(templateData);
      }),
      {
        loading: "Loading template...",
        success: (): string => "Template loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
