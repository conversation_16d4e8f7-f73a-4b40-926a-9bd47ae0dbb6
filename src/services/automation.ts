import { toast } from "sonner";
import { Automation } from "@mug/models/automation";
import { 
  mockAutomations, 
  getAutomationById 
} from "@mug/constants/mock-automations";
import {
  SuccessResponseMany,
  SuccessResponseSingle,
  ErrorResponse,
} from "@mug/services/request";
import { Pagination } from "@mug/models/core";

// Read all automations with pagination
export const readAutomations = async (
  page: number = 1,
  limit: number = 10,
  filters?: {
    search?: string;
    status?: boolean;
  }
): Promise<{
  data: Automation[];
  pagination: Pagination;
}> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(
        new URL(
          `/v1/automations?page=${page}&limit=${limit}${
            filters?.status !== undefined ? `&status=${filters.status}` : ""
          }${filters?.search ? `&search=${filters.search}` : ""}`,
          process.env.NEXT_PUBLIC_API_URL
        ),
        {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      ).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, filter the data here
        let filteredData = [...mockAutomations];
        
        if (filters?.status !== undefined) {
          filteredData = filteredData.filter(automation => automation.status === filters.status);
        }
        
        if (filters?.search) {
          const searchLower = filters.search.toLowerCase();
          filteredData = filteredData.filter(automation => 
            automation.name.toLowerCase().includes(searchLower) || 
            automation.description?.toLowerCase().includes(searchLower)
          );
        }
        
        // Calculate pagination
        const totalRecords = filteredData.length;
        const totalPages = Math.ceil(totalRecords / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedData = filteredData.slice(startIndex, endIndex);
        
        // Generate page numbers array
        const pages = Array.from({ length: totalPages }, (_, i) => i + 1);
        
        const pagination: Pagination = {
          page,
          pages,
          records_per_page: limit,
          total_pages: totalPages,
          total_records: totalRecords,
        };
        
        resolve({ 
          data: paginatedData, 
          pagination 
        });
      }),
      {
        loading: "Loading automations...",
        success: (): string => "Automations loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read a single automation by ID
export const readAutomation = async (id: string): Promise<Automation> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/automations/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the automation by ID
        const automation = getAutomationById(id);
        if (!automation) {
          throw new Error("Automation not found");
        }
        
        resolve(automation);
      }),
      {
        loading: "Loading automation details...",
        success: (): string => "Automation details loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
