import { toast } from "sonner";
import { ErrorResponseDto } from "@mug/services/dtos";
import { refreshToken } from "@mug/services/auth";

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

interface RequestOptions {
  method: HttpMethod;
  body?: unknown;
  headers?: Record<string, string>;
  withCredentials?: boolean;
}

interface ToastMessages {
  loading: string;
  success: string;
  error: string | ((err: Error) => string);
}

// Base URL for API requests
const BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

// Variables for token refresh handling
let isRefreshing = false;
let failedQueue: { resolve: (value: unknown) => void; reject: (reason?: any) => void }[] = [];

// Process the queue of failed requests
const processQueue = (error: Error | null, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

/**
 * Handle error response from API
 */
async function handleErrorResponse(res: Response): Promise<never> {
  try {
    // Try to get the response text first
    const responseText = await res.text();
    console.error("API error response text:", responseText);

    // Try to parse as JSON
    let errorData: ErrorResponseDto;
    try {
      errorData = JSON.parse(responseText);
      console.error("API error response parsed:", errorData);
    } catch (jsonError) {
      console.error("Failed to parse error response as JSON:", jsonError);
      throw new Error(`Request failed: ${res.status} ${res.statusText}. Response: ${responseText}`);
    }

    if (errorData && errorData.success === false) {
      throw new Error(errorData.details || errorData.error || `Request failed with status ${res.status}`);
    } else if (errorData && errorData.error) {
      // Handle OAuth-style errors
      throw new Error(`${errorData.error}: ${errorData.error_description || 'Unknown error'}`);
    } else {
      throw new Error(`Request failed: ${res.status} ${res.statusText}`);
    }
  } catch (parseError) {
    console.error("Error handling error response:", parseError);
    throw new Error(`Request failed: ${res.status} ${res.statusText}`);
  }
}

/**
 * Handle success response from API
 */
async function handleSuccessResponse<T>(res: Response): Promise<T> {
  try {
    const data = await res.json();

    // Validate response format for API responses
    if (typeof data === 'object' && 'success' in data) {
      if (!data.success || !data.data) {
        console.error("Invalid API response format:", data);
        throw new Error("Invalid response format");
      }
      return data.data as T;
    }

    // For non-standard responses, return the whole response
    return data as T;
  } catch (parseError) {
    console.error("Error parsing success response:", parseError);
    throw new Error("Failed to process response");
  }
}

/**
 * Make a request to the API with toast notifications
 */
async function request<T>(endpoint: string, options: RequestOptions, toastMessages: ToastMessages): Promise<T> {
  return new Promise<T>((resolve, reject) => {
    try {
      // Prepare request
      console.log(`BASE_URL: ${BASE_URL}`);
      console.log(`Endpoint: ${endpoint}`);

      const url = new URL(endpoint, BASE_URL);
      console.log(`API Request: ${options.method} ${url.toString()}`);

      const fetchOptions: RequestInit = {
        method: options.method,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        credentials: options.withCredentials ? 'include' : 'same-origin'
      };

      console.log(`Request options:`, {
        method: options.method,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        credentials: options.withCredentials ? 'include' : 'same-origin'
      });

      if (options.body) {
        console.log(`Request body:`, typeof options.body === 'object' ?
          { ...options.body, password: options.body.hasOwnProperty('password') ? '***REDACTED***' : undefined } :
          options.body);
        fetchOptions.body = JSON.stringify(options.body);
      }

      // Create fetch promise
      const fetchPromise = fetch(url, fetchOptions)
        .then(async (res) => {
          console.log(`API Response: ${res.status} ${res.statusText} for ${options.method} ${url.toString()}`);

          // Handle 401 Unauthorized error (token expired)
          if (res.status === 401 && !url.pathname.includes('/auth/refresh-token')) {
            console.log('Token expired, attempting to refresh...');

            // Skip token refresh for 2FA setup endpoint to avoid redirect loops
            if (url.pathname.includes('/auth/setup-two-factor')) {
              console.log('Skipping token refresh for 2FA setup endpoint');
              return await handleErrorResponse(res);
            }

            if (isRefreshing) {
              // If already refreshing, add to queue
              return new Promise((resolve, reject) => {
                failedQueue.push({ resolve, reject });
              })
                .then(token => {
                  // Retry with new token
                  const newHeaders = { ...fetchOptions.headers, Authorization: `Bearer ${token}` };
                  return fetch(url, { ...fetchOptions, headers: newHeaders });
                })
                .then(async newRes => {
                  if (!newRes.ok) {
                    console.error(`Error response after token refresh: ${newRes.status} ${newRes.statusText}`);
                    await handleErrorResponse(newRes);
                  }
                  return handleSuccessResponse<T>(newRes);
                });
            }

            isRefreshing = true;

            try {
              // Try to refresh the token
              const { session } = await refreshToken();

              // Update the current request with the new token
              const newHeaders = { ...fetchOptions.headers, Authorization: `Bearer ${session.token}` };

              // Process all queued requests with the new token
              processQueue(null, session.token);

              // Retry the current request with the new token
              const newRes = await fetch(url, { ...fetchOptions, headers: newHeaders });

              if (!newRes.ok) {
                console.error(`Error response after token refresh: ${newRes.status} ${newRes.statusText}`);
                await handleErrorResponse(newRes);
              }

              const result = await handleSuccessResponse<T>(newRes);
              console.log(`Response data after token refresh:`, result);
              return result;
            } catch (refreshError) {
              console.error('Failed to refresh token:', refreshError);

              // Process all queued requests with the error
              processQueue(refreshError instanceof Error ? refreshError : new Error(String(refreshError)));

              // Redirect to login page if token refresh fails
              if (typeof window !== 'undefined') {
                window.location.href = '/auth/login';
              }

              throw refreshError;
            } finally {
              isRefreshing = false;
            }
          }

          if (!res.ok) {
            console.error(`Error response: ${res.status} ${res.statusText}`);
            await handleErrorResponse(res);
          }

          const result = await handleSuccessResponse<T>(res);
          console.log(`Response data:`, result);
          return result;
        });

      // Wrap with toast
      toast.promise(
        fetchPromise.then(result => {
          resolve(result);
          return result;
        }).catch((error: Error) => {
          reject(error);
          throw error;
        }),
        {
          loading: toastMessages.loading,
          success: toastMessages.success,
          error: typeof toastMessages.error === 'function'
            ? (err: Error) => (toastMessages.error as (err: Error) => string)(err)
            : toastMessages.error
        }
      );
    } catch (error) {
      console.error(`API request error (${endpoint}):`, error);

      // Provide more detailed error information
      if (error instanceof Error) {
        console.error("Error details:", {
          message: error.message,
          stack: error.stack
        });

        // Handle network errors
        if (error.message.includes("Failed to fetch") || error.message.includes("NetworkError")) {
          reject(new Error("Could not connect to the server. Please check your internet connection and try again."));
          return;
        }
      }

      reject(error instanceof Error ? error : new Error(String(error)));
    }
  });
}

// Convenience methods
async function get<T>(endpoint: string, toastMessages: ToastMessages, options: Omit<RequestOptions, 'method'> = {}): Promise<T> {
  return request<T>(endpoint, { ...options, method: 'GET' }, toastMessages);
}

async function post<T>(
  endpoint: string,
  body: unknown,
  toastMessages: ToastMessages,
  options: Omit<RequestOptions, 'method' | 'body'> = {}
): Promise<T> {
  return request<T>(endpoint, { ...options, body, method: 'POST' }, toastMessages);
}

async function put<T>(
  endpoint: string,
  body: unknown,
  toastMessages: ToastMessages,
  options: Omit<RequestOptions, 'method' | 'body'> = {}
): Promise<T> {
  return request<T>(endpoint, { ...options, body, method: 'PUT' }, toastMessages);
}

async function del<T>(
  endpoint: string,
  toastMessages: ToastMessages,
  options: Omit<RequestOptions, 'method'> = {}
): Promise<T> {
  return request<T>(endpoint, { ...options, method: 'DELETE' }, toastMessages);
}

// Default export with all API methods
const apiClient = {
  request,
  get,
  post,
  put,
  del
};

export default apiClient;
