import { 
  ChannelType, 
  ChannelStatus, 
  ConversationStatus, 
  PriorityLevel,
  MessageType,
  MessageStatus,
  CallDirection,
  CallStatus,
  OperatorStatus,
  Channel,
  Contact,
  Conversation,
  Message,
  Attachment,
  Call,
  MessageTemplate,
  Tag,
  OperatorSettings,
  InboxMetrics
} from "@mug/models/inbox";
import { 
  mockChannels, 
  mockContacts, 
  mockConversations, 
  mockMessages, 
  mockCalls, 
  mockMessageTemplates, 
  mockTags 
} from "@mug/constants/mock-inbox-data";
import {
  mockOperatorSettings,
  findChannelById,
  findChannelByType,
  findContactById,
  findContactByIdentifier,
  findConversationById,
  findConversationsByContactId,
  findConversationsByChannelId,
  findConversationsByAssignedUser,
  findMessagesByConversationId,
  findCallsByConversationId,
  findCallsByContactId,
  findTemplateById,
  findTemplatesByChannelType,
  findTagById,
  findOperatorSettingsByUserId,
  createConversation,
  createMessage,
  createCall,
  updateCallStatus,
  createInternalConversation,
  getMetricsForPeriod,
  getMetricsForOperator
} from "@mug/constants/mock-inbox-helpers";

// Simulate network delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Get all channels
const getChannels = async (): Promise<Channel[]> => {
  await delay(500);
  return [...mockChannels];
};

// Get channel by ID
const getChannelById = async (id: string): Promise<Channel> => {
  await delay(300);
  const channel = findChannelById(id);
  if (!channel) {
    throw new Error(`Channel with ID ${id} not found`);
  }
  return { ...channel };
};

// Create a new channel
const createChannel = async (channel: Omit<Channel, "id" | "createdAt" | "updatedAt">): Promise<Channel> => {
  await delay(800);
  const newChannel: Channel = {
    ...channel,
    id: `channel-${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  mockChannels.push(newChannel);
  return { ...newChannel };
};

// Update a channel
const updateChannel = async (id: string, updates: Partial<Channel>): Promise<Channel> => {
  await delay(800);
  const channel = findChannelById(id);
  if (!channel) {
    throw new Error(`Channel with ID ${id} not found`);
  }
  
  const updatedChannel = {
    ...channel,
    ...updates,
    id, // Ensure ID doesn't change
    updatedAt: new Date().toISOString()
  };
  
  const index = mockChannels.findIndex(c => c.id === id);
  mockChannels[index] = updatedChannel;
  
  return { ...updatedChannel };
};

// Get all contacts
const getContacts = async (): Promise<Contact[]> => {
  await delay(500);
  return [...mockContacts];
};

// Get contact by ID
const getContactById = async (id: string): Promise<Contact> => {
  await delay(300);
  const contact = findContactById(id);
  if (!contact) {
    throw new Error(`Contact with ID ${id} not found`);
  }
  return { ...contact };
};

// Create a new contact
const createContact = async (contact: Omit<Contact, "id" | "createdAt" | "updatedAt">): Promise<Contact> => {
  await delay(800);
  const newContact: Contact = {
    ...contact,
    id: `contact-${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  mockContacts.push(newContact);
  return { ...newContact };
};

// Update a contact
const updateContact = async (id: string, updates: Partial<Contact>): Promise<Contact> => {
  await delay(800);
  const contact = findContactById(id);
  if (!contact) {
    throw new Error(`Contact with ID ${id} not found`);
  }
  
  const updatedContact = {
    ...contact,
    ...updates,
    id, // Ensure ID doesn't change
    updatedAt: new Date().toISOString()
  };
  
  const index = mockContacts.findIndex(c => c.id === id);
  mockContacts[index] = updatedContact;
  
  return { ...updatedContact };
};

// Get all conversations
const getConversations = async (filters?: {
  status?: ConversationStatus[];
  channelType?: ChannelType[];
  assignedTo?: string;
  priority?: PriorityLevel[];
  search?: string;
}): Promise<Conversation[]> => {
  await delay(700);
  
  let filteredConversations = [...mockConversations];
  
  if (filters) {
    if (filters.status && filters.status.length > 0) {
      filteredConversations = filteredConversations.filter(conv => 
        filters.status?.includes(conv.status)
      );
    }
    
    if (filters.channelType && filters.channelType.length > 0) {
      filteredConversations = filteredConversations.filter(conv => 
        filters.channelType?.includes(conv.channelType)
      );
    }
    
    if (filters.assignedTo) {
      filteredConversations = filteredConversations.filter(conv => 
        conv.assignedTo === filters.assignedTo
      );
    }
    
    if (filters.priority && filters.priority.length > 0) {
      filteredConversations = filteredConversations.filter(conv => 
        filters.priority?.includes(conv.priority)
      );
    }
    
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredConversations = filteredConversations.filter(conv => {
        // Search in subject or last message
        const subjectMatch = conv.subject?.toLowerCase().includes(searchLower);
        const lastMessageMatch = conv.lastMessagePreview?.toLowerCase().includes(searchLower);
        
        // Get contact name
        const contact = findContactById(conv.contactId);
        const contactMatch = contact?.name.toLowerCase().includes(searchLower);
        
        return subjectMatch || lastMessageMatch || contactMatch;
      });
    }
  }
  
  return filteredConversations;
};

// Get conversation by ID
const getConversationById = async (id: string): Promise<Conversation> => {
  await delay(300);
  const conversation = findConversationById(id);
  if (!conversation) {
    throw new Error(`Conversation with ID ${id} not found`);
  }
  return { ...conversation };
};

// Create a new conversation
const startConversation = async (
  channelId: string,
  contactId: string,
  subject?: string,
  initialMessage?: string,
  assignedTo?: string
): Promise<{ conversation: Conversation; message?: Message }> => {
  await delay(1000);
  
  const conversation = createConversation(channelId, contactId, subject, assignedTo);
  
  let message: Message | undefined;
  
  if (initialMessage) {
    message = createMessage(
      conversation.id,
      initialMessage,
      contactId,
      "contact"
    );
  }
  
  return {
    conversation: { ...conversation },
    message: message ? { ...message } : undefined
  };
};

// Update a conversation
const updateConversation = async (id: string, updates: Partial<Conversation>): Promise<Conversation> => {
  await delay(800);
  const conversation = findConversationById(id);
  if (!conversation) {
    throw new Error(`Conversation with ID ${id} not found`);
  }
  
  const updatedConversation = {
    ...conversation,
    ...updates,
    id, // Ensure ID doesn't change
    updatedAt: new Date().toISOString()
  };
  
  const index = mockConversations.findIndex(c => c.id === id);
  mockConversations[index] = updatedConversation;
  
  return { ...updatedConversation };
};

// Get messages for a conversation
const getMessagesByConversationId = async (conversationId: string): Promise<Message[]> => {
  await delay(500);
  const messages = findMessagesByConversationId(conversationId);
  
  // Sort by creation date
  return [...messages].sort((a, b) => 
    new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  );
};

// Send a message
const sendMessage = async (
  conversationId: string,
  content: string,
  senderId: string,
  type: MessageType = MessageType.TEXT,
  attachments?: Attachment[]
): Promise<Message> => {
  await delay(800);
  
  const message = createMessage(
    conversationId,
    content,
    senderId,
    "user",
    type,
    attachments
  );
  
  // Mark conversation as open if it was new
  const conversation = findConversationById(conversationId);
  if (conversation && conversation.status === ConversationStatus.NEW) {
    updateConversation(conversationId, { status: ConversationStatus.OPEN });
  }
  
  return { ...message };
};

// Add a private note
const addNote = async (
  conversationId: string,
  content: string,
  userId: string
): Promise<Message> => {
  await delay(500);
  
  const message = createMessage(
    conversationId,
    content,
    userId,
    "user",
    MessageType.NOTE
  );
  
  // Set as private note
  message.isPrivate = true;
  
  return { ...message };
};

// Mark messages as read
const markMessagesAsRead = async (
  conversationId: string,
  userId: string
): Promise<void> => {
  await delay(300);
  
  const conversation = findConversationById(conversationId);
  if (!conversation) {
    throw new Error(`Conversation with ID ${conversationId} not found`);
  }
  
  // Update unread count
  conversation.unreadCount = 0;
  
  // Mark messages as read
  const messages = findMessagesByConversationId(conversationId);
  const now = new Date().toISOString();
  
  messages.forEach(message => {
    if (message.senderType === "contact" && !message.readAt) {
      message.readAt = now;
      message.status = MessageStatus.READ;
    }
  });
};

// Get calls for a conversation
const getCallsByConversationId = async (conversationId: string): Promise<Call[]> => {
  await delay(500);
  const calls = findCallsByConversationId(conversationId);
  
  // Sort by creation date (newest first)
  return [...calls].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

// Start a call
const startCall = async (
  contactId: string,
  direction: CallDirection,
  phoneNumber: string,
  assignedTo: string
): Promise<Call> => {
  await delay(1000);
  
  const call = createCall(contactId, direction, phoneNumber, assignedTo);
  
  return { ...call };
};

// Update call status
const updateCall = async (
  callId: string,
  status: CallStatus,
  duration?: number,
  notes?: string
): Promise<Call> => {
  await delay(800);
  
  const updatedCall = updateCallStatus(callId, status, duration, notes);
  
  return { ...updatedCall };
};

// Get message templates
const getMessageTemplates = async (channelType?: ChannelType): Promise<MessageTemplate[]> => {
  await delay(500);
  
  if (channelType) {
    return findTemplatesByChannelType(channelType);
  }
  
  return [...mockMessageTemplates];
};

// Create a message template
const createMessageTemplate = async (
  template: Omit<MessageTemplate, "id" | "createdAt" | "updatedAt">
): Promise<MessageTemplate> => {
  await delay(800);
  
  const newTemplate: MessageTemplate = {
    ...template,
    id: `template-${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  mockMessageTemplates.push(newTemplate);
  
  return { ...newTemplate };
};

// Get all tags
const getTags = async (): Promise<Tag[]> => {
  await delay(300);
  return [...mockTags];
};

// Create a tag
const createTag = async (
  name: string,
  color: string,
  createdBy: string
): Promise<Tag> => {
  await delay(500);
  
  const newTag: Tag = {
    id: `tag-${Date.now()}`,
    name,
    color,
    createdBy,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  mockTags.push(newTag);
  
  return { ...newTag };
};

// Get operator settings
const getOperatorSettings = async (userId: string): Promise<OperatorSettings> => {
  await delay(300);
  
  const settings = findOperatorSettingsByUserId(userId);
  
  if (!settings) {
    // Create default settings if not found
    const defaultSettings: OperatorSettings = {
      userId,
      status: OperatorStatus.AVAILABLE,
      notifications: {
        email: true,
        desktop: true,
        mobile: true
      },
      channels: mockChannels.map(channel => channel.id)
    };
    
    mockOperatorSettings.push(defaultSettings);
    return { ...defaultSettings };
  }
  
  return { ...settings };
};

// Update operator settings
const updateOperatorSettings = async (
  userId: string,
  updates: Partial<OperatorSettings>
): Promise<OperatorSettings> => {
  await delay(500);
  
  let settings = findOperatorSettingsByUserId(userId);
  
  if (!settings) {
    // Create default settings if not found
    settings = {
      userId,
      status: OperatorStatus.AVAILABLE,
      notifications: {
        email: true,
        desktop: true,
        mobile: true
      },
      channels: mockChannels.map(channel => channel.id)
    };
    
    mockOperatorSettings.push(settings);
  }
  
  const updatedSettings = {
    ...settings,
    ...updates,
    userId // Ensure userId doesn't change
  };
  
  const index = mockOperatorSettings.findIndex(s => s.userId === userId);
  mockOperatorSettings[index] = updatedSettings;
  
  return { ...updatedSettings };
};

// Create an internal conversation (group)
const createInternalGroup = async (
  subject: string,
  participants: string[],
  initialMessage?: string
): Promise<{ conversation: Conversation; message?: Message }> => {
  await delay(1000);
  
  const conversation = createInternalConversation(subject, participants);
  
  let message: Message | undefined;
  
  if (initialMessage && participants.length > 0) {
    message = createMessage(
      conversation.id,
      initialMessage,
      participants[0],
      "user"
    );
  }
  
  return {
    conversation: { ...conversation },
    message: message ? { ...message } : undefined
  };
};

// Get metrics
const getMetrics = async (
  startDate: string,
  endDate: string
): Promise<InboxMetrics> => {
  await delay(1000);
  
  return getMetricsForPeriod(startDate, endDate);
};

// Get operator metrics
const getOperatorMetrics = async (
  userId: string,
  startDate: string,
  endDate: string
): Promise<any> => {
  await delay(1000);
  
  return getMetricsForOperator(userId, startDate, endDate);
};

// Default export with all inbox methods
const inboxService = {
  getChannels,
  getChannelById,
  createChannel,
  updateChannel,
  getContacts,
  getContactById,
  createContact,
  updateContact,
  getConversations,
  getConversationById,
  startConversation,
  updateConversation,
  getMessagesByConversationId,
  sendMessage,
  addNote,
  markMessagesAsRead,
  getCallsByConversationId,
  startCall,
  updateCall,
  getMessageTemplates,
  createMessageTemplate,
  getTags,
  createTag,
  getOperatorSettings,
  updateOperatorSettings,
  createInternalGroup,
  getMetrics,
  getOperatorMetrics
};

export default inboxService;
