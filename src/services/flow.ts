import { toast } from "sonner";
import { Pagination } from "@mug/models/core";
import { Flow, FlowMode, FlowType, FlowTemplate, FlowVisibility } from "@mug/models/flow";
import {
  SuccessResponseMany,
  SuccessResponseSingle,
  ErrorResponse,
} from "@mug/services/request";

// DTOs for Flow service
export interface CreateFlowDTO {
  title: string;
  description?: string;
  type: FlowType;
  template?: FlowTemplate;
  mode?: FlowMode;
  visibility: FlowVisibility;
  members?: string[]; // User IDs
  tags?: string[];
}

export interface UpdateFlowDTO {
  title: string;
  description?: string;
  type: FlowType;
  visibility: FlowVisibility;
  members?: string[]; // User IDs
  tags?: string[];
}

export const createFlow = async (data: CreateFlowDTO): Promise<Flow> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL("/v1/flows/", process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        const { data }: SuccessResponseSingle<Flow> = await res.json();
        resolve(data);
      }),
      {
        loading: "Creating flow...",
        success: (): string => "Flow created successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

export const readFlows = async (): Promise<{
  data: Flow[];
  pagination: Pagination;
}> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL("/v1/flows/", process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        const { data, pagination }: SuccessResponseMany<Flow> =
          await res.json();
        resolve({ data, pagination });
      }),
      {
        loading: "Loading flows...",
        success: (): string => "Flows loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

export const readFlow = async (id: string): Promise<Flow> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/flows/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        const { data }: SuccessResponseSingle<Flow> = await res.json();
        resolve(data);
      }),
      {
        loading: "Loading flow...",
        success: (): string => "Flow loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

export const deleteFlow = async (id: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/flows/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting flow...",
        success: (): string => "Flow deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
