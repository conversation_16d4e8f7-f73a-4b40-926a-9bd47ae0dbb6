import { toast } from "sonner";
import { Person, PersonType, Person<PERSON><PERSON>act, PersonDocument, PersonAddress } from "@mug/models/person";
import { mockPeople, getPersonById } from "@mug/constants/mock-people";
import {
  SuccessResponseMany,
  SuccessResponse<PERSON>ing<PERSON>,
  ErrorResponse,
} from "@mug/services/request";
import { Pagination } from "@mug/models/core";

// DTOs for Person service
export interface CreatePersonDTO {
  name: string;
  type: PersonType;
  email?: string;
  phone?: string;
  documents?: Omit<PersonDocument, "id">[];
  contacts?: Omit<PersonContact, "id">[];
  addresses?: Omit<PersonAddress, "id">[];
  partners?: string[]; // IDs of partner persons (for companies)
  status?: boolean;
}

export interface UpdatePersonDTO {
  name: string;
  email?: string;
  phone?: string;
  documents?: Omit<PersonDocument, "id">[];
  contacts?: Omit<PersonContact, "id">[];
  addresses?: Omit<PersonAddress, "id">[];
  partners?: string[]; // IDs of partner persons (for companies)
  status?: boolean;
}

export interface AddContactDTO {
  type: PersonContact["type"];
  name: string;
  value: string;
}

export interface AddDocumentDTO {
  name: string;
  value: string;
}

export interface AddAddressDTO {
  name: string;
  street: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface AddPartnerDTO {
  partnerId: string;
}

// Read all people with pagination
export const readPeople = async (
  page: number = 1,
  limit: number = 10,
  filters?: {
    type?: PersonType;
    status?: boolean;
    search?: string;
  }
): Promise<{
  data: Person[];
  pagination: Pagination;
}> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(
        new URL(
          `/v1/people?page=${page}&limit=${limit}${
            filters?.type ? `&type=${filters.type}` : ""
          }${filters?.status !== undefined ? `&status=${filters.status}` : ""}${
            filters?.search ? `&search=${filters.search}` : ""
          }`,
          process.env.NEXT_PUBLIC_API_URL
        ),
        {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      ).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, filter the data here
        let filteredData = [...mockPeople];
        
        if (filters?.type) {
          filteredData = filteredData.filter(person => person.type === filters.type);
        }
        
        if (filters?.status !== undefined) {
          filteredData = filteredData.filter(person => person.status === filters.status);
        }
        
        if (filters?.search) {
          const searchLower = filters.search.toLowerCase();
          filteredData = filteredData.filter(person => 
            person.name.toLowerCase().includes(searchLower) || 
            person.email?.toLowerCase().includes(searchLower) ||
            person.phone?.toLowerCase().includes(searchLower)
          );
        }
        
        // Calculate pagination
        const totalRecords = filteredData.length;
        const totalPages = Math.ceil(totalRecords / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedData = filteredData.slice(startIndex, endIndex);
        
        // Generate page numbers array
        const pages = Array.from({ length: totalPages }, (_, i) => i + 1);
        
        const pagination: Pagination = {
          page,
          pages,
          records_per_page: limit,
          total_pages: totalPages,
          total_records: totalRecords,
        };
        
        resolve({ 
          data: paginatedData, 
          pagination 
        });
      }),
      {
        loading: "Loading people...",
        success: (): string => "People loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read a single person by ID
export const readPerson = async (id: string): Promise<Person> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/people/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the person by ID
        const person = getPersonById(id);
        if (!person) {
          throw new Error("Person not found");
        }
        
        resolve(person);
      }),
      {
        loading: "Loading person details...",
        success: (): string => "Person details loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Create a new person
export const createPerson = async (data: CreatePersonDTO): Promise<Person> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL("/v1/people", process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, create a new person with the provided data
        // In a real implementation, this would come from the API response
        const newPerson: Person = data.type === "person" 
          ? {
              id: `person-${mockPeople.length + 1}`,
              type: "person",
              name: data.name,
              email: data.email,
              phone: data.phone,
              documents: data.documents?.map(doc => ({ ...doc, id: `doc-${Math.random().toString(36).substr(2, 9)}` })),
              contacts: data.contacts?.map(contact => ({ ...contact, id: `contact-${Math.random().toString(36).substr(2, 9)}` })),
              addresses: data.addresses?.map(address => ({ ...address, id: `address-${Math.random().toString(36).substr(2, 9)}` })),
              status: data.status ?? true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }
          : {
              id: `company-${mockPeople.length + 1}`,
              type: "company",
              name: data.name,
              email: data.email,
              phone: data.phone,
              documents: data.documents?.map(doc => ({ ...doc, id: `doc-${Math.random().toString(36).substr(2, 9)}` })),
              contacts: data.contacts?.map(contact => ({ ...contact, id: `contact-${Math.random().toString(36).substr(2, 9)}` })),
              addresses: data.addresses?.map(address => ({ ...address, id: `address-${Math.random().toString(36).substr(2, 9)}` })),
              partners: data.partners ? data.partners.map(id => {
                const partner = getPersonById(id);
                return partner && partner.type === "person" ? partner : null;
              }).filter(Boolean) as Person[] : [],
              status: data.status ?? true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };
        
        resolve(newPerson);
      }),
      {
        loading: "Creating person...",
        success: (): string => "Person created successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Update an existing person
export const updatePerson = async (id: string, data: UpdatePersonDTO): Promise<Person> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/people/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, update the person with the provided data
        const person = getPersonById(id);
        if (!person) {
          throw new Error("Person not found");
        }
        
        // Update the person with the new data
        const updatedPerson: Person = {
          ...person,
          name: data.name,
          email: data.email ?? person.email,
          phone: data.phone ?? person.phone,
          documents: data.documents 
            ? data.documents.map(doc => ({ ...doc, id: `doc-${Math.random().toString(36).substr(2, 9)}` }))
            : person.documents,
          contacts: data.contacts
            ? data.contacts.map(contact => ({ ...contact, id: `contact-${Math.random().toString(36).substr(2, 9)}` }))
            : person.contacts,
          addresses: data.addresses
            ? data.addresses.map(address => ({ ...address, id: `address-${Math.random().toString(36).substr(2, 9)}` }))
            : person.addresses,
          status: data.status ?? person.status,
          updatedAt: new Date().toISOString(),
        };
        
        // If it's a company, update partners
        if (person.type === "company" && data.partners) {
          (updatedPerson as any).partners = data.partners.map(id => {
            const partner = getPersonById(id);
            return partner && partner.type === "person" ? partner : null;
          }).filter(Boolean) as Person[];
        }
        
        resolve(updatedPerson);
      }),
      {
        loading: "Updating person...",
        success: (): string => "Person updated successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete a person
export const deletePerson = async (id: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/people/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting person...",
        success: (): string => "Person deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete multiple people
export const deletePeople = async (ids: string[]): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/people/batch`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids }),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting people...",
        success: (): string => "People deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Add a contact to a person
export const addContact = async (personId: string, data: AddContactDTO): Promise<PersonContact> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/people/${personId}/contacts`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, create a new contact
        const newContact: PersonContact = {
          id: `contact-${Math.random().toString(36).substr(2, 9)}`,
          ...data,
        };
        
        resolve(newContact);
      }),
      {
        loading: "Adding contact...",
        success: (): string => "Contact added successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Add a document to a person
export const addDocument = async (personId: string, data: AddDocumentDTO): Promise<PersonDocument> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/people/${personId}/documents`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, create a new document
        const newDocument: PersonDocument = {
          id: `doc-${Math.random().toString(36).substr(2, 9)}`,
          ...data,
        };
        
        resolve(newDocument);
      }),
      {
        loading: "Adding document...",
        success: (): string => "Document added successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Add an address to a person
export const addAddress = async (personId: string, data: AddAddressDTO): Promise<PersonAddress> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/people/${personId}/addresses`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, create a new address
        const newAddress: PersonAddress = {
          id: `address-${Math.random().toString(36).substr(2, 9)}`,
          ...data,
        };
        
        resolve(newAddress);
      }),
      {
        loading: "Adding address...",
        success: (): string => "Address added successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Add a partner to a company
export const addPartner = async (companyId: string, data: AddPartnerDTO): Promise<Person> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/people/${companyId}/partners`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, find the partner
        const partner = getPersonById(data.partnerId);
        if (!partner || partner.type !== "person") {
          throw new Error("Partner not found or not a person");
        }
        
        resolve(partner);
      }),
      {
        loading: "Adding partner...",
        success: (): string => "Partner added successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Remove a contact from a person
export const removeContact = async (personId: string, contactId: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/people/${personId}/contacts/${contactId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Removing contact...",
        success: (): string => "Contact removed successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Remove a document from a person
export const removeDocument = async (personId: string, documentId: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/people/${personId}/documents/${documentId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Removing document...",
        success: (): string => "Document removed successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Remove an address from a person
export const removeAddress = async (personId: string, addressId: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/people/${personId}/addresses/${addressId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Removing address...",
        success: (): string => "Address removed successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Remove a partner from a company
export const removePartner = async (companyId: string, partnerId: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/people/${companyId}/partners/${partnerId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Removing partner...",
        success: (): string => "Partner removed successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
