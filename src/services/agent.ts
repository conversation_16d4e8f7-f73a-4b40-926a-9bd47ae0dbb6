import { toast } from "sonner";
import { Agent, AgentModelType, AgentTemplate } from "@mug/models/agent";
import { 
  mockAgents, 
  getAgentById, 
  getAgentTemplate, 
  getAllActions 
} from "@mug/constants/mock-agents";
import {
  SuccessResponseMany,
  SuccessResponseSingle,
  ErrorResponse,
} from "@mug/services/request";
import { Pagination } from "@mug/models/core";
import { v4 as uuidv4 } from "uuid";

// DTOs for Agent service
export interface CreateAgentDTO {
  name: string;
  description?: string;
  actions: string[];
  rules: string[];
  model: AgentModelType;
  status?: boolean;
}

export interface UpdateAgentDTO {
  name: string;
  description?: string;
  actions: string[];
  rules: string[];
  model: AgentModelType;
  status?: boolean;
}

// Read all agents with pagination
export const readAgents = async (
  page: number = 1,
  limit: number = 10,
  filters?: {
    search?: string;
    status?: boolean;
  }
): Promise<{
  data: Agent[];
  pagination: Pagination;
}> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(
        new URL(
          `/v1/agents?page=${page}&limit=${limit}${
            filters?.status !== undefined ? `&status=${filters.status}` : ""
          }${filters?.search ? `&search=${filters.search}` : ""}`,
          process.env.NEXT_PUBLIC_API_URL
        ),
        {
          credentials: "include",
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      ).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, filter the data here
        let filteredData = [...mockAgents];
        
        if (filters?.status !== undefined) {
          filteredData = filteredData.filter(agent => agent.status === filters.status);
        }
        
        if (filters?.search) {
          const searchLower = filters.search.toLowerCase();
          filteredData = filteredData.filter(agent => 
            agent.name.toLowerCase().includes(searchLower) || 
            agent.description?.toLowerCase().includes(searchLower)
          );
        }
        
        // Calculate pagination
        const totalRecords = filteredData.length;
        const totalPages = Math.ceil(totalRecords / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedData = filteredData.slice(startIndex, endIndex);
        
        // Generate page numbers array
        const pages = Array.from({ length: totalPages }, (_, i) => i + 1);
        
        const pagination: Pagination = {
          page,
          pages,
          records_per_page: limit,
          total_pages: totalPages,
          total_records: totalRecords,
        };
        
        resolve({ 
          data: paginatedData, 
          pagination 
        });
      }),
      {
        loading: "Loading agents...",
        success: (): string => "Agents loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read a single agent by ID
export const readAgent = async (id: string): Promise<Agent> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/agents/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the agent by ID
        const agent = getAgentById(id);
        if (!agent) {
          throw new Error("Agent not found");
        }
        
        resolve(agent);
      }),
      {
        loading: "Loading agent details...",
        success: (): string => "Agent details loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Create a new agent
export const createAgent = async (data: CreateAgentDTO): Promise<Agent> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL("/v1/agents", process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, create a new agent with the provided data
        const newAgent: Agent = {
          id: `agent-${uuidv4()}`,
          name: data.name,
          description: data.description,
          actions: data.actions,
          rules: data.rules,
          model: data.model,
          status: data.status ?? true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        resolve(newAgent);
      }),
      {
        loading: "Creating agent...",
        success: (): string => "Agent created successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Update an existing agent
export const updateAgent = async (id: string, data: UpdateAgentDTO): Promise<Agent> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/agents/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, update the agent with the provided data
        const agent = getAgentById(id);
        if (!agent) {
          throw new Error("Agent not found");
        }
        
        // Update the agent with the new data
        const updatedAgent: Agent = {
          ...agent,
          name: data.name,
          description: data.description,
          actions: data.actions,
          rules: data.rules,
          model: data.model,
          status: data.status ?? agent.status,
          updatedAt: new Date().toISOString(),
        };
        
        resolve(updatedAgent);
      }),
      {
        loading: "Updating agent...",
        success: (): string => "Agent updated successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete an agent
export const deleteAgent = async (id: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/agents/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting agent...",
        success: (): string => "Agent deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete multiple agents
export const deleteAgents = async (ids: string[]): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/agents/batch`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids }),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting agents...",
        success: (): string => "Agents deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Get agent template
export const getTemplate = async (template: AgentTemplate): Promise<Partial<Agent>> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/agents/templates/${template}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the template
        const templateData = getAgentTemplate(template);
        
        resolve(templateData);
      }),
      {
        loading: "Loading template...",
        success: (): string => "Template loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Get all available actions
export const getAvailableActions = async (): Promise<any[]> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/actions`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get all actions
        const actions = getAllActions();
        
        resolve(actions);
      }),
      {
        loading: "Loading available actions...",
        success: (): string => "Actions loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
