import { toast } from "sonner";
import { CalendarEvent, EventFormValues } from "@mug/models/calendar";
import { 
  getAllEvents, 
  getEventById, 
  getEventsByDateRange,
  getEventsByFlowId,
  getEventsByRecordId
} from "@mug/constants/mock-calendar";
import {
  ErrorResponse,
} from "@mug/services/request";
import { v4 as uuidv4 } from "uuid";

// Read all events
export const readEvents = async (): Promise<CalendarEvent[]> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/calendar/events`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, return all events
        const events = getAllEvents();
        
        resolve(events);
      }),
      {
        loading: "Loading events...",
        success: (): string => "Events loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read events by date range
export const readEventsByDateRange = async (start: Date, end: Date): Promise<CalendarEvent[]> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/calendar/events?start=${start.toISOString()}&end=${end.toISOString()}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, filter events by date range
        const events = getEventsByDateRange(start, end);
        
        resolve(events);
      }),
      {
        loading: "Loading events...",
        success: (): string => "Events loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read a single event by ID
export const readEvent = async (id: string): Promise<CalendarEvent> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/calendar/events/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, get the event by ID
        const event = getEventById(id);
        if (!event) {
          throw new Error("Event not found");
        }
        
        resolve(event);
      }),
      {
        loading: "Loading event details...",
        success: (): string => "Event details loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read events by flow ID
export const readEventsByFlowId = async (flowId: string): Promise<CalendarEvent[]> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/calendar/events/flow/${flowId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, filter events by flow ID
        const events = getEventsByFlowId(flowId);
        
        resolve(events);
      }),
      {
        loading: "Loading flow events...",
        success: (): string => "Flow events loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Read events by record ID
export const readEventsByRecordId = async (recordId: string): Promise<CalendarEvent[]> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/calendar/events/record/${recordId}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, filter events by record ID
        const events = getEventsByRecordId(recordId);
        
        resolve(events);
      }),
      {
        loading: "Loading record events...",
        success: (): string => "Record events loaded successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Create a new event
export const createEvent = async (data: EventFormValues): Promise<CalendarEvent> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/calendar/events`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, create a new event
        const newEvent: CalendarEvent = {
          id: `event-${uuidv4()}`,
          title: data.title,
          description: data.description,
          start: data.start.toISOString(),
          end: data.end.toISOString(),
          allDay: data.allDay,
          location: data.location,
          url: data.url,
          type: data.type,
          status: data.status,
          priority: data.priority,
          attendees: data.attendees,
          flowId: data.flowId,
          recordId: data.recordId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        resolve(newEvent);
      }),
      {
        loading: "Creating event...",
        success: (): string => "Event created successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Update an existing event
export const updateEvent = async (id: string, data: EventFormValues): Promise<CalendarEvent> => {
  return new Promise((resolve) =>
    toast.promise(
      fetch(new URL(`/v1/calendar/events/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
        
        // For mock implementation, update the event
        const event = getEventById(id);
        if (!event) {
          throw new Error("Event not found");
        }
        
        const updatedEvent: CalendarEvent = {
          ...event,
          title: data.title,
          description: data.description,
          start: data.start.toISOString(),
          end: data.end.toISOString(),
          allDay: data.allDay,
          location: data.location,
          url: data.url,
          type: data.type,
          status: data.status,
          priority: data.priority,
          attendees: data.attendees,
          flowId: data.flowId,
          recordId: data.recordId,
          updatedAt: new Date().toISOString(),
        };
        
        resolve(updatedEvent);
      }),
      {
        loading: "Updating event...",
        success: (): string => "Event updated successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};

// Delete an event
export const deleteEvent = async (id: string): Promise<void> => {
  return new Promise(() =>
    toast.promise(
      fetch(new URL(`/v1/calendar/events/${id}`, process.env.NEXT_PUBLIC_API_URL), {
        credentials: "include",
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }).then(async (res: Response): Promise<void> => {
        if (!res.ok) {
          const errorData: ErrorResponse = await res.json();
          console.error(errorData);
          throw new Error(errorData.details);
        }
      }),
      {
        loading: "Deleting event...",
        success: (): string => "Event deleted successfully!",
        error: (err: Error): string => err.message,
      }
    )
  );
};
