"use client";

import { ReactNode, useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@mug/components/ui/dialog";

interface WindowModalProps {
  children: ReactNode;
  header?: {
    title: string;
    description?: string;
  };
  footer?: ReactNode;
  visible: boolean;
  onClose: () => void;
}
export function WindowModal(props: WindowModalProps) {
  const {
    children,
    footer,
    header,
    visible,
    onClose,
  } = props;
  const [open, setOpen] = useState<boolean>(visible);

  useEffect(() => {
    setOpen(visible);
  }, [visible]);

  return (
    <Dialog
      open={open}
      onOpenChange={(val) => {
        setOpen(val);
        if (!val) onClose();
      }}
    >
      <DialogContent className="sm:max-w-[425px] select-none">
        {!!header && (
          <DialogHeader>
            <DialogTitle className="uppercase">{header.title}</DialogTitle>
            {!!header.description && (
              <DialogDescription>{header.description}</DialogDescription>
            )}
          </DialogHeader>
        )}
        {children}
        {!!footer && <DialogFooter>{footer}</DialogFooter>}
      </DialogContent>
    </Dialog>
  );
}
