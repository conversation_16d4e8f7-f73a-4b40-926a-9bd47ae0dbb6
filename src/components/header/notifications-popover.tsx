"use client";

import { useState } from "react";
import { Bell, Check, Clock, Info, MessageSquare, User } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useTranslate } from "@mug/contexts/translate";

import { Badge } from "@mug/components/ui/badge";
import { Button } from "@mug/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";
import { Separator } from "@mug/components/ui/separator";
import { classNameBuilder as cn } from "@mug/lib/utils/class-name-builder";

// Mock data for notifications
const mockNotifications = [
  {
    id: "notif-1",
    title: "Nova mensagem",
    description: "Ana Silva enviou uma mensagem",
    time: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
    read: false,
    type: "message",
  },
  {
    id: "notif-2",
    title: "<PERSON>mbre<PERSON>",
    description: "Reunião de equipe em 30 minutos",
    time: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    read: false,
    type: "reminder",
  },
  {
    id: "notif-3",
    title: "Atualização de fluxo",
    description: "Carlos atualizou o fluxo de vendas",
    time: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    read: false,
    type: "update",
  },
  {
    id: "notif-4",
    title: "Novo usuário",
    description: "Mariana Costa foi adicionada ao time",
    time: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    read: true,
    type: "user",
  },
  {
    id: "notif-5",
    title: "Sistema atualizado",
    description: "Nova versão do sistema disponível",
    time: new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 days ago
    read: true,
    type: "system",
  },
];

export function NotificationsPopover() {
  const [notifications, setNotifications] = useState(mockNotifications);
  const [open, setOpen] = useState(false);
  const { t } = useTranslate();

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAsRead = (id: string) => {
    setNotifications(notifications.map(n =>
      n.id === id ? { ...n, read: true } : n
    ));
  };

  const markAllAsRead = () => {
    setNotifications(notifications.map(n => ({ ...n, read: true })));
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "message":
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case "reminder":
        return <Clock className="h-4 w-4 text-amber-500" />;
      case "update":
        return <Info className="h-4 w-4 text-green-500" />;
      case "user":
        return <User className="h-4 w-4 text-purple-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTime = (date: Date) => {
    return formatDistanceToNow(date, {
      addSuffix: true,
      locale: ptBR
    });
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative h-9 w-9">
          <Bell className="h-[18px] w-[18px]" />
          {unreadCount > 0 && (
            <Badge
              variant="default"
              className="absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center p-0 text-[10px]"
            >
              {unreadCount}
            </Badge>
          )}
          <span className="sr-only">{t("header.notifications")}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4">
          <h3 className="font-medium">{t("header.notifications")}</h3>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-auto p-0 text-xs text-muted-foreground"
              onClick={markAllAsRead}
            >
              <Check className="mr-1 h-3 w-3" />
              {t("header.markAllAsRead")}
            </Button>
          )}
        </div>
        <Separator />
        <div className="max-h-[calc(80vh-8rem)] overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-4 text-center">
              <Bell className="h-10 w-10 text-muted-foreground mb-2 opacity-50" />
              <p className="text-sm text-muted-foreground">
                {t("header.noNotifications")}
              </p>
            </div>
          ) : (
            <div>
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={cn(
                    "flex items-start gap-3 p-4 hover:bg-muted/50 transition-colors cursor-pointer",
                    !notification.read && "bg-muted/30"
                  )}
                  onClick={() => markAsRead(notification.id)}
                >
                  <div className="mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className={cn(
                        "text-sm font-medium",
                        !notification.read && "font-semibold"
                      )}>
                        {notification.title}
                      </p>
                      <span className="text-xs text-muted-foreground">
                        {formatTime(notification.time)}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {notification.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        <Separator />
        <div className="p-2">
          <Button variant="outline" size="sm" className="w-full">
            {t("header.viewAllNotifications")}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
