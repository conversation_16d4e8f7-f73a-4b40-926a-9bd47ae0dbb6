"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Calendar,
  FileText,
  Inbox,
  Layers,
  Search,
  User,
  Users
} from "lucide-react";
import { useTranslate } from "@mug/contexts/translate";
import { classNameBuilder as cn } from "@mug/lib/utils/class-name-builder";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";

// Mock data for search results
const mockSearchResults = {
  flows: [
    { id: "flow-1", name: "Fluxo de Vendas" },
    { id: "flow-2", name: "Fluxo de Marketing" },
    { id: "flow-3", name: "Fluxo de Onboarding" },
  ],
  people: [
    { id: "person-1", name: "<PERSON>" },
    { id: "person-2", name: "<PERSON>" },
    { id: "person-3", name: "<PERSON>" },
  ],
  documents: [
    { id: "doc-1", name: "Relatório Q1 2023" },
    { id: "doc-2", name: "Proposta Cliente XYZ" },
  ],
  messages: [
    { id: "msg-1", name: "Reunião de equipe", from: "Ana Silva" },
    { id: "msg-2", name: "Atualização do projeto", from: "Carlos Oliveira" },
  ],
  events: [
    { id: "event-1", name: "Reunião de planejamento", date: "Amanhã, 10:00" },
    { id: "event-2", name: "Apresentação para cliente", date: "Sexta, 14:00" },
  ],
};

export function GlobalSearch() {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const router = useRouter();
  const { t } = useTranslate();
  const searchRef = useRef<HTMLDivElement>(null);

  // Filter results based on query
  const getFilteredResults = () => {
    if (!query) return mockSearchResults;

    const q = query.toLowerCase();
    return {
      flows: mockSearchResults.flows.filter(item =>
        item.name.toLowerCase().includes(q)
      ),
      people: mockSearchResults.people.filter(item =>
        item.name.toLowerCase().includes(q)
      ),
      documents: mockSearchResults.documents.filter(item =>
        item.name.toLowerCase().includes(q)
      ),
      messages: mockSearchResults.messages.filter(item =>
        item.name.toLowerCase().includes(q) || item.from.toLowerCase().includes(q)
      ),
      events: mockSearchResults.events.filter(item =>
        item.name.toLowerCase().includes(q) || item.date.toLowerCase().includes(q)
      ),
    };
  };

  const filteredResults = getFilteredResults();

  const handleSelect = (path: string) => {
    setShowSuggestions(false);
    router.push(path);
  };

  // Efeito para fechar as sugestões ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Efeito para mostrar sugestões quando o usuário digita
  useEffect(() => {
    if (query.length > 0) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  }, [query]);

  return (
    <div className="relative w-full" ref={searchRef}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <input
          type="text"
          placeholder={t("header.search")}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className={cn(
            "h-10 w-full rounded-xl border border-input bg-background pl-10 pr-4 py-2",
            "focus:outline-none focus:ring-2 focus:ring-ring focus:border-input"
          )}
        />
      </div>

      {showSuggestions && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 rounded-md border border-border bg-background shadow-md overflow-hidden">
          {query.length > 0 && Object.values(filteredResults).every(arr => arr.length === 0) ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              {t("common.noResults")}
            </div>
          ) : (
            <div className="max-h-[80vh] overflow-y-auto">
              {filteredResults.flows.length > 0 && (
                <div className="px-2 py-1.5">
                  <div className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                    {t("search.flows")}
                  </div>
                  {filteredResults.flows.map((item) => (
                    <button
                      key={item.id}
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      onClick={() => handleSelect(`/flows/${item.id}`)}
                    >
                      <Layers className="mr-2 h-4 w-4" />
                      {item.name}
                    </button>
                  ))}
                </div>
              )}

              {filteredResults.people.length > 0 && (
                <div className="px-2 py-1.5">
                  <div className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                    {t("search.people")}
                  </div>
                  {filteredResults.people.map((item) => (
                    <button
                      key={item.id}
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      onClick={() => handleSelect(`/people/${item.id}`)}
                    >
                      <User className="mr-2 h-4 w-4" />
                      {item.name}
                    </button>
                  ))}
                </div>
              )}

              {filteredResults.documents.length > 0 && (
                <div className="px-2 py-1.5">
                  <div className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                    {t("search.documents")}
                  </div>
                  {filteredResults.documents.map((item) => (
                    <button
                      key={item.id}
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      onClick={() => handleSelect(`/documents/${item.id}`)}
                    >
                      <FileText className="mr-2 h-4 w-4" />
                      {item.name}
                    </button>
                  ))}
                </div>
              )}

              {filteredResults.messages.length > 0 && (
                <div className="px-2 py-1.5">
                  <div className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                    {t("search.messages")}
                  </div>
                  {filteredResults.messages.map((item) => (
                    <button
                      key={item.id}
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      onClick={() => handleSelect(`/inbox/${item.id}`)}
                    >
                      <Inbox className="mr-2 h-4 w-4" />
                      <div className="flex flex-col items-start">
                        <span>{item.name}</span>
                        <span className="text-xs text-muted-foreground">{t("search.from")}: {item.from}</span>
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {filteredResults.events.length > 0 && (
                <div className="px-2 py-1.5">
                  <div className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                    {t("search.events")}
                  </div>
                  {filteredResults.events.map((item) => (
                    <button
                      key={item.id}
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                      onClick={() => handleSelect(`/calendar/${item.id}`)}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      <div className="flex flex-col items-start">
                        <span>{item.name}</span>
                        <span className="text-xs text-muted-foreground">{item.date}</span>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
