"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  TooltipProvider,
  Too<PERSON><PERSON>,
  Toolt<PERSON>Trigger,
  TooltipContent,
} from "@mug/components/ui/tooltip";
import { sidebarMenuItems } from "@mug/constants/sidebar-menu";

export function AppSidebar() {
  const pathname = usePathname() || "";

  return (
    <div className="flex flex-col h-full px-8">
      <TooltipProvider delayDuration={0}>
        <nav className="flex flex-col justify-center items-center space-y-3 flex-1">
          {sidebarMenuItems.map((item) => {
            const isActive = pathname.startsWith(item.href);
            return (
              <Tooltip key={item.id}>
                <TooltipTrigger asChild>
                  <Link
                    href={item.href}
                    className={`flex items-center justify-center w-8 h-8 rounded-md transition-colors ${
                      isActive
                        ? "bg-sidebar-accent text-sidebar-accent-foreground"
                        : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                    }`}
                  >
                    <item.icon className="h-5 w-5" />
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right" align="center">
                  {item.label}
                  {item.badge && (
                    <span className="ml-2 bg-primary text-primary-foreground text-xs rounded-full px-2 py-0.5">
                      {item.badge}
                    </span>
                  )}
                </TooltipContent>
              </Tooltip>
            );
          })}
        </nav>
      </TooltipProvider>
    </div>
  );
}
