"use client";

import { useTheme } from "@mug/contexts/theme";
import { useTranslate } from "@mug/contexts/translate";
import { Theme } from "@mug/models/core";
import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@mug/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";

interface ThemeToggleProps {
  variant?: "icon" | "button";
}

export function ThemeToggle({ variant = "icon" }: ThemeToggleProps) {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const { t } = useTranslate();

  // Se estiver no modo texto (dentro do menu do usuário), renderizar apenas os itens
  if (variant === "text") {
    return (
      <>
        <DropdownMenuItem onClick={() => setTheme(Theme.LIGHT)} className="flex justify-between">
          <div className="flex items-center">
            <Sun className="mr-2 h-4 w-4" />
            <span>{t("themes.light")}</span>
          </div>
          {theme === Theme.LIGHT && <Check className="h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme(Theme.DARK)} className="flex justify-between">
          <div className="flex items-center">
            <Moon className="mr-2 h-4 w-4" />
            <span>{t("themes.dark")}</span>
          </div>
          {theme === Theme.DARK && <Check className="h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme(Theme.SYSTEM)} className="flex justify-between">
          <div className="flex items-center">
            <svg
              className="mr-2 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect x="2" y="3" width="20" height="14" rx="2" />
              <line x1="8" x2="16" y1="21" y2="21" />
              <line x1="12" x2="12" y1="17" y2="21" />
            </svg>
            <span>{t("themes.system")}</span>
          </div>
          {theme === Theme.SYSTEM && <Check className="h-4 w-4" />}
        </DropdownMenuItem>
      </>
    );
  }

  // Modo ícone (dropdown independente)
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-9 w-9">
          <Sun className="h-[18px] w-[18px] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[18px] w-[18px] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">{t("common.toggleTheme")}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t("common.selectTheme")}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => setTheme(Theme.LIGHT)}>
          <Sun className="mr-2 h-4 w-4" />
          <span>{t("themes.light")}</span>
          {theme === Theme.LIGHT && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme(Theme.DARK)}>
          <Moon className="mr-2 h-4 w-4" />
          <span>{t("themes.dark")}</span>
          {theme === Theme.DARK && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme(Theme.SYSTEM)}>
          <svg
            className="mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <rect x="2" y="3" width="20" height="14" rx="2" />
            <line x1="8" x2="16" y1="21" y2="21" />
            <line x1="12" x2="12" y1="17" y2="21" />
          </svg>
          <span>{t("themes.system")}</span>
          {theme === Theme.SYSTEM && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
