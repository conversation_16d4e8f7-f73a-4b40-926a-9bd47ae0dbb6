"use client";

import { ReactNode, useEffect } from "react";
import { useRouter } from "next/navigation";

import { useAuth } from "@mug/contexts/auth";

interface ProtectedProps {
  children: ReactNode;
}
export function ProtectedRoute(props: ProtectedProps) {
  const { children } = props;

  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push("/login");
    }
  }, [user, loading, router]);

  if (loading || !user) return <p>Carregando...</p>;

  return children;
}
