"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  TooltipProvider,
  Too<PERSON>ip,
  TooltipTrigger,
  TooltipContent,
} from "@mug/components/ui/tooltip";
import { sidebarMenuItems } from "@mug/constants/sidebar-menu";

export function AppNavBar() {
  const pathname = usePathname() || "";

  return (
    <div className="fixed flex justify-center bottom-6 px-8 right-6 left-6">
      <TooltipProvider delayDuration={0}>
        <nav className="flex flex-row justify-center items-center space-x-4 rounded-full p-4 shadow-lg border border-neutral-100 bg-neutral-100/50 dark:bg-neutral-950/30 dark:border-neutral-950 backdrop-blur-sm">
          {sidebarMenuItems.map((item) => {
            const isActive = pathname.startsWith(item.href);
            return (
              <Tooltip key={item.id}>
                <TooltipTrigger asChild>
                  <Link
                    href={item.href}
                    className={`flex items-center justify-center w-8 h-8 rounded-md transition-colors ${
                      isActive
                        ? "bg-sidebar-accent text-sidebar-accent-foreground"
                        : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                    }`}
                  >
                    <item.icon className="h-6 w-6" />
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="top" align="center">
                  {item.label}
                  {item.badge && (
                    <span className="ml-2 bg-primary text-primary-foreground text-xs rounded-full px-2 py-0.5">
                      {item.badge}
                    </span>
                  )}
                </TooltipContent>
              </Tooltip>
            );
          })}
        </nav>
      </TooltipProvider>
    </div>
  );
}
