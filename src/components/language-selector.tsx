"use client";

import { useTranslate } from "@mug/contexts/translate";
import { Check, Globe } from "lucide-react";
import { But<PERSON> } from "@mug/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@mug/components/ui/dropdown-menu";

interface LanguageSelectorProps {
  variant?: "icon" | "text";
}

export function LanguageSelector({ variant = "icon" }: LanguageSelectorProps) {
  const { locale, setLocale, t, availableLocales } = useTranslate();

  // Se estiver no modo texto (dentro do menu do usuário), renderizar apenas os itens
  if (variant === "text") {
    return (
      <>
        {availableLocales.map((lang) => (
          <DropdownMenuItem
            key={lang}
            onClick={() => setLocale(lang)}
            className="flex justify-between"
          >
            <div className="flex items-center">
              <Globe className="mr-2 h-4 w-4" />
              <span className="flex items-center">
                {t(`languages.${lang}`)}
                <span className="ml-1.5 text-xs px-1 py-0.5 rounded bg-muted text-muted-foreground font-medium">
                  {lang === "pt-BR" ? "PT" : lang === "en" ? "EN" : "ES"}
                </span>
              </span>
            </div>
            {locale === lang && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
        ))}
      </>
    );
  }

  // Modo ícone (dropdown independente)
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-9 w-9">
          <Globe className="h-[18px] w-[18px]" />
          <span className="sr-only">{t("common.language")}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t("common.selectLanguage")}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {availableLocales.map((lang) => (
          <DropdownMenuItem
            key={lang}
            onClick={() => setLocale(lang)}
            className="flex justify-between"
          >
            <div className="flex items-center">
              <Globe className="mr-2 h-4 w-4" />
              <span className="flex items-center">
                {t(`languages.${lang}`)}
                <span className="ml-1.5 text-xs px-1 py-0.5 rounded bg-muted text-muted-foreground font-medium">
                  {lang === "pt-BR" ? "PT" : lang === "en" ? "EN" : "ES"}
                </span>
              </span>
            </div>
            {locale === lang && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
