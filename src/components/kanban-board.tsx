import { Dispatch, Fragment, SetStateAction } from "react";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from "react-beautiful-dnd";
import {
  ChevronRight,
  ChevronLeft,
  Trash,
  CirclePlus,
  SquarePlus,
} from "lucide-react";
import { toast } from "sonner";
import { Lane } from "@mug/models/lane";
import { Item } from "@mug/models/item";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";
import { ClassValue } from "clsx";
import { Button } from "./ui/button";

interface KanbanBoardProps {
  editMode?: boolean;
  lanes: Lane[];
  onUpdateLanes: Dispatch<SetStateAction<Lane[]>>;
  onAddItem: (id: string) => void;
  onDropItem: (result: DropResult) => void;
  addNewLane: (newLane?: Lane) => Lane;
  className?: ClassValue;
}
export default function KanbanBoard(props: KanbanBoardProps) {
  const {
    lanes,
    onUpdateLanes,
    onAddItem,
    onDropItem,
    addNewLane,
    className,
    editMode = false,
  } = props;

  const addNewLaneHandler = () => {
    const newLane = addNewLane();
    onUpdateLanes([...lanes, newLane]);
  };

  const removeLaneHandler = (laneID: string) => {
    onUpdateLanes(lanes.filter(({ id }: Lane) => id !== laneID));
  };

  const moveLaneHandler = (laneID: string, direction: "left" | "right") => {
    const index: number = lanes.findIndex(({ id }: Lane) => id === laneID);
    if (index < 0) return;

    const newIndex: number = direction === "left" ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= lanes.length) return;

    const updatedLanes = [...lanes];
    const [movedLane] = updatedLanes.splice(index, 1);
    updatedLanes.splice(newIndex, 0, movedLane);

    onUpdateLanes(updatedLanes);
  };

  const verifyLaneDrop = (result: DropResult) => {
    const { source, destination } = result;

    if (!destination) return;

    const fromIndex: number = lanes.findIndex(
      ({ id }: Lane) => id === source?.droppableId
    );
    const toIndex: number = lanes.findIndex(
      ({ id }: Lane) => id === destination?.droppableId
    );

    const destLane = lanes[toIndex];

    if (source?.droppableId === destination.droppableId) {
      onDropItem(result);
      return;
    }

    if (!destLane) return;

    const allowDrop = destLane.allowDrop ?? "all";

    const isNewItem = !source;

    const isValidDrop =
      allowDrop === "all" ||
      (allowDrop === "new" && isNewItem) ||
      (allowDrop === "only-left" && fromIndex === toIndex - 1) ||
      (allowDrop === "only-right" && fromIndex === toIndex + 1);

    if (allowDrop === "none" || !isValidDrop) {
      toast.error(
        `O registro não pode ser movido para a etapa ${lanes[toIndex].title}`
      );
      return;
    }

    onDropItem(result);
  };

  return (
    <div className={classNameBuilder("overflow-x-auto select-none", className)}>
      <DragDropContext onDragEnd={verifyLaneDrop}>
        <div className="space-x-4 w-full flex">
          {!!lanes.length ? (
            <Fragment>
              {lanes.map((lane: Lane) => (
                <div
                  key={lane.id}
                  className="p-2 rounded-lg shadow-md min-h-[500px] min-w-sm w-full max-w-sm border-gray-300 border hover:shadow-lg hover:border-gray-400 hover:bg-gray-300/25 transition-all"
                >
                  <div className="flex justify-between items-center p-4 pb-2">
                    {editMode &&
                      lanes.findIndex(({ id }: Lane) => id === lane.id) > 0 && (
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => moveLaneHandler(lane.id, "left")}
                        >
                          <ChevronLeft />
                        </Button>
                      )}
                    <h2 className="text-xl flex-1 font-semibold">
                      {lane.title} ({lane.items.length})
                    </h2>
                    {editMode && (
                      <Button
                        size="icon"
                        variant="destructive"
                        onClick={() => removeLaneHandler(lane.id)}
                      >
                        <Trash />
                      </Button>
                    )}
                    {editMode &&
                      lanes.findIndex(({ id }: Lane) => id === lane.id) <
                        lanes.length - 1 && (
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => moveLaneHandler(lane.id, "right")}
                        >
                          <ChevronRight />
                        </Button>
                      )}
                    {!editMode && lane.allowDrop !== "none" && (
                      <Button
                        size="icon"
                        onClick={() => onAddItem(lane.id)}
                      >
                        <SquarePlus />
                      </Button>
                    )}
                  </div>
                  <Droppable droppableId={lane.id} direction="vertical">
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="space-y-3 p-3"
                      >
                        {lane.items.map((item: Item, index: number) => (
                          <Draggable
                            key={item.id}
                            draggableId={item.id}
                            index={index}
                          >
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                              >
                                <div
                                  className={classNameBuilder(
                                    "p-4 rounded-sm bg-gray-200 border hover:border-gray-300 transition-all cursor-pointer"
                                  )}
                                >
                                  {item.title}
                                  testess
                                </div>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </div>
              ))}
              {editMode && (
                <button
                  className="p-2 rounded-lg flex flex-col justify-center items-center border-dashed shadow-md min-h-[500px] min-w-sm flex-1 text-gray-300 border-gray-200 border hover:shadow-lg hover:text-gray-400 hover:border-gray-400 hover:bg-gray-300/25 transition-all cursor-pointer"
                  onClick={addNewLaneHandler}
                >
                  <SquarePlus />
                  <p>Adicionar Novo</p>
                </button>
              )}
            </Fragment>
          ) : (
            <div className="w-full flex">
              {editMode ? (
                <button
                  className="p-4 rounded-lg flex flex-col justify-center items-center border-dashed shadow-md min-h-[500px] min-w-sm flex-1 text-gray-300 border-gray-200 border hover:shadow-lg hover:text-gray-400 hover:border-gray-400 hover:bg-gray-300/25 transition-all cursor-pointer"
                  onClick={addNewLaneHandler}
                >
                  <SquarePlus />
                  <p>Adicionar Novo</p>
                </button>
              ) : (
                <div className="p-4 flex flex-1 flex-col justify-center items-center h-[500px]">
                  Nenhum Board
                </div>
              )}
            </div>
          )}
        </div>
      </DragDropContext>
    </div>
  );
}
