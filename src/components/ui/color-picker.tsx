"use client";

import * as React from "react";
import { Check, ChevronDown } from "lucide-react";
import { classNameBuilder } from "@mug/lib/utils/class-name-builder";
import { Button } from "@mug/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@mug/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@mug/components/ui/popover";

export interface ColorOption {
  id: string;
  name: string;
  value: string;
}

interface ColorPickerProps {
  options: ColorOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export function ColorPicker({
  options,
  value,
  onChange,
  placeholder = "Select color...",
  disabled = false,
  className,
}: ColorPickerProps) {
  const [open, setOpen] = React.useState(false);

  // Find the selected color option
  const selectedColor = React.useMemo(() => {
    return options.find((option) => option.value === value) || options[0];
  }, [options, value]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={classNameBuilder("w-full justify-between", className)}
          disabled={disabled}
        >
          <div className="flex items-center gap-2">
            <div
              className="h-4 w-4 rounded-full border"
              style={{ backgroundColor: selectedColor.value }}
            />
            <span>{selectedColor.name}</span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder={`Search ${placeholder.toLowerCase()}`} />
          <CommandEmpty>No color found.</CommandEmpty>
          <CommandGroup>
            <CommandList>
              {options.map((option) => (
                <CommandItem
                  key={option.id}
                  value={option.id}
                  onSelect={() => {
                    onChange(option.value);
                    setOpen(false);
                  }}
                >
                  <div className="flex items-center gap-2 w-full">
                    <div
                      className="h-4 w-4 rounded-full border"
                      style={{ backgroundColor: option.value }}
                    />
                    <span>{option.name}</span>
                  </div>
                  {option.value === value && (
                    <Check className="ml-auto h-4 w-4" />
                  )}
                </CommandItem>
              ))}
            </CommandList>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
