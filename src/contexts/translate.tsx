"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import Cookies from "js-cookie";

import { locales, Locale } from "@mug/intl/config";
import pt_br from "@mug/intl/pt-br.json";
import en from "@mug/intl/en.json";
import es from "@mug/intl/es.json";

const translationsMap = {
  "pt-BR": pt_br,
  en: en,
  es: es,
};

interface TranslateContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
  availableLocales: typeof locales;
}

const TranslateContext = createContext<TranslateContextType | undefined>(
  undefined
);

interface TranslateProviderProps {
  children: ReactNode;
}

export function TranslateProvider({ children }: TranslateProviderProps) {
  const [locale, setLocaleState] = useState<Locale>("pt-BR");

  useEffect(() => {
    const savedLocale = localStorage.getItem("locale") as Locale | null;
    if (savedLocale && locales.includes(savedLocale)) {
      setLocaleState(savedLocale);
      return;
    }

    const browserLocale = navigator.language;
    const matchedLocale = locales.find(
      (loc) =>
        browserLocale.startsWith(loc) ||
        browserLocale.startsWith(loc.split("-")[0])
    );

    if (matchedLocale) {
      setLocaleState(matchedLocale);
      localStorage.setItem("locale", matchedLocale);
    }
  }, []);

  const t = (key: string, params?: Record<string, string | number>): string => {
    try {
      const translations = translationsMap[locale] || translationsMap["pt-BR"];

      const keys = key.split(".");
      let value: unknown = translations;

      for (const k of keys) {
        if (value && typeof value === "object" && k in value) {
          value = (value as Record<string, unknown>)[k];
        } else {
          throw new Error(`Translation key not found: ${key}`);
        }
      }

      if (typeof value !== "string") {
        throw new Error(`Translation value is not a string: ${key}`);
      }

      if (params) {
        return Object.entries(params).reduce(
          (str, [paramKey, paramValue]) =>
            str.replace(new RegExp(`{${paramKey}}`, "g"), String(paramValue)),
          value
        );
      }

      return value;
    } catch (error) {
      console.warn(
        "[contexts]",
        "[translate]",
        "t",
        `Translation error: ${error}`
      );
      return key.split(".").pop() || key;
    }
  };

  const setLocale = (newLocale: Locale) => {
    if (locales.includes(newLocale)) {
      localStorage.setItem("locale", newLocale);

      Cookies.set("NEXT_LOCALE", newLocale, { expires: 365 });

      setLocaleState(newLocale);

      window.location.reload();
    }
  };

  const value = {
    locale,
    setLocale,
    t,
    availableLocales: locales,
  };

  return (
    <TranslateContext.Provider value={value}>
      {children}
    </TranslateContext.Provider>
  );
}

export function useTranslate() {
  const context = useContext(TranslateContext);

  if (context === undefined) {
    throw new Error("useTranslate must be used within a TranslateProvider");
  }

  return context;
}
