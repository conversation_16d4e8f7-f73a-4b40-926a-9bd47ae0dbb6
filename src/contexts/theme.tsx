"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { Theme } from "@mug/models/core";

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  resolvedTheme: "light" | "dark";
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(Theme.LIGHT);
  const [resolvedTheme, setResolvedTheme] = useState<"light" | "dark">("light");

  const getSystemTheme = (): "light" | "dark" => {
    if (typeof window === "undefined") return "light";
    return window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light";
  };

  useEffect(() => {
    const savedTheme = localStorage.getItem("theme") as string | null;
    if (
      savedTheme &&
      (savedTheme === Theme.LIGHT ||
        savedTheme === Theme.DARK ||
        savedTheme === Theme.SYSTEM)
    ) {
      setThemeState(savedTheme as Theme);

      if (savedTheme === Theme.SYSTEM) {
        setResolvedTheme(getSystemTheme());
      } else {
        setResolvedTheme(savedTheme as "light" | "dark");
      }
      return;
    }

    const systemTheme = getSystemTheme();
    setThemeState(Theme.SYSTEM);
    setResolvedTheme(systemTheme);
    localStorage.setItem("theme", Theme.SYSTEM);
  }, []);

  useEffect(() => {
    const root = window.document.documentElement;

    if (resolvedTheme === "dark") {
      root.classList.add("dark");
    } else {
      root.classList.remove("dark");
    }
  }, [resolvedTheme]);

  useEffect(() => {
    if (theme !== Theme.SYSTEM) return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const handleChange = (e: MediaQueryListEvent) => {
      setResolvedTheme(e.matches ? "dark" : "light");
    };

    mediaQuery.addEventListener("change", handleChange);

    return () => {
      mediaQuery.removeEventListener("change", handleChange);
    };
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    if (
      newTheme === Theme.LIGHT ||
      newTheme === Theme.DARK ||
      newTheme === Theme.SYSTEM
    ) {
      localStorage.setItem("theme", newTheme);

      setThemeState(newTheme);

      if (newTheme === Theme.SYSTEM) {
        setResolvedTheme(getSystemTheme());
      } else {
        setResolvedTheme(newTheme as "light" | "dark");
      }
    }
  };

  const toggleTheme = () => {
    if (theme === Theme.LIGHT) {
      setTheme(Theme.DARK);
    } else if (theme === Theme.DARK) {
      setTheme(Theme.SYSTEM);
    } else {
      setTheme(Theme.LIGHT);
    }
  };

  const value = {
    theme,
    setTheme,
    toggleTheme,
    resolvedTheme,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }

  return context;
}
