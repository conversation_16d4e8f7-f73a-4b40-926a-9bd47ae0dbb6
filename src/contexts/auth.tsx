"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from "react";
import { useRouter, usePathname } from "next/navigation";
import { User } from "@mug/models/user";
import { AuthSession } from "@mug/models/auth";
import {
  LoginCredentialsDto,
  TwoFactorVerificationDto,
  PasswordResetCompletionDto,
  InvitationAcceptanceDto,
} from "@mug/services/dtos";
import {
  login,
  loginWithGoogle,
  exchangeGoogleCode,
  requestMagicLink,
  verifyMagicLink,
  verifyTwoFactor,
  requestPasswordReset,
  resetPassword,
  acceptInvitation,
  logout,
  refreshToken,
} from "@mug/services/auth";
import { jwtService } from "@mug/services/auth";

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  requiresTwoFactor: boolean;
  shouldShowTwoFactorReminder: boolean;
  login: (
    credentials: LoginCredentialsDto,
    returnUrl?: string
  ) => Promise<void>;
  loginWithGoogle: (returnUrl?: string) => void;
  handleGoogleCallback: (
    code: string,
    state: string | null,
    returnUrl?: string
  ) => Promise<void>;
  requestMagicLink: (email: string) => Promise<void>;
  verifyMagicLink: (token: string, returnUrl?: string) => Promise<void>;
  verifyTwoFactor: (
    verification: TwoFactorVerificationDto,
    returnUrl?: string
  ) => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (reset: PasswordResetCompletionDto) => Promise<void>;
  acceptInvitation: (acceptance: InvitationAcceptanceDto) => Promise<void>;
  logout: () => Promise<void>;
  error: string | null;
  clearError: () => void;
  dismissTwoFactorReminder: () => void;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  requiresTwoFactor: boolean;
  shouldShowTwoFactorReminder: boolean;
  session: AuthSession | null;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | null>(null);

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  requiresTwoFactor: false,
  shouldShowTwoFactorReminder: false,
  session: null,
  error: null,
};

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, setState] = useState<AuthState>(initialState);
  const router = useRouter();

  const isTokenExpiringSoon = useCallback((token: string): boolean => {
    try {
      const payload = jwtService.decodeToken(token);
      if (!payload.exp) return true;

      const expiryTime = payload.exp * 1000;
      const currentTime = Date.now();

      return expiryTime - currentTime < 5 * 60 * 1000;
    } catch (error) {
      console.error(
        "[contexts]",
        "[auth]",
        "isTokenExpiringSoon",
        "error checking token expiration:",
        error
      );
      return false;
    }
  }, []);

  const handleTokenRefresh = useCallback(async () => {
    try {
      console.log(
        "[contexts]",
        "[auth]",
        "handleTokenRefresh",
        "refreshing token..."
      );
      const { user, session } = await refreshToken();

      const shouldRemind = checkIfShouldRemindAbout2FA(user);

      localStorage.setItem("auth_session", JSON.stringify({ user, session }));

      setState({
        user,
        isAuthenticated: true,
        isLoading: false,
        requiresTwoFactor: Boolean(
          session.requiresTwoFactor && !session.twoFactorVerified
        ),
        shouldShowTwoFactorReminder: shouldRemind,
        session,
        error: null,
      });

      console.log(
        "[contexts]",
        "[auth]",
        "handleTokenRefresh",
        "token refreshed successfully"
      );
      return true;
    } catch (error) {
      console.error(
        "[contexts]",
        "[auth]",
        "handleTokenRefresh",
        "failed to refresh token:",
        error
      );

      localStorage.removeItem("auth_session");
      setState({
        ...initialState,
        isLoading: false,
        error: "Your session has expired. Please log in again.",
      });

      return false;
    }
  }, []);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const sessionData = localStorage.getItem("auth_session");

        if (sessionData) {
          const { user, session } = JSON.parse(sessionData);

          if (new Date(session.expiresAt) < new Date()) {
            localStorage.removeItem("auth_session");
            setState({
              ...initialState,
              isLoading: false,
            });
            return;
          }

          if (isTokenExpiringSoon(session.token)) {
            console.log(
              "[contexts]",
              "[auth]",
              "checkAuth",
              "token is about to expire, refreshing..."
            );
            const refreshed = await handleTokenRefresh();
            if (!refreshed) {
              return;
            }
          } else {
            const shouldRemind = checkIfShouldRemindAbout2FA(user);

            setState({
              user,
              isAuthenticated: true,
              isLoading: false,
              requiresTwoFactor:
                session.requiresTwoFactor && !session.twoFactorVerified,
              shouldShowTwoFactorReminder: shouldRemind,
              session,
              error: null,
            });
          }
        } else {
          // No session found
          setState({
            ...initialState,
            isLoading: false,
          });
        }
      } catch (error) {
        console.error(
          "[contexts]",
          "[auth]",
          "checkAuth",
          "error checking authentication:",
          error
        );
        setState({
          ...initialState,
          isLoading: false,
          error: "Failed to check authentication status",
        });
      }
    };

    checkAuth();
  }, [isTokenExpiringSoon, handleTokenRefresh]);

  useEffect(() => {
    if (state.isAuthenticated && state.session?.token) {
      console.log(
        "[contexts]",
        "[auth]",
        "checkAuth",
        "setting up token refresh interval"
      );

      const intervalId = setInterval(() => {
        if (state.session?.token && isTokenExpiringSoon(state.session.token)) {
          console.log(
            "[contexts]",
            "[auth]",
            "checkAuth",
            "token is about to expire, refreshing..."
          );
          handleTokenRefresh().catch((error) => {
            console.error(
              "[contexts]",
              "[auth]",
              "checkAuth",
              "error in token refresh interval:",
              error
            );
          });
        }
      }, 60000);

      return () => {
        console.log(
          "[contexts]",
          "[auth]",
          "checkAuth",
          "clearing token refresh interval"
        );
        clearInterval(intervalId);
      };
    }
  }, [
    state.isAuthenticated,
    state.session,
    isTokenExpiringSoon,
    handleTokenRefresh,
  ]);

  const checkIfShouldRemindAbout2FA = (user: User): boolean => {
    if (user.twoFactorEnabled) {
      return false;
    }

    const reminderDateStr = localStorage.getItem("two_factor_reminder_date");
    if (!reminderDateStr) {
      return true;
    }

    const reminderDate = new Date(reminderDateStr);
    const now = new Date();
    const daysDiff = Math.floor(
      (now.getTime() - reminderDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    return daysDiff >= 15;
  };

  const handleLogin = async (
    credentials: LoginCredentialsDto,
    returnUrl?: string
  ) => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      const { user, session } = await login(credentials);

      const shouldRemind = checkIfShouldRemindAbout2FA(user);

      localStorage.setItem("auth_session", JSON.stringify({ user, session }));

      setState({
        user,
        isAuthenticated: true,
        isLoading: false,
        requiresTwoFactor: Boolean(
          session.requiresTwoFactor && !session.twoFactorVerified
        ),
        shouldShowTwoFactorReminder: shouldRemind,
        session,
        error: null,
      });

      if (session.requiresTwoFactor && !session.twoFactorVerified) {
        if (returnUrl) {
          localStorage.setItem("auth_return_url", returnUrl);
        }
        router.push("/auth/two-factor");
      } else if (shouldRemind) {
        router.push("/auth/two-factor-reminder");
      } else {
        router.push(returnUrl || "/dashboard");
      }
    } catch (error) {
      console.error(
        "[contexts]",
        "[auth]",
        "handleLogin",
        "login error:",
        error
      );
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to login",
      }));
    }
  };

  const handleGoogleLogin = (returnUrl?: string) => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));
    loginWithGoogle(returnUrl);
  };

  const handleGoogleCallback = async (
    code: string,
    state: string | null,
    returnUrl?: string
  ) => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      const storedReturnUrl = localStorage.getItem("google_auth_return_url");

      const finalReturnUrl = returnUrl || storedReturnUrl || undefined;
      localStorage.removeItem("google_auth_return_url");

      const { user, session } = await exchangeGoogleCode(code, state);

      const shouldRemind = checkIfShouldRemindAbout2FA(user);

      localStorage.setItem("auth_session", JSON.stringify({ user, session }));

      setState({
        user,
        isAuthenticated: true,
        isLoading: false,
        requiresTwoFactor: Boolean(
          session.requiresTwoFactor && !session.twoFactorVerified
        ),
        shouldShowTwoFactorReminder: shouldRemind,
        session,
        error: null,
      });

      sessionStorage.removeItem("processing_google_callback");
      sessionStorage.removeItem("processing_google_callback_timestamp");

      if (session.requiresTwoFactor && !session.twoFactorVerified) {
        if (finalReturnUrl) {
          localStorage.setItem("auth_return_url", finalReturnUrl);
        }
        router.push("/auth/two-factor");
      } else if (shouldRemind) {
        router.push("/auth/two-factor-reminder");
      } else {
        router.push(finalReturnUrl || "/dashboard");
      }
    } catch (error) {
      sessionStorage.removeItem("processing_google_callback");
      sessionStorage.removeItem("processing_google_callback_timestamp");

      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to authenticate with Google",
      }));
    }
  };

  const handleRequestMagicLink = async (email: string) => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      await requestMagicLink(email);

      setState((prev) => ({
        ...prev,
        isLoading: false,
      }));

      router.push("/auth/magic-link-sent");
    } catch (error) {
      console.error(
        "[contexts]",
        "[auth]",
        "handleRequestMagicLink",
        "magic link request error:",
        error
      );
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to request magic link",
      }));
    }
  };

  const handleVerifyMagicLink = async (token: string, returnUrl?: string) => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      const storedReturnUrl = localStorage.getItem("magic_link_return_url");
      const finalReturnUrl = returnUrl || storedReturnUrl || undefined;
      localStorage.removeItem("magic_link_return_url");

      const { user, session } = await verifyMagicLink(token);

      const shouldRemind = checkIfShouldRemindAbout2FA(user);

      localStorage.setItem("auth_session", JSON.stringify({ user, session }));

      setState({
        user,
        isAuthenticated: true,
        isLoading: false,
        requiresTwoFactor: Boolean(
          session.requiresTwoFactor && !session.twoFactorVerified
        ),
        shouldShowTwoFactorReminder: shouldRemind,
        session,
        error: null,
      });

      if (session.requiresTwoFactor && !session.twoFactorVerified) {
        if (finalReturnUrl) {
          localStorage.setItem("auth_return_url", finalReturnUrl);
        }
        router.push("/auth/two-factor");
      } else if (shouldRemind) {
        router.push("/auth/two-factor-reminder");
      } else {
        router.push(finalReturnUrl || "/dashboard");
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to verify magic link",
      }));
    }
  };

  const handleVerifyTwoFactor = async (
    verification: TwoFactorVerificationDto,
    returnUrl?: string
  ) => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      const storedReturnUrl = localStorage.getItem("auth_return_url");
      const finalReturnUrl = returnUrl || storedReturnUrl || undefined;
      localStorage.removeItem("auth_return_url");

      const { user, session } = await verifyTwoFactor(verification);

      localStorage.setItem("auth_session", JSON.stringify({ user, session }));

      setState({
        user,
        isAuthenticated: true,
        isLoading: false,
        requiresTwoFactor: false,
        shouldShowTwoFactorReminder: false,
        session,
        error: null,
      });

      router.push(finalReturnUrl || "/dashboard");
    } catch (error) {
      console.error(
        "[contexts]",
        "[auth]",
        "handleVerifyTwoFactor",
        "two-factor verification error:",
        error
      );
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to verify two-factor authentication",
      }));
    }
  };

  const handleDismissTwoFactorReminder = () => {
    const now = new Date().toISOString();
    localStorage.setItem("two_factor_reminder_date", now);

    setState((prev) => ({
      ...prev,
      shouldShowTwoFactorReminder: false,
    }));
  };

  const handleRequestPasswordReset = async (email: string) => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      await requestPasswordReset(email);

      setState((prev) => ({
        ...prev,
        isLoading: false,
      }));

      router.push("/auth/reset-password-sent");
    } catch (error) {
      console.error(
        "[contexts]",
        "[auth]",
        "handleRequestPasswordReset",
        "password reset request error:",
        error
      );
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to request password reset",
      }));
    }
  };

  const handleResetPassword = async (reset: PasswordResetCompletionDto) => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      await resetPassword(reset);

      setState((prev) => ({
        ...prev,
        isLoading: false,
      }));

      router.push("/auth/login?reset=success");
    } catch (error) {
      console.error(
        "[contexts]",
        "[auth]",
        "handleResetPassword",
        "password reset error:",
        error
      );
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error ? error.message : "Failed to reset password",
      }));
    }
  };

  const handleAcceptInvitation = async (
    acceptance: InvitationAcceptanceDto
  ) => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      const { user, session } = await acceptInvitation(acceptance);

      const shouldRemind = checkIfShouldRemindAbout2FA(user);

      localStorage.setItem("auth_session", JSON.stringify({ user, session }));

      setState({
        user,
        isAuthenticated: true,
        isLoading: false,
        requiresTwoFactor: false,
        shouldShowTwoFactorReminder: shouldRemind,
        session,
        error: null,
      });

      if (shouldRemind) {
        router.push("/auth/two-factor-reminder");
      } else {
        router.push("/dashboard");
      }
    } catch (error) {
      console.error(
        "[contexts]",
        "[auth]",
        "handleAcceptInvitation",
        "invitation acceptance error:",
        error
      );
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to accept invitation",
      }));
    }
  };

  const handleLogout = async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true }));

      await logout(state.session?.id);

      localStorage.removeItem("auth_session");

      setState({
        ...initialState,
        isLoading: false,
      });

      router.push("/auth/login");
    } catch (error) {
      console.error(
        "[contexts]",
        "[auth]",
        "handleLogout",
        "logout error:",
        error
      );
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to logout",
      }));
    }
  };

  const clearError = () => {
    setState((prev) => ({ ...prev, error: null }));
  };

  const value: AuthContextType = {
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    requiresTwoFactor: state.requiresTwoFactor,
    shouldShowTwoFactorReminder: state.shouldShowTwoFactorReminder,
    login: handleLogin,
    loginWithGoogle: handleGoogleLogin,
    handleGoogleCallback,
    requestMagicLink: handleRequestMagicLink,
    verifyMagicLink: handleVerifyMagicLink,
    verifyTwoFactor: handleVerifyTwoFactor,
    requestPasswordReset: handleRequestPasswordReset,
    resetPassword: handleResetPassword,
    acceptInvitation: handleAcceptInvitation,
    logout: handleLogout,
    error: state.error,
    clearError,
    dismissTwoFactorReminder: handleDismissTwoFactorReminder,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
}

interface ProtectedRouteProps {
  children: ReactNode;
  loading: ReactNode;
}

export function ProtectedRoute({ children, loading }: ProtectedRouteProps) {
  const {
    isAuthenticated,
    isLoading,
    requiresTwoFactor,
    shouldShowTwoFactorReminder,
  } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(
        `/auth/login?returnUrl=${encodeURIComponent(pathname || "")}`
      );
    } else if (
      !isLoading &&
      isAuthenticated &&
      requiresTwoFactor &&
      pathname !== "/auth/two-factor"
    ) {
      localStorage.setItem("auth_return_url", pathname);
      router.push("/auth/two-factor");
    } else if (
      !isLoading &&
      isAuthenticated &&
      !requiresTwoFactor &&
      shouldShowTwoFactorReminder &&
      pathname !== "/auth/two-factor-reminder" &&
      pathname !== "/profile"
    ) {
      router.push("/auth/two-factor-reminder");
    }
  }, [
    isAuthenticated,
    isLoading,
    requiresTwoFactor,
    shouldShowTwoFactorReminder,
    router,
    pathname,
  ]);

  if (isLoading) {
    return loading;
  }

  if (
    isAuthenticated &&
    (!requiresTwoFactor || pathname === "/auth/two-factor") &&
    (!shouldShowTwoFactorReminder ||
      pathname === "/auth/two-factor-reminder" ||
      pathname === "/profile")
  ) {
    return <>{children}</>;
  }

  return null;
}
