export const locales = ["pt-BR", "en", "es"] as const;
export type Locale = (typeof locales)[number];

export async function loadMessages(locale: string) {
  if (!locales.includes(locale as Locale)) {
    locale = "pt-BR";
  }

  try {
    return (await import(`./${locale}.json`)).default;
  } catch (error) {
    console.error(
      "[intl]",
      "[config]",
      "loadMessages",
      `Failed to load messages for locale: ${locale}`,
      error
    );
    return (await import(`./pt-br.json`)).default;
  }
}
