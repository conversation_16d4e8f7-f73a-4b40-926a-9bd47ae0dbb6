{"common": {"search": "Buscar", "searchPlaceholder": "Buscar em tudo...", "noResults": "Nenhum resultado encontrado.", "view": "Visualizar", "edit": "<PERSON><PERSON>", "delete": "Excluir", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "actions": "Ações", "profile": "Perfil", "myPlan": "<PERSON><PERSON>", "logout": "<PERSON><PERSON>", "days": "dias", "updated": "Atualizado", "comingSoon": "Em breve", "underDevelopmentDescription": "Esta funcionalidade está em desenvolvimento e estará disponível em breve.", "language": "Idioma", "selectLanguage": "Selecionar idioma", "loading": "Carregando...", "back": "Voltar", "theme": "<PERSON><PERSON>", "toggleTheme": "Alternar tema", "selectTheme": "Selecionar o tema", "title": "<PERSON><PERSON><PERSON><PERSON>", "titlePlaceholder": "Digite o título do item", "description": "Descrição", "descriptionPlaceholder": "Digite uma descrição para o item", "priority": "Prioridade", "high": "Alta", "medium": "Média", "low": "Baixa", "selectPriority": "Selecionar prioridade", "column": "Coluna", "selectColumn": "Selecionar coluna", "assignee": "Responsável", "selectAssignee": "Selecionar responsável", "unassigned": "Não atribuído", "dueDate": "Data de prazo", "selectDate": "Selecionar data", "progress": "Progresso", "tags": "Tags", "addTag": "Adicionar tag", "suggestedTags": "Tags sugeridas", "newItem": "Novo Item", "createItemDescription": "Preencha os campos abaixo para criar um novo item", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moveTo": "Mover para", "move": "Mover", "addItem": "<PERSON><PERSON><PERSON><PERSON>", "allColumns": "<PERSON><PERSON> as colu<PERSON>", "notSet": "<PERSON><PERSON> definido", "notAvailable": "Não disponível", "createdOn": "C<PERSON><PERSON> em", "lastUpdated": "Última atualização", "comments": "Comentários", "noComments": "<PERSON><PERSON><PERSON>", "activity": "Atividade", "itemCreated": "<PERSON>em criado", "close": "<PERSON><PERSON><PERSON>", "stats": "Estatísticas", "filters": "<PERSON><PERSON><PERSON>", "filterItems": "Filtrar itens", "reset": "Redefinir", "apply": "Aplicar", "clearAll": "<PERSON><PERSON> tudo", "requiredField": "Campo obrigatório", "allDates": "Todas as datas", "today": "Hoje", "last7Days": "Últimos 7 dias", "last30Days": "Últimos 30 dias", "thisMonth": "<PERSON><PERSON> mês", "thisYear": "Este ano", "customPeriod": "<PERSON><PERSON>do personalizado", "startDate": "Data inicial", "endDate": "Data final", "unknown": "Desconhecido", "newColumn": "Nova Coluna", "createColumnDescription": "Preencha os campos abaixo para criar uma nova coluna", "columnTitlePlaceholder": "Digite o título da coluna", "columnDescriptionPlaceholder": "Digite uma descrição para a coluna", "wipLimit": "<PERSON><PERSON> (WIP)", "wipLimitPlaceholder": "Digite o limite de itens", "wipLimitDescription": "Limite máximo de itens permitidos nesta coluna (opcional)", "position": "Posição", "selectPosition": "Selecionar posição", "positionStart": "No início", "positionEnd": "No final", "positionAfter": "Após uma coluna", "afterColumn": "Após a coluna", "columnCreated": "Coluna criada com sucesso!", "allowItemCreation": "Permitir <PERSON> de itens", "allowItemCreationDescription": "Se marcado, os usuários poderão criar novos itens diretamente nesta coluna", "movementRestriction": "Restrição de movimento", "selectRestriction": "Selecionar restrição", "restrictionNone": "Sem restrições", "restrictionOnlyLeft": "Apenas para a esquerda", "restrictionOnlyRight": "Apenas para a direita", "movementRestrictionDescription": "Define para onde os itens desta coluna podem ser movidos", "laneModel": "<PERSON><PERSON> <PERSON>", "selectModel": "Selecionar modelo", "laneModelDescription": "Define o tipo/propósito desta coluna no fluxo de trabalho", "modelDefault": "Padrão", "modelBacklog": "Backlog", "modelNew": "Novo", "modelNurture": "<PERSON><PERSON><PERSON><PERSON>", "modelPotential": "Potencial", "modelOpportunity": "Oportunidade", "modelDone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modelLost": "<PERSON><PERSON><PERSON>", "modelWon": "<PERSON><PERSON><PERSON>", "modelPending": "Pendente", "modelAttention": "Atenção"}, "themes": {"light": "<PERSON><PERSON><PERSON>", "dark": "Escuro", "system": "Sistema"}, "header": {"search": "Buscar em tudo...", "notifications": "Notificações", "markAllAsRead": "Marcar todas como lidas", "noNotifications": "Nenhuma notificação", "viewAllNotifications": "<PERSON><PERSON> to<PERSON> as notificaçõ<PERSON>", "inbox": "Caixa de entrada", "calendar": "<PERSON><PERSON><PERSON><PERSON>"}, "search": {"flows": "Fluxos", "people": "<PERSON><PERSON><PERSON><PERSON>", "documents": "Documentos", "messages": "Mensagens", "events": "Eventos", "from": "De"}, "notifications": {"newMessage": "Nova mensagem", "reminder": "Le<PERSON><PERSON>", "update": "Atualização", "newUser": "Novo usuário", "systemUpdate": "Atualização do sistema"}, "flows": {"title": "Fluxos", "description": "Gere<PERSON>ie seus fluxos de trabalho", "noFlows": "Nenhum fluxo encontrado", "createFlow": "<PERSON><PERSON><PERSON>", "newFlow": "Novo Fluxo", "createFlowDescription": "Preencha os campos abaixo para criar um novo fluxo de trabalho", "editFlowDescription": "Edite as informações do fluxo de trabalho", "flowUpdated": "Fluxo atualizado com sucesso", "flowTitlePlaceholder": "Digite o título do fluxo", "flowDescriptionPlaceholder": "Descreva o propósito deste fluxo de trabalho", "viewFlow": "Ver Fluxo", "editFlow": "<PERSON><PERSON>", "deleteFlow": "Excluir Fluxo", "flowDeleted": "Fluxo excluído com sucesso", "flowCreated": "Fluxo criado com sucesso", "confirmDeleteFlow": "Tem certeza que deseja excluir este fluxo?", "flowDetails": "Detalhes do Fluxo", "flowName": "Nome do Fluxo", "flowType": "Tipo de Fluxo", "selectFlowType": "Selecione o tipo de fluxo", "typeDescription": "Categoriza o fluxo de acordo com sua finalidade principal", "typeCannotBeChanged": "O tipo de fluxo não pode ser alterado após a criação", "modeCannotBeChanged": "O modo de atuação não pode ser alterado após a criação", "templateCannotBeChanged": "O modelo inicial não pode ser alterado após a criação", "flowMode": "Modo de Atuação", "selectFlowMode": "Selecione o modo de atuação", "modeDescription": "Define o modo de atuação deste fluxo no processo", "flowCreatedAt": "C<PERSON><PERSON> em", "flowUpdatedAt": "Atualizado em", "flowCreatedBy": "<PERSON><PERSON><PERSON> por", "flowStatus": "Status", "flowActions": "Ações", "addItem": "<PERSON><PERSON><PERSON><PERSON>", "editLanes": "<PERSON><PERSON>", "wipLimit": "Limite WIP", "showDetails": "<PERSON><PERSON> de<PERSON>", "hideDetails": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "noDescription": "Sem descrição", "items": "itens", "details": "<PERSON><PERSON><PERSON>", "noMembers": "<PERSON><PERSON><PERSON> membro", "members": "Me<PERSON><PERSON>", "archived": "Arquivado", "addLane": "<PERSON><PERSON><PERSON><PERSON>", "kanbanView": "Visualização Kanban", "listView": "Visualização em Lista", "filterItems": "Filtrar Itens", "noItems": "Nenhum item encontrado", "itemDetails": "Detalhes do Item", "itemCreated": "Item criado com sucesso", "itemUpdated": "Item atualizado com sucesso", "itemDeleted": "Item excluído com sucesso", "confirmDeleteItem": "Tem certeza que deseja excluir este item?", "moveItem": "Mover <PERSON><PERSON>", "selectLane": "Selecionar Coluna", "initialTemplate": "<PERSON><PERSON> Inicial", "selectTemplate": "Selecione o modelo", "templatePreview": "Visualização do Modelo", "templateDescription": "Escolha um modelo predefinido para iniciar seu fluxo", "visibility": {"public": "Público", "private": "Privado", "shared": "Comp<PERSON><PERSON><PERSON><PERSON>", "publicDescription": "Todos os usuários podem ver e interagir com este fluxo", "privateDescription": "Apenas você pode ver e interagir com este fluxo", "sharedDescription": "Apenas você e os membros selecionados podem ver e interagir com este fluxo"}, "visibilityTooltip": "Define quem pode ver e interagir com este fluxo", "selectVisibility": "Selecione a visibilidade", "membersDescription": "Selecione os usuários que terão acesso a este fluxo", "searchMembers": "Buscar membros...", "tags": "Tags", "tagsDescription": "Adicione tags para categorizar e encontrar este fluxo facilmente", "addTag": "Adicionar tag...", "add": "<PERSON><PERSON><PERSON><PERSON>", "suggestedTags": "Tags sugeridas", "searchTags": "Buscar tags...", "noTagsFound": "Nenhuma tag encontrada", "createTag": "Criar tag", "createNew": "Criar nova", "typeToSearch": "Digite para buscar ou criar uma tag", "tagsHelpText": "Selecione tags existentes ou crie novas digitando", "selected": "Selecionado", "archive": "<PERSON><PERSON><PERSON><PERSON>", "unarchive": "Desar<PERSON><PERSON>", "totalItems": "Total de itens", "completedItems": "Itens concluídos", "conversionRate": "Taxa de conversão", "avgCycleTime": "Tempo médio de ciclo", "types": {"sales": "<PERSON><PERSON><PERSON>", "marketing": "Marketing", "support": "Suporte", "development": "Desenvolvimento", "onboarding": "Onboarding", "recruitment": "Recrutamento", "other": "Outro"}, "templates": {"empty": "<PERSON><PERSON><PERSON>", "basic": "Básico", "sales": "<PERSON><PERSON><PERSON>", "marketing": "Marketing", "support": "Suporte", "emptyDescription": "Comece com um fluxo vazio e adicione colunas conforme necessário", "backlog": "Backlog", "inProgress": "Em Progresso", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leads": "Leads", "qualification": "Qualificação", "proposal": "Proposta", "negotiation": "Negociação", "closed": "<PERSON><PERSON><PERSON>", "ideas": "<PERSON><PERSON><PERSON>", "planning": "Planejamento", "production": "Em Produção", "review": "<PERSON><PERSON><PERSON>", "published": "Publicado", "new": "Novo", "analyzing": "<PERSON>", "waitingClient": "Aguardando Cliente", "resolved": "Resolvido"}, "modes": {"attract": "Atração", "convert": "Conversão", "engage": "Engajamento", "close": "Fechamento", "delight": "Encantamento", "analyze": "<PERSON><PERSON><PERSON><PERSON>"}}, "people": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Gerencie pessoas e empresas", "noPeopleFound": "<PERSON>enhuma pessoa encontrada", "addPerson": "<PERSON><PERSON><PERSON><PERSON>", "createPerson": "<PERSON><PERSON><PERSON>", "createPersonDescription": "Preencha os campos abaixo para criar uma nova pessoa ou empresa", "editPerson": "<PERSON><PERSON>", "editPersonDescription": "Edite as informações da pessoa ou empresa", "personUpdated": "Pessoa atualizada com sucesso", "personCreated": "Pessoa criada com sucesso", "personDeleted": "Pessoa excluída com sucesso", "deleteConfirmation": "Tem certeza que deseja excluir {count, plural, one {esta pessoa} other {estas # pessoas}}?", "deleteWarning": "Esta ação não pode ser desfeita. Todos os dados associados serão excluídos permanentemente.", "filterByType": "Filtrar por tipo", "filterByStatus": "Filtrar por status", "person": "Pessoa Física", "company": "Pessoa <PERSON>í<PERSON>", "personType": "<PERSON><PERSON><PERSON> de Pessoa", "typeCannotBeChanged": "O tipo de pessoa não pode ser alterado após a criação", "enterName": "Digite o nome", "enterEmail": "Digite o e-mail", "enterPhone": "Digite o telefone", "statusDescription": "Pessoas inativas não aparecem em seleções e buscas por padrão", "contacts": "Contatos", "contactsDescription": "Informações de contato associadas a esta pessoa", "noContacts": "Nenhum contato adicionado", "addContact": "<PERSON><PERSON><PERSON><PERSON>", "contactType": "Tipo de Contato", "selectContactType": "Selecione o tipo de contato", "contactName": "Nome do Contato", "enterContactName": "Digite o nome do contato", "contactValue": "Valor do Contato", "enterContactValue": "Digite o valor do contato", "email": "E-mail", "phone": "Telefone", "whatsapp": "WhatsApp", "telegram": "Telegram", "documents": "Documentos", "documentsDescription": "Documentos associados a esta pessoa", "noDocuments": "Nenhum documento adicionado", "addDocument": "Adicionar <PERSON>", "documentName": "Nome do Documento", "enterDocumentName": "Digite o nome do documento", "documentValue": "Valor do Documento", "enterDocumentValue": "Digite o valor do documento", "addresses": "Endereços", "addressesDescription": "Endereços associados a esta pessoa", "noAddresses": "Nenhum endereço adicionado", "addAddress": "<PERSON><PERSON><PERSON><PERSON>", "addressName": "Nome do Endereço", "enterAddressName": "Digite o nome do endereço", "street": "<PERSON><PERSON>", "enterStreet": "Digite a rua", "number": "Número", "enterNumber": "Digite o número", "complement": "Complemento", "enterComplement": "Digite o complemento", "neighborhood": "Bairro", "enterNeighborhood": "Digite o bairro", "city": "Cidade", "enterCity": "Digite a cidade", "state": "Estado", "enterState": "Digite o estado", "postalCode": "CEP", "enterPostalCode": "Digite o CEP", "country": "<PERSON><PERSON>", "enterCountry": "Digite o país", "partners": "<PERSON><PERSON><PERSON><PERSON>", "partnersDescription": "Pessoas físicas associadas a esta empresa", "noPartners": "<PERSON><PERSON><PERSON> s<PERSON>cio adicionado", "addPartner": "<PERSON><PERSON><PERSON><PERSON>", "selectPartner": "Selecione um sócio"}, "agents": {"title": "<PERSON><PERSON>", "description": "Configure agentes autônomos para automatizar tarefas e interações", "noAgentsFound": "Nenhum agente encontrado", "newAgent": "Novo Agente", "createAgent": "<PERSON><PERSON><PERSON>", "createAgentDescription": "Preencha os campos abaixo para criar um novo agente autônomo", "editAgent": "<PERSON><PERSON>", "editAgentDescription": "<PERSON>e as configurações do agente autônomo", "agentUpdated": "Agente atualizado com sucesso", "agentCreated": "Agente criado com sucesso", "agentDeleted": "Agente excluído com sucesso", "deleteConfirmation": "Tem certeza que deseja excluir este agente?", "deleteWarning": "Esta ação não pode ser desfeita. Todos os dados associados serão excluídos permanentemente.", "enterName": "Digite o nome do agente", "enterDescription": "Digite uma descrição para o agente", "enterRule": "Digite uma regra para o agente", "statusDescription": "Agentes inativos não serão executados automaticamente", "actions": "Ações", "actionsDescription": "Ações que o agente pode executar", "actionsSelected": "{count} aç<PERSON>es se<PERSON>s", "selectActions": "Selecionar ações", "searchActions": "Buscar ações...", "noActions": "Nenhuma ação adicionada", "noActionsFound": "Nenhuma ação encontrada", "rules": "Regras", "rulesDescription": "Regras que o agente deve seguir", "rulesCount": "{count} regras definidas", "noRules": "Nenhuma regra adicionada", "model": "<PERSON><PERSON>", "modelDescription": "Modelo de IA a ser usado pelo agente", "selectModel": "Selecionar modelo", "template": "<PERSON><PERSON>", "templateDescription": "Iniciar com um modelo pré-configurado", "selectTemplate": "Selecionar modelo", "noDescription": "Sem descrição", "templates": {"empty": "<PERSON><PERSON><PERSON>", "customerService": "Assistente de Atendimento ao Cliente", "salesAssistant": "Assistente de Vendas", "dataAnalyst": "Analista de Dados", "contentCreator": "<PERSON><PERSON><PERSON>"}}, "automations": {"title": "Automações", "description": "Crie automações para otimizar seus processos e aumentar a produtividade", "noAutomationsFound": "Nenhuma automação encontrada", "newAutomation": "Nova Automação", "createAutomation": "Criar Automação", "createAutomationDescription": "Preencha os campos abaixo para criar uma nova automação", "editAutomation": "Editar Automação", "editAutomationDescription": "Edite as configurações da automação", "automationUpdated": "Automação atualizada com sucesso", "automationCreated": "Automação criada com sucesso", "automationDeleted": "Automação excluída com sucesso", "deleteConfirmation": "Tem certeza que deseja excluir esta automação?", "deleteWarning": "Esta ação não pode ser desfeita. Todos os dados associados serão excluídos permanentemente.", "enterName": "Digite o nome da automação", "enterDescription": "Digite uma descrição para a automação", "statusDescription": "Automações inativas não serão executadas", "trigger": "<PERSON><PERSON><PERSON><PERSON>", "triggers": {"recordCreated": "Quando um {model} for criado", "recordUpdated": "Quando um {model} for atualizado", "recordDeleted": "Quando um {model} for excluído", "fieldChanged": "Quando o campo {field} de um {model} for alterado", "statusChanged": "Quando o status de um {model} for alterado", "scheduled": "Agendado para {time} ({frequency})", "webhook": "Quando receber uma requisição em {endpoint}", "formSubmitted": "Quando o formulário {formId} for enviado", "unknown": "<PERSON><PERSON><PERSON><PERSON>he<PERSON>"}, "triggerTypes": {"record_created": "Criação de Registro", "record_updated": "Atualização de Registro", "record_deleted": "Exclusão de Registro", "field_changed": "Alteração de Campo", "status_changed": "Alteração de Status", "scheduled": "Agendado", "webhook": "Webhook", "form_submitted": "<PERSON><PERSON>"}, "actions": "Ações", "actionsDescription": "Ações que serão executadas quando o gatilho for acionado", "actionsCount": "{count} a<PERSON><PERSON><PERSON> configuradas", "noActions": "Nenhuma ação configurada", "conditions": "Condições", "conditionsDescription": "Condições que devem ser atendidas para que a automação seja executada", "conditionsCount": "{count} condiç<PERSON><PERSON> configuradas", "noConditions": "Nenhuma condição configurada", "noDescription": "Sem descrição", "pause": "Pausar", "activate": "Ativar", "implementationPending": "Implementação pendente - esta funcionalidade será implementada em breve"}, "actions": {"title": "Ações", "description": "Configure ações personalizadas para automatizar seus fluxos de trabalho", "noActionsFound": "Nenhuma ação encontrada", "newAction": "Nova Ação", "createAction": "Criar Ação", "createActionDescription": "Preencha os campos abaixo para criar uma nova ação personalizada", "editAction": "Editar <PERSON>", "editActionDescription": "<PERSON>e as configurações da ação personalizada", "actionUpdated": "Ação atualizada com sucesso", "actionCreated": "Ação criada com sucesso", "actionDeleted": "Ação excluída com sucesso", "deleteConfirmation": "Tem certeza que deseja excluir esta ação?", "deleteWarning": "Esta ação não pode ser desfeita. Todos os dados associados serão excluídos permanentemente.", "enterName": "Digite o nome da ação", "enterDescription": "Digite uma descrição para a ação", "statusDescription": "Ações inativas não estarão disponíveis para uso em automações", "type": "Tipo", "category": "Categoria", "filterByType": "Filtrar por tipo", "filterByCategory": "Filtrar por categoria", "noDescription": "Sem descrição", "implementationPending": "Implementação pendente - esta funcionalidade será implementada em breve", "types": {"email": "E-mail", "notification": "Notificação", "webhook": "Webhook", "api_call": "Chamada de API", "database": "Banco de Dados", "file": "Arquivo", "integration": "Integração", "custom": "Personalizado"}, "categories": {"communication": "Comunicação", "data_processing": "Processamento de Dados", "integration": "Integração", "notification": "Notificação", "automation": "Automação", "reporting": "Relatórios", "utility": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "languages": {"pt-BR": "Português", "en": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "es": "Espanhol"}, "auth": {"login": "Entrar", "loginDescription": "Entre com sua conta para acessar o sistema", "withPassword": "<PERSON><PERSON> senha", "withMagicLink": "Com magic link", "email": "E-mail", "emailPlaceholder": "Digite seu e-mail", "password": "<PERSON><PERSON>", "passwordPlaceholder": "Digite sua senha", "forgotPassword": "Esqueceu sua senha?", "sendMagicLink": "Enviar magic link", "magicLinkDescription": "Enviaremos um link de acesso para seu e-mail", "orContinueWith": "ou continue com", "continueWithGoogle": "Continuar com Google", "noAccount": "Não tem uma conta?", "contactAdmin": "Entre em contato com o administrador", "error": "Erro", "checkYourEmail": "Verifique seu e-mail", "magicLinkSentDescription": "Enviamos um link de acesso para seu e-mail", "magicLinkSentInstructions": "Clique no link enviado para seu e-mail para fazer login. O link expira em 15 minutos.", "resetPasswordSentDescription": "Enviamos um link para redefinir sua senha", "resetPasswordSentInstructions": "Clique no link enviado para seu e-mail para redefinir sua senha. O link expira em 1 hora.", "backToLogin": "Voltar para o login", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "resetPasswordDescription": "Crie uma nova senha para sua conta", "newPassword": "Nova senha", "newPasswordPlaceholder": "Digite sua nova senha", "confirmPassword": "Confirmar <PERSON><PERSON><PERSON>", "confirmPasswordPlaceholder": "Digite novamente sua nova senha", "passwordStrength": "Força da senha", "passwordStrengths": {"veryWeak": "<PERSON><PERSON> fraca", "weak": "Fraca", "fair": "<PERSON><PERSON><PERSON><PERSON>", "good": "Bo<PERSON>", "strong": "Forte"}, "passwordRequirements": {"length": "Pelo menos 8 caracteres", "uppercase": "<PERSON>elo menos uma letra mai<PERSON>", "lowercase": "<PERSON>elo menos uma letra minúscula", "number": "Pelo menos um número", "special": "<PERSON>elo menos um caractere especial"}, "passwordsDoNotMatch": "As senhas não coincidem", "passwordTooWeak": "Sua senha é muito fraca. Atenda a todos os requisitos de segurança.", "passwordResetSuccess": "Senha redefinida com sucesso", "passwordResetSuccessDescription": "Sua senha foi redefinida com sucesso. Você pode fazer login agora.", "twoFactorVerification": "Verificação de dois fatores", "twoFactorVerificationDescription": "Digite o código de verificação enviado para seu {method}", "twoFactorMethods": {"app": "aplicativo autenticador", "sms": "telefone", "email": "e-mail"}, "verificationCode": "Código de verificação", "verificationCodePlaceholder": "Digite o código de 6 dígitos", "enterCodeFromApp": "Digite o código de 6 dígitos do seu aplicativo autenticador", "enterCodeFromSMS": "Digite o código de 6 dígitos enviado para seu telefone", "enterCodeFromEmail": "Digite o código de 6 dígitos enviado para seu e-mail", "verify": "Verificar", "resendCode": "Reenviar código", "resendCodeIn": "<PERSON>en<PERSON>r código em {seconds}s", "verifyingMagicLink": "Verificando magic link", "verifyingMagicLinkDescription": "Estamos verificando seu magic link", "verifyingMagicLinkWait": "Por favor, aguarde enquanto verificamos seu magic link", "redirecting": "Redirecionando...", "invalidOrExpiredMagicLink": "<PERSON> in<PERSON><PERSON><PERSON>o ou expirado", "invitation": "<PERSON><PERSON><PERSON>", "acceptInvitation": "Aceitar convite", "acceptInvitationDescription": "Você foi convidado para se juntar à plataforma com o e-mail {email}", "name": "Nome", "namePlaceholder": "Digite seu nome completo", "createAccount": "C<PERSON><PERSON> conta", "invalidOrExpiredInvitation": "Con<PERSON>te in<PERSON> ou expirado", "errorLoadingInvitation": "Erro ao carregar o convite"}, "inbox": {"inbox": "Caixa de Entrada", "allConversations": "<PERSON><PERSON> as conversas", "byStatus": "Por status", "byChannel": "Por canal", "statusTypes": {"new": "Novo", "open": "Abe<PERSON>o", "pending": "Pendente", "resolved": "Resolvido", "archived": "Arquivado"}, "channelTypes": {"whatsapp": "WhatsApp", "email": "Email", "chat": "Cha<PERSON>", "telegram": "Telegram", "sms": "SMS", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "linkedin": "LinkedIn", "google_business": "Google Business", "internal": "Interno", "voip": "Telefone"}, "priorityLevels": {"low": "Baixa", "medium": "Média", "high": "Alta", "urgent": "Urgente"}, "operatorStatus": {"available": "Disponível", "busy": "Ocupado", "away": "Ausente", "offline": "Offline"}, "callStatus": {"ringing": "<PERSON><PERSON><PERSON>", "in_progress": "Em andamento", "completed": "Conclu<PERSON><PERSON>", "missed": "Perdida", "busy": "Ocupado", "failed": "Fal<PERSON>", "voicemail": "Caixa postal", "canceled": "Cancelada"}, "filters": "<PERSON><PERSON><PERSON>", "filterBy": "Filtrar por", "status": "Status", "channels": "Canais", "priority": "Prioridade", "assignedTo": "Atribuído a", "me": "Eu", "unassigned": "Não atribuído", "clearFilters": "Limpar filtros", "listView": "Visualização em lista", "splitView": "Visualização dividida", "setStatus": "Definir status", "new": "Novo", "newConversation": "Nova conversa", "newCall": "Nova chamada", "newInternalGroup": "Novo grupo interno", "noConversations": "Nenhuma conversa encontrada", "noConversationsDescription": "Não há conversas que correspondam aos seus filtros ou você ainda não iniciou nenhuma conversa.", "noActiveConversation": "Nenhuma conversa selecionada", "selectConversation": "Selecione uma conversa da lista para visualizar os detalhes.", "selectConversationOrStart": "Selecione uma conversa da lista ou inicie uma nova conversa.", "date": "Data", "unknownContact": "Contato desconhecido", "conversationActions": "Ações da conversa", "markAsOpen": "Marcar como aberto", "markAsPending": "Marcar como pendente", "markAsResolved": "Marcar como resolvido", "archive": "<PERSON><PERSON><PERSON><PERSON>", "setPriority": "Definir prioridade", "assign": "Atribuir", "assignToMe": "Atribuir a mim", "messages": "Mensagens", "calls": "<PERSON><PERSON><PERSON>", "info": "Informações", "typeMessage": "Digite uma mensagem...", "addPrivateNote": "Adicionar nota privada...", "send": "Enviar", "addNote": "<PERSON><PERSON><PERSON><PERSON> nota", "cancelNote": "<PERSON><PERSON><PERSON> nota", "pressEnterToSend": "Pressione Ctrl+Enter para enviar", "templates": "Modelos", "quickReplies": "Respostas <PERSON>", "noTemplates": "Nenhum modelo encontrado", "attachFiles": "Anexar arquivos", "privateNote": "Nota privada", "contactInfo": "Informações de contato", "at": "em", "tags": "Tags", "viewInCrm": "Ver no CRM", "groupInfo": "Informações do grupo", "participants": "participantes", "conversationDetails": "Detalhes da conversa", "channel": "Canal", "created": "<PERSON><PERSON><PERSON>", "lastUpdated": "Última atualização", "searchConversations": "Pesquisar conversas...", "selectChannel": "Selecionar canal", "selectContact": "Selecionar contato", "searchContacts": "Pesquisar contatos...", "noContactsFound": "Nenhum contato encontrado", "subject": "<PERSON><PERSON><PERSON>", "subjectPlaceholder": "Digite um assunto para a conversa", "initialMessage": "Mensagem inicial", "initialMessagePlaceholder": "Digite uma mensagem inicial...", "startConversation": "Iniciar conversa", "newCallDescription": "Inicie uma chamada telefônica com um contato", "phoneNumber": "Número de telefone", "phoneNumberPlaceholder": "Digite o número de telefone", "startCall": "Iniciar chama<PERSON>", "call": "Ligar para", "noCalls": "<PERSON><PERSON><PERSON><PERSON> chamada encontrada", "noCallsDescription": "Não há chamadas registradas para esta conversa.", "calling": "Chamand<PERSON>...", "callNotes": "<PERSON><PERSON> da chamada", "callNotesPlaceholder": "Adicione notas sobre esta chamada...", "playRecording": "Reproduzir gravação", "newInternalGroupDescription": "Crie um grupo de comunicação interna com outros operadores", "groupSubject": "Assunto do grupo", "groupSubjectPlaceholder": "Digite um assunto para o grupo", "addParticipants": "Adicionar participantes", "searchUsers": "Pesquisar usuá<PERSON>...", "noUsersFound": "Nenhum usuário encontrado", "createGroup": "Criar grupo", "userName": "{{name}}"}, "errors": {"notFound": {"title": "Página não encontrada", "description": "<PERSON><PERSON><PERSON><PERSON>, a página que você está procurando não existe ou foi movida.", "backHome": "Voltar para a página inicial", "search": "Buscar no site"}, "accessDenied": {"title": "<PERSON><PERSON>", "description": "Você não tem permissão para acessar esta página. Entre em contato com o administrador se acredita que isso é um erro.", "backHome": "Voltar para a página inicial", "login": "Fazer login"}, "generic": {"title": "<PERSON>go deu errado", "description": "Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente mais tarde.", "backHome": "Voltar para a página inicial", "tryAgain": "Tentar novamente"}}, "profile": {"title": "Perfil", "description": "Gerencie suas informações pessoais e configurações de conta", "personalInfo": "Informaçõ<PERSON>", "personalInfoDescription": "Atualize suas informações pessoais e preferências", "security": "Segurança", "connections": "Conexões", "data": "<PERSON><PERSON>", "errorLoadingProfile": "Erro ao carregar o perfil", "name": "Nome", "namePlaceholder": "Digite seu nome completo", "email": "E-mail", "emailPlaceholder": "Digite seu e-mail", "phone": "Telefone", "phonePlaceholder": "Digite seu telefone", "timezone": "<PERSON><PERSON>", "selectTimezone": "Selecione seu fuso horário", "language": "Idioma", "selectLanguage": "Selecione seu idioma", "theme": "<PERSON><PERSON>", "selectTheme": "Selecione seu tema", "profilePicture": "Foto de perfil", "profilePictureDescription": "Esta foto será exibida em seu perfil e em comentários", "uploadPicture": "Carregar foto", "removePicture": "Remover foto", "notificationPreferences": "Preferências de Notificação", "notificationPreferencesDescription": "Gerencie como e quando você recebe notificações", "emailNotifications": "Notificações por e-mail", "emailNotificationsDescription": "Receba notificações importantes por e-mail", "pushNotifications": "Notificações push", "pushNotificationsDescription": "Receba notificações em tempo real no seu navegador", "inAppNotifications": "Notificações no aplicativo", "inAppNotificationsDescription": "Veja notificações dentro do aplicativo", "marketingNotifications": "Comunicações de marketing", "marketingNotificationsDescription": "Receba novidades, dicas e ofertas especiais", "digestFrequency": "Frequência do resumo", "selectDigestFrequency": "Selecione a frequência do resumo", "digestFrequencyDescription": "Receba um resumo das atividades importantes", "digestFrequencies": {"daily": "<PERSON><PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON>", "never": "Nunca"}, "changePassword": "<PERSON><PERSON><PERSON>", "changePasswordDescription": "Atualize sua senha para manter sua conta segura", "currentPassword": "<PERSON><PERSON> atual", "currentPasswordPlaceholder": "Digite sua senha atual", "newPassword": "Nova senha", "newPasswordPlaceholder": "Digite sua nova senha", "confirmPassword": "Confirmar <PERSON><PERSON><PERSON>", "confirmPasswordPlaceholder": "Digite novamente sua nova senha", "passwordRequirements": "A senha deve ter pelo menos 8 caracteres e incluir letras, números e caracteres especiais", "updatePassword": "<PERSON><PERSON><PERSON><PERSON>", "passwordsDoNotMatch": "As senhas não coincidem", "passwordTooShort": "A senha deve ter pelo menos 8 caracteres", "passwordChangeError": "Erro ao alterar a senha. Verifique sua senha atual e tente novamente", "error": "Erro", "twoFactorAuth": "Autenticação de Dois Fatores", "twoFactorAuthDescription": "Adicione uma camada extra de segurança à sua conta", "twoFactorEnabled": "Autenticação de dois fatores ativada", "twoFactorEnabledDescription": "Sua conta está protegida com autenticação de dois fatores usando {method}", "twoFactorDisabled": "Autenticação de dois fatores desativada", "twoFactorDisabledDescription": "Ative a autenticação de dois fatores para aumentar a segurança da sua conta", "selectMethod": "Selecione um método de autenticação", "twoFactorMethods": {"app": "Aplicativo autenticador", "sms": "SMS", "email": "E-mail"}, "twoFactorMethodDescriptions": {"app": "Use um aplicativo como Google Authenticator ou Authy", "sms": "Receba códigos de verificação por SMS", "email": "Receba códigos de verificação por e-mail"}, "phoneNumberRequired": "Você precisa adicionar um número de telefone para usar este método", "setup2FA": "Configurar autenticação de dois fatores", "setup2FADescription": {"app": "Escaneie o código QR abaixo com seu aplicativo autenticador ou insira o código manualmente", "sms": "Um código de verificação foi enviado para seu telefone", "email": "Um código de verificação foi enviado para seu e-mail"}, "manualEntry": "Entrada manual", "manualEntryDescription": "Se não conseguir escanear o código QR, insira este código no seu aplicativo autenticador", "codeSent": "Código enviado", "codeSentToPhone": "Um código de verificação foi enviado para {phone}", "codeSentToEmail": "Um código de verificação foi enviado para {email}", "verificationCode": "Código de verificação", "verificationCodePlaceholder": "Digite o código de 6 dígitos", "verify": "Verificar", "backupCodes": "Códigos de backup", "backupCodesDescription": "Guarde estes códigos em um lugar seguro. Você pode usá-los para acessar sua conta caso perca acesso ao seu método de autenticação de dois fatores.", "backupCodesWarning": "Cada código pode ser usado apenas uma vez. Guarde-os em um lugar seguro.", "viewBackupCodes": "Ver códigos de backup", "copyBackupCodes": "<PERSON><PERSON><PERSON>", "downloadBackupCodes": "Baixar <PERSON>", "disable2FA": "Desativar autenticação de dois fatores", "disable2FAConfirmation": "Desativar autenticação de dois fatores?", "disable2FAWarning": "<PERSON><PERSON> reduzirá a segurança da sua conta. Tem certeza que deseja continuar?", "connectedAccounts": "Contas Conectadas", "connectedAccountsDescription": "Conecte suas contas de redes sociais para login mais rápido", "googleAccount": "Conta do Google", "googleConnected": "Conectado com o Google", "googleNotConnected": "Não conectado com o Google", "connect": "Conectar", "disconnect": "Desconectar", "warning": "Aviso", "disconnectGoogleWarning": "Se você desconectar sua conta do Google enquanto a autenticação de dois fatores estiver ativada, você precisará usar seu método de autenticação de dois fatores para fazer login", "disconnectGoogleConfirmation": "Desconectar conta do Google?", "disconnectGoogleDescription": "Você não poderá mais usar sua conta do Google para fazer login", "securityActivity": "Atividade de Segurança", "securityActivityDescription": "Monitore a atividade recente da sua conta", "loginHistory": "Hist<PERSON><PERSON><PERSON> de login", "connectedDevices": "Dispositivos conectados", "successfulLogin": "<PERSON>gin bem-sucedido", "failedLogin": "Tentativa de login falhou", "noLoginHistory": "Nenhum histórico de login disponível", "noConnectedDevices": "Nenhum dispositivo conectado", "currentDevice": "Dispositivo atual", "lastActive": "Última atividade", "revokeAccess": "<PERSON><PERSON><PERSON>", "revokeDeviceConfirmation": "Revogar acesso do dispositivo?", "revokeDeviceDescription": "O dispositivo {device} será desconectado da sua conta. Você precisará fazer login novamente neste dispositivo.", "dataManagement": "Gerenciamento de Dados", "dataManagementDescription": "<PERSON><PERSON><PERSON><PERSON> seus dados pessoais", "exportData": "Exportar dados", "exportDataDescription": "Baixe uma cópia dos seus dados pessoais em formato JSON", "downloadData": "Baixar meus dados", "deleteAccount": "Excluir conta", "deleteAccountDescription": "Exclua permanentemente sua conta e todos os dados associados", "deleteAccountConfirmation": "Excluir conta permanentemente?", "deleteAccountWarning": "Esta ação não pode ser desfeita. Todos os seus dados serão excluídos permanentemente.", "deleteAccountPermanent": "A exclusão da conta é permanente e não pode ser desfeita. Todos os seus dados, incluindo perfil, histórico e configurações, serão excluídos permanentemente.", "typeToConfirm": "Digite {text} para confirmar", "confirmDelete": "Confirmar exclusão"}, "myPlan": {"title": "<PERSON><PERSON>", "description": "Gerencie seu plano de assinatura e usuários", "overview": "Visão Geral", "users": "Usuários", "billing": "Faturamento", "changePlan": "<PERSON><PERSON> de <PERSON>o", "changePlanDescription": "Compare os planos disponíveis e escolha o que melhor atende às suas necessidades", "price": "Preço", "nextBilling": "Próxima <PERSON>", "expires": "Expira em", "updatePaymentMethod": "Atualizar m<PERSON>agamento", "includedFeatures": "Recursos incluídos", "autoRenew": "Renovação automática", "autoRenewEnabled": "Sua assinatura será renovada automaticamente", "autoRenewDisabled": "Sua assinatura não será renovada automaticamente", "enableAutoRenew": "Ativar renovação automática", "disableAutoRenew": "Desativar renovação automática", "cancelSubscription": "Cancelar assinatura", "usageOverview": "Uso de Recursos", "usageOverviewDescription": "Acompanhe o uso dos recursos do seu plano", "criticalResourcesWarning": "Recursos em nível crítico", "criticalResourcesDescription": "Alguns recursos estão próximos do limite. Considere fazer um upgrade do seu plano.", "usersManagement": "Gerenciamento de Usuários", "usersCount": "{count} de {max} usu<PERSON><PERSON>s", "inviteUser": "<PERSON><PERSON><PERSON>", "inviteUserDescription": "Convide um novo usuário para sua conta", "userName": "Nome do usuário", "userNamePlaceholder": "Digite o nome do usuário", "userEmail": "E-mail do usuário", "userEmailPlaceholder": "Digite o e-mail do usuário", "userRole": "Função do usuário", "selectRole": "Selecione a função", "sendInvite": "Enviar convite", "resendInvite": "Reenviar convite", "changeRole": "Alterar função", "changeRoleDescription": "Alterar a função de {name}", "removeUser": "Remover usuário", "removeUserConfirmation": "Remover usuário?", "removeUserWarning": "Tem certeza que deseja remover {name} da sua conta? Esta ação não pode ser desfeita.", "invoicesHistory": "Histórico de Faturas", "invoicesHistoryDescription": "Visualize e baixe suas faturas anteriores", "invoice": "<PERSON><PERSON>", "issuedOn": "Emitida em", "downloadInvoice": "Baixar fatura", "noInvoices": "<PERSON>enhuma fatura encontrada", "popular": "Popular", "month": "mês", "year": "ano", "free": "<PERSON><PERSON><PERSON><PERSON>", "contactUs": "Consulte-nos", "enterprise": "Enterprise", "enterpriseDescription": "Para grandes empresas com necessidades específicas", "contactSales": "Fale com vendas", "selectPlan": "Selecionar plano", "currentPlan": "Plano atual", "confirmPlanChange": "Confirmar <PERSON> de plano?", "confirmPlanChangeDescription": "Tem certeza que deseja mudar de plano? Sua assinatura atual será alterada imediatamente.", "confirmChange": "Confi<PERSON><PERSON>", "savePercent": "Economize {percent}%", "noSubscription": "Você não possui uma assinatura ativa", "enterpriseFeatures": {"users": "Usuários ilimitados", "storage": "Armazenamento ilimitado", "support": "Suporte dedicado", "sla": "SLA garantido", "integrations": "Integrações personalizadas"}, "resources": {"users": "Usuários", "flows": "Fluxos", "records": "Registros", "storage": "Armazenamento", "landingPages": "Landing Pages", "forms": "Formulários", "automations": "Automações"}, "roles": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "admin": "Administrador", "manager": "<PERSON><PERSON><PERSON>", "member": "Membro", "viewer": "Visualizador"}, "userStatuses": {"active": "Ativo", "invited": "Convidado", "suspended": "Suspenso"}, "statuses": {"active": "Ativo", "trialing": "Em teste", "past_due": "<PERSON><PERSON><PERSON> at<PERSON>", "canceled": "Cancelado", "unpaid": "Não pago", "expired": "<PERSON><PERSON><PERSON>"}, "invoiceStatuses": {"paid": "Pago", "unpaid": "Não pago", "void": "Cancelado", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "billingCycles": {"monthly": "Mensal", "yearly": "<PERSON><PERSON>", "custom": "Personalizado"}, "paymentMethods": {"credit_card": "Cartão de crédito", "bank_account": "Conta bancária", "paypal": "PayPal", "other": "Outro"}}, "landingPages": {"title": "Landing Pages e Formulários", "description": "Crie e gerencie landing pages e formulários personalizados para captura de leads", "landingPages": "Landing Pages", "noLandingPagesFound": "Nenhuma landing page encontrada", "newLandingPage": "Nova Landing Page", "createLandingPage": "C<PERSON>r Landing Page", "createLandingPageDescription": "Preencha os campos abaixo para criar uma nova landing page", "editLandingPage": "<PERSON><PERSON>", "editLandingPageDescription": "<PERSON><PERSON> as configura<PERSON><PERSON><PERSON> da landing page", "landingPageUpdated": "Landing page atualizada com sucesso", "landingPageCreated": "Landing page criada com sucesso", "landingPageDeleted": "Landing page excluída com sucesso", "deleteConfirmation": "Tem certeza que deseja excluir esta landing page?", "deleteWarning": "Esta ação não pode ser desfeita. Todos os dados associados serão excluídos permanentemente.", "enterName": "Digite o nome da landing page", "enterDescription": "Digite uma descrição para a landing page", "enterSlug": "Digite o slug da landing page", "statusDescription": "Landing pages inativas não estarão disponíveis para acesso", "url": "URL", "blocks": "Blocos", "blocksCount": "{count} blocos configurados", "noBlocks": "Nenhum bloco adicionado", "lane": "Coluna", "linkedToLane": "Vinculada a uma coluna", "laneTooltip": "Esta landing page está vinculada a uma coluna e criará registros quando for submetida", "preview": "Visualizar", "copyUrl": "Copiar URL", "urlCopied": "URL copiada para a área de transferência", "filterByStatus": "Filtrar por status", "noDescription": "Sem descrição", "implementationPending": "Implementação pendente - esta funcionalidade será implementada em breve", "customTemplate": "Personalizado", "conversionModes": {"lead": "Lead", "service": "Serviço", "product": "Produ<PERSON>", "subscribe": "Inscrição", "schedule": "Agendamento"}, "templates": {"blank": "Em Branco", "product": "Página de Produto", "capture": "Página de Captura", "event": "Página de Evento", "contact": "Página de Contato", "thank-you": "Página de Agradecimento"}}, "forms": {"title": "Formulários", "forms": "Formulários", "noFormsFound": "<PERSON>en<PERSON> formul<PERSON> encontrado", "newForm": "Novo Formulário", "createForm": "<PERSON><PERSON><PERSON>", "createFormDescription": "Preencha os campos abaixo para criar um novo formulário", "editForm": "<PERSON><PERSON>", "editFormDescription": "Edite as configurações do formulário", "formUpdated": "Formulário atualizado com sucesso", "formCreated": "Formulário criado com sucesso", "formDeleted": "Formulário excluído com sucesso", "deleteConfirmation": "Tem certeza que deseja excluir este formulário?", "deleteWarning": "Esta ação não pode ser desfeita. Todos os dados associados serão excluídos permanentemente.", "enterName": "Digite o nome do formulário", "enterDescription": "Digite uma descrição para o formulário", "enterSlug": "Digite o slug do formulário", "statusDescription": "Formulários inativos não estarão disponíveis para acesso", "url": "URL", "fields": "Campos", "fieldsCount": "{count} campos configurados", "noFields": "Nenhum campo adicionado", "lane": "Coluna", "linkedToLane": "Vinculado a uma coluna", "laneTooltip": "Este formulário está vinculado a uma coluna e criará registros quando for submetido", "preview": "Visualizar", "copyUrl": "Copiar URL", "urlCopied": "URL copiada para a área de transferência", "filterByStatus": "Filtrar por status", "noDescription": "Sem descrição", "implementationPending": "Implementação pendente - esta funcionalidade será implementada em breve", "customTemplate": "Personalizado", "conversionModes": {"lead": "Lead", "service": "Serviço", "product": "Produ<PERSON>", "subscribe": "Inscrição", "schedule": "Agendamento"}, "templates": {"blank": "Em Branco", "contact": "Formulário de Contato", "registration": "Formulário de Inscrição", "survey": "Formulário de Pesquisa", "feedback": "<PERSON><PERSON><PERSON><PERSON>", "order": "Formulário de Pedido"}}, "dashboards": {"title": "Dashboards", "description": "Visualize métricas e indicadores de desempenho", "selectDashboard": "Selecionar dashboard", "noDashboardSelected": "Nenhum dashboard selecionado", "generalDashboard": "Dashboard Geral", "flowDashboard": "Dashboard do Fluxo", "metrics": {"totalLeads": "Total de Leads", "conversionRate": "Taxa de Conversão", "averageTicket": "Ticket <PERSON>", "revenue": "<PERSON><PERSON><PERSON>", "orders": "Pedidos", "customers": "Clientes", "cac": "CAC", "ltv": "LTV", "roi": "ROI"}, "periods": {"today": "Hoje", "yesterday": "Ontem", "thisWeek": "<PERSON><PERSON>", "lastWeek": "Semana Passada", "thisMonth": "<PERSON><PERSON>", "lastMonth": "<PERSON><PERSON><PERSON>", "thisYear": "<PERSON><PERSON>", "lastYear": "Ano Passado", "custom": "Personalizado"}, "widgets": {"leadsPerSource": "Leads por <PERSON><PERSON>m", "conversionsPerMonth": "Conversões por Mês", "salesFunnel": "<PERSON><PERSON>", "topFlows": "Principais <PERSON>", "leadsPerSegment": "Leads por Segmento", "salesPerMonth": "V<PERSON><PERSON> por <PERSON>", "salesFunnelB2B": "Funil de Vendas B2B", "topOpportunities": "Principais Oportunidades", "leadsPerChannel": "Leads por Canal", "campaignPerformance": "<PERSON><PERSON><PERSON><PERSON>", "trafficPerDevice": "Tráfego por Dispositivo", "engagementMetrics": "Métricas de Engajamento", "totalRevenue": "Receita Total", "salesPerPeriod": "Vendas por Período", "topProducts": "<PERSON><PERSON><PERSON>", "cartAbandonmentRate": "Taxa de Abandono de Carrinho", "paymentMethods": "Métodos de Pagamento"}}, "calendar": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Descrição", "newEvent": "Novo Evento", "createEvent": "<PERSON><PERSON><PERSON>", "createEventDescription": "Preencha os campos abaixo para criar um novo evento", "editEvent": "<PERSON><PERSON>", "editEventDescription": "Edite as informações do evento", "deleteConfirmation": "Tem certeza que deseja excluir este evento?", "deleteWarning": "Esta ação não pode ser desfeita. O evento será excluído permanentemente.", "selectDate": "Selecionar data", "selectView": "Selecionar visualização", "noEventsForDate": "Nenhum evento para esta data", "allDay": "<PERSON>a inteiro", "allDayDescription": "Este evento dura o dia todo", "titlePlaceholder": "Digite o título do evento", "titleRequired": "O título é obrigatório", "descriptionPlaceholder": "Digite uma descrição para o evento", "startDate": "Data de início", "endDate": "Data de término", "endDateMustBeAfterStart": "A data de término deve ser posterior à data de início", "location": "Local", "locationPlaceholder": "Digite o local do evento", "url": "URL", "urlPlaceholder": "Digite a URL do evento", "type": "Tipo", "selectType": "Selecione o tipo", "status": "Status", "selectStatus": "Selecione o status", "priority": "Prioridade", "selectPriority": "Selecione a prioridade", "dateAndTime": "Data e Hora", "attendees": "Participantes", "relatedFlow": "Fluxo Relacionado", "relatedRecord": "Registro Relacionado", "views": {"month": "<PERSON><PERSON><PERSON>", "week": "Se<PERSON>", "day": "<PERSON>a", "agenda": "Agenda"}, "types": {"meeting": "Reunião", "call": "Ligação", "task": "<PERSON><PERSON><PERSON>", "reminder": "Le<PERSON><PERSON>", "deadline": "Prazo", "appointment": "Compromisso", "other": "Outro"}, "statuses": {"scheduled": "Agendado", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "Cancelado", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pending": "Pendente"}, "priorities": {"high": "Alta", "medium": "Média", "low": "Baixa"}, "attendeeStatuses": {"accepted": "Aceitou", "declined": "<PERSON><PERSON><PERSON>", "tentative": "<PERSON><PERSON>z", "pending": "Pendente"}}}