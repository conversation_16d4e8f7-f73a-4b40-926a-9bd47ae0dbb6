{"common": {"search": "Search", "searchPlaceholder": "Search everything...", "noResults": "No results found.", "view": "View", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "save": "Save", "create": "Create", "actions": "Actions", "profile": "Profile", "logout": "Logout", "days": "days", "updated": "Updated", "comingSoon": "Coming Soon", "underDevelopmentDescription": "This feature is under development and will be available soon.", "language": "Language", "selectLanguage": "Select language", "theme": "Theme", "toggleTheme": "Toggle theme", "selectTheme": "Select theme", "title": "Title", "titlePlaceholder": "Enter item title", "description": "Description", "descriptionPlaceholder": "Enter a description for the item", "priority": "Priority", "high": "High", "medium": "Medium", "low": "Low", "selectPriority": "Select priority", "column": "Column", "selectColumn": "Select column", "assignee": "Assignee", "selectAssignee": "Select assignee", "unassigned": "Unassigned", "dueDate": "Due date", "selectDate": "Select date", "progress": "Progress", "tags": "Tags", "addTag": "Add tag", "suggestedTags": "Suggested tags", "newItem": "New Item", "createItemDescription": "Fill in the fields below to create a new item", "done": "Done", "moveTo": "Move to", "move": "Move", "addItem": "Add Item", "allColumns": "All columns", "notSet": "Not set", "notAvailable": "Not available", "createdOn": "Created on", "lastUpdated": "Last updated", "comments": "Comments", "noComments": "No comments", "activity": "Activity", "itemCreated": "Item created", "close": "Close", "stats": "Stats", "filters": "Filters", "filterItems": "Filter items", "reset": "Reset", "apply": "Apply", "clearAll": "Clear all", "requiredField": "Required field", "allDates": "All dates", "today": "Today", "last7Days": "Last 7 days", "last30Days": "Last 30 days", "thisMonth": "This month", "thisYear": "This year", "customPeriod": "Custom period", "startDate": "Start date", "endDate": "End date", "unknown": "Unknown", "newColumn": "New Column", "createColumnDescription": "Fill in the fields below to create a new column", "columnTitlePlaceholder": "Enter column title", "columnDescriptionPlaceholder": "Enter a description for the column", "wipLimit": "Work In Progress (WIP) Limit", "wipLimitPlaceholder": "Enter WIP limit", "wipLimitDescription": "Maximum number of items allowed in this column (optional)", "position": "Position", "selectPosition": "Select position", "positionStart": "At the beginning", "positionEnd": "At the end", "positionAfter": "After a column", "afterColumn": "After column", "columnCreated": "Column created successfully!", "allowItemCreation": "Allow item creation", "allowItemCreationDescription": "If checked, users will be able to create new items directly in this column", "movementRestriction": "Movement restriction", "selectRestriction": "Select restriction", "restrictionNone": "No restrictions", "restrictionOnlyLeft": "Only to the left", "restrictionOnlyRight": "Only to the right", "movementRestrictionDescription": "Defines where items from this column can be moved to", "laneModel": "Column model", "selectModel": "Select model", "laneModelDescription": "Defines the type/purpose of this column in the workflow", "modelDefault": "<PERSON><PERSON><PERSON>", "modelBacklog": "Backlog", "modelNew": "New", "modelNurture": "Nurture", "modelPotential": "Potential", "modelOpportunity": "Opportunity", "modelDone": "Done", "modelLost": "Lost", "modelWon": "Won", "modelPending": "Pending", "modelAttention": "Attention"}, "themes": {"light": "Light", "dark": "Dark", "system": "System"}, "header": {"search": "Search everything...", "notifications": "Notifications", "markAllAsRead": "Mark all as read", "noNotifications": "No notifications", "viewAllNotifications": "View all notifications", "inbox": "Inbox", "calendar": "Calendar"}, "search": {"flows": "Flows", "people": "People", "documents": "Documents", "messages": "Messages", "events": "Events", "from": "From"}, "notifications": {"newMessage": "New message", "reminder": "Reminder", "update": "Update", "newUser": "New user", "systemUpdate": "System update"}, "flows": {"title": "Flows", "description": "Manage your workflows", "noFlows": "No flows found", "createFlow": "Create Flow", "newFlow": "New Flow", "createFlowDescription": "Fill in the fields below to create a new workflow", "editFlowDescription": "Edit the workflow information", "flowUpdated": "Flow updated successfully", "flowTitlePlaceholder": "Enter flow title", "flowDescriptionPlaceholder": "Describe the purpose of this workflow", "viewFlow": "View Flow", "editFlow": "Edit Flow", "deleteFlow": "Delete Flow", "flowDeleted": "Flow deleted successfully", "flowCreated": "Flow created successfully", "confirmDeleteFlow": "Are you sure you want to delete this flow?", "flowDetails": "Flow Details", "flowName": "Flow Name", "flowType": "Flow Type", "selectFlowType": "Select flow type", "typeDescription": "Categorizes the flow according to its main purpose", "typeCannotBeChanged": "Flow type cannot be changed after creation", "modeCannotBeChanged": "Operation mode cannot be changed after creation", "templateCannotBeChanged": "Initial template cannot be changed after creation", "flowMode": "Operation Mode", "selectFlowMode": "Select operation mode", "modeDescription": "Defines the operation mode of this flow in the process", "flowCreatedAt": "Created at", "flowUpdatedAt": "Updated at", "flowCreatedBy": "Created by", "flowStatus": "Status", "flowActions": "Actions", "addItem": "Add Item", "editLanes": "<PERSON>", "wipLimit": "WIP Limit", "showDetails": "Show details", "hideDetails": "Hide details", "noDescription": "No description", "items": "items", "details": "Details", "noMembers": "No members", "members": "Members", "archived": "Archived", "addLane": "Add Column", "kanbanView": "Kanban View", "listView": "List View", "filterItems": "Filter Items", "noItems": "No items found", "itemDetails": "<PERSON><PERSON>", "itemCreated": "Item created successfully", "itemUpdated": "Item updated successfully", "itemDeleted": "Item deleted successfully", "confirmDeleteItem": "Are you sure you want to delete this item?", "moveItem": "Move Item", "selectLane": "Select Column", "initialTemplate": "Initial Template", "selectTemplate": "Select template", "templatePreview": "Template Preview", "templateDescription": "Choose a predefined template to start your flow", "visibility": {"public": "Public", "private": "Private", "shared": "Shared", "publicDescription": "All users can see and interact with this flow", "privateDescription": "Only you can see and interact with this flow", "sharedDescription": "Only you and selected members can see and interact with this flow"}, "visibilityTooltip": "Defines who can see and interact with this flow", "selectVisibility": "Select visibility", "membersDescription": "Select users who will have access to this flow", "searchMembers": "Search members...", "tags": "Tags", "tagsDescription": "Add tags to categorize and easily find this flow", "addTag": "Add tag...", "add": "Add", "suggestedTags": "Suggested tags", "searchTags": "Search tags...", "noTagsFound": "No tags found", "createTag": "Create tag", "createNew": "Create new", "typeToSearch": "Type to search or create a tag", "tagsHelpText": "Select existing tags or create new ones by typing", "selected": "Selected", "archive": "Archive", "unarchive": "Unarchive", "totalItems": "Total items", "completedItems": "Completed items", "conversionRate": "Conversion rate", "avgCycleTime": "Average cycle time", "types": {"sales": "Sales", "marketing": "Marketing", "support": "Support", "development": "Development", "onboarding": "Onboarding", "recruitment": "Recruitment", "other": "Other"}, "templates": {"empty": "Empty", "basic": "Basic", "sales": "Sales", "marketing": "Marketing", "support": "Support", "emptyDescription": "Start with an empty flow and add columns as needed", "backlog": "Backlog", "inProgress": "In Progress", "done": "Done", "leads": "Leads", "qualification": "Qualification", "proposal": "Proposal", "negotiation": "Negotiation", "closed": "Closed", "ideas": "Ideas", "planning": "Planning", "production": "In Production", "review": "Review", "published": "Published", "new": "New", "analyzing": "Analyzing", "waitingClient": "Waiting for Client", "resolved": "Resolved"}, "modes": {"attract": "Attract", "convert": "Convert", "engage": "Engage", "close": "Close", "delight": "Delight", "analyze": "Analyze"}}, "languages": {"pt-BR": "Portuguese", "en": "English", "es": "Spanish"}, "auth": {"login": "<PERSON><PERSON>", "loginDescription": "Sign in to your account to access the system", "withPassword": "With password", "withMagicLink": "With magic link", "email": "Email", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot your password?", "sendMagicLink": "Send magic link", "magicLinkDescription": "We'll send a login link to your email", "orContinueWith": "or continue with", "continueWithGoogle": "Continue with Google", "noAccount": "Don't have an account?", "contactAdmin": "Contact the administrator", "error": "Error", "checkYourEmail": "Check your email", "magicLinkSentDescription": "We've sent a login link to your email", "magicLinkSentInstructions": "Click the link in your email to sign in. The link expires in 15 minutes.", "resetPasswordSentDescription": "We've sent a password reset link to your email", "resetPasswordSentInstructions": "Click the link in your email to reset your password. The link expires in 1 hour.", "backToLogin": "Back to login", "tryAgain": "Try again", "googleAuthError": "Google Authentication Error", "processingGoogleLogin": "Processing Google Login", "pleaseWait": "Please wait while we authenticate your account..."}, "errors": {"notFound": {"title": "Page not found", "description": "Sorry, the page you are looking for doesn't exist or has been moved.", "backHome": "Back to home", "search": "Search the site"}, "accessDenied": {"title": "Access denied", "description": "You don't have permission to access this page. Please contact the administrator if you believe this is an error.", "backHome": "Back to home", "login": "Log in"}, "generic": {"title": "Something went wrong", "description": "An error occurred while processing your request. Please try again later.", "backHome": "Back to home", "tryAgain": "Try again"}}}