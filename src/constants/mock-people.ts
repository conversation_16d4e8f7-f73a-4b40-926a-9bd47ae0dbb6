import { v4 as uuidv4 } from 'uuid';
import { Person, PersonType, PersonContact, PersonDocument, PersonAddress, PersonContactType } from '@mug/models/person';

// Mock data for person contacts
export const mockContacts: PersonContact[] = [
  {
    id: uuidv4(),
    type: "email",
    name: "<PERSON><PERSON> Pessoal",
    value: "<EMAIL>"
  },
  {
    id: uuidv4(),
    type: "phone",
    name: "<PERSON><PERSON><PERSON>",
    value: "+55 11 98765-4321"
  },
  {
    id: uuidv4(),
    type: "whatsapp",
    name: "WhatsApp",
    value: "+55 11 98765-4321"
  },
  {
    id: uuidv4(),
    type: "email",
    name: "Email Corporativo",
    value: "<EMAIL>"
  },
  {
    id: uuidv4(),
    type: "phone",
    name: "Telefone Comercial",
    value: "+55 11 3456-7890"
  },
  {
    id: uuidv4(),
    type: "telegram",
    name: "Telegram",
    value: "@pedrosantos"
  }
];

// Mock data for person documents
export const mockDocuments: PersonDocument[] = [
  {
    id: uuidv4(),
    name: "CP<PERSON>",
    value: "123.456.789-00"
  },
  {
    id: uuidv4(),
    name: "RG",
    value: "12.345.678-9"
  },
  {
    id: uuidv4(),
    name: "CNPJ",
    value: "12.345.678/0001-90"
  },
  {
    id: uuidv4(),
    name: "Inscrição Estadual",
    value: "123456789"
  },
  {
    id: uuidv4(),
    name: "Passaporte",
    value: "AB123456"
  }
];

// Mock data for person addresses
export const mockAddresses: PersonAddress[] = [
  {
    id: uuidv4(),
    name: "Residencial",
    street: "Rua das Flores",
    number: "123",
    complement: "Apto 101",
    neighborhood: "Jardim Primavera",
    city: "São Paulo",
    state: "SP",
    postalCode: "01234-567",
    country: "Brasil"
  },
  {
    id: uuidv4(),
    name: "Comercial",
    street: "Avenida Paulista",
    number: "1000",
    complement: "Sala 1010",
    neighborhood: "Bela Vista",
    city: "São Paulo",
    state: "SP",
    postalCode: "01310-100",
    country: "Brasil"
  },
  {
    id: uuidv4(),
    name: "Entrega",
    street: "Rua dos Pinheiros",
    number: "500",
    neighborhood: "Pinheiros",
    city: "São Paulo",
    state: "SP",
    postalCode: "05422-000",
    country: "Brasil"
  }
];

// Mock data for people (natural persons)
export const mockNaturalPersons: Person[] = [
  {
    id: "person-1",
    type: "person",
    name: "João Silva",
    email: "<EMAIL>",
    phone: "+55 11 98765-4321",
    documents: [mockDocuments[0], mockDocuments[1]],
    contacts: [mockContacts[0], mockContacts[1], mockContacts[2]],
    addresses: [mockAddresses[0]],
    status: true,
    createdAt: "2023-01-15T10:30:00Z",
    updatedAt: "2023-06-20T14:45:00Z"
  },
  {
    id: "person-2",
    type: "person",
    name: "Maria Oliveira",
    email: "<EMAIL>",
    phone: "+55 11 91234-5678",
    documents: [mockDocuments[0], mockDocuments[1]],
    contacts: [mockContacts[3], mockContacts[4]],
    addresses: [mockAddresses[0], mockAddresses[2]],
    status: true,
    createdAt: "2023-02-10T09:15:00Z",
    updatedAt: "2023-07-05T11:20:00Z"
  },
  {
    id: "person-3",
    type: "person",
    name: "Pedro Santos",
    email: "<EMAIL>",
    phone: "+55 11 97654-3210",
    documents: [mockDocuments[0], mockDocuments[4]],
    contacts: [mockContacts[0], mockContacts[5]],
    addresses: [mockAddresses[0]],
    status: true,
    createdAt: "2023-03-05T14:20:00Z",
    updatedAt: "2023-08-12T16:30:00Z"
  },
  {
    id: "person-4",
    type: "person",
    name: "Ana Costa",
    email: "<EMAIL>",
    phone: "+55 11 98877-6655",
    documents: [mockDocuments[0], mockDocuments[1]],
    contacts: [mockContacts[0], mockContacts[1]],
    addresses: [mockAddresses[0]],
    status: false,
    createdAt: "2023-04-20T11:45:00Z",
    updatedAt: "2023-09-01T10:10:00Z"
  },
  {
    id: "person-5",
    type: "person",
    name: "Carlos Pereira",
    email: "<EMAIL>",
    phone: "+55 11 96543-2109",
    documents: [mockDocuments[0], mockDocuments[1]],
    contacts: [mockContacts[0], mockContacts[1]],
    addresses: [mockAddresses[0]],
    status: true,
    createdAt: "2023-05-12T08:30:00Z",
    updatedAt: "2023-10-15T13:25:00Z"
  }
];

// Mock data for companies (legal persons)
export const mockCompanies: Person[] = [
  {
    id: "company-1",
    type: "company",
    name: "Empresa ABC Ltda",
    email: "<EMAIL>",
    phone: "+55 11 3456-7890",
    documents: [mockDocuments[2], mockDocuments[3]],
    contacts: [mockContacts[3], mockContacts[4]],
    addresses: [mockAddresses[1]],
    partners: [mockNaturalPersons[0], mockNaturalPersons[1]],
    status: true,
    createdAt: "2023-01-05T09:00:00Z",
    updatedAt: "2023-06-10T15:30:00Z"
  },
  {
    id: "company-2",
    type: "company",
    name: "XYZ Comércio S.A.",
    email: "<EMAIL>",
    phone: "+55 11 2345-6789",
    documents: [mockDocuments[2], mockDocuments[3]],
    contacts: [mockContacts[3], mockContacts[4]],
    addresses: [mockAddresses[1]],
    partners: [mockNaturalPersons[2]],
    status: true,
    createdAt: "2023-02-15T10:45:00Z",
    updatedAt: "2023-07-20T14:15:00Z"
  },
  {
    id: "company-3",
    type: "company",
    name: "123 Tecnologia Ltda",
    email: "<EMAIL>",
    phone: "+55 11 3344-5566",
    documents: [mockDocuments[2], mockDocuments[3]],
    contacts: [mockContacts[3], mockContacts[4]],
    addresses: [mockAddresses[1]],
    partners: [mockNaturalPersons[3], mockNaturalPersons[4]],
    status: true,
    createdAt: "2023-03-20T11:30:00Z",
    updatedAt: "2023-08-25T16:45:00Z"
  },
  {
    id: "company-4",
    type: "company",
    name: "Indústria DEF S.A.",
    email: "<EMAIL>",
    phone: "+55 11 5566-7788",
    documents: [mockDocuments[2], mockDocuments[3]],
    contacts: [mockContacts[3], mockContacts[4]],
    addresses: [mockAddresses[1]],
    partners: [mockNaturalPersons[0]],
    status: false,
    createdAt: "2023-04-10T13:15:00Z",
    updatedAt: "2023-09-15T10:30:00Z"
  },
  {
    id: "company-5",
    type: "company",
    name: "GHI Serviços Ltda",
    email: "<EMAIL>",
    phone: "+55 11 7788-9900",
    documents: [mockDocuments[2], mockDocuments[3]],
    contacts: [mockContacts[3], mockContacts[4]],
    addresses: [mockAddresses[1]],
    partners: [mockNaturalPersons[1], mockNaturalPersons[2]],
    status: true,
    createdAt: "2023-05-05T14:45:00Z",
    updatedAt: "2023-10-10T12:00:00Z"
  }
];

// Combine all people for easier access
export const mockPeople: Person[] = [...mockNaturalPersons, ...mockCompanies];

// Helper functions to get mock data
export const getAllPeople = (): Person[] => {
  return mockPeople;
};

export const getPersonById = (id: string): Person | undefined => {
  return mockPeople.find(person => person.id === id);
};

export const getPeopleByType = (type: PersonType): Person[] => {
  return mockPeople.filter(person => person.type === type);
};

export const getActivePersons = (): Person[] => {
  return mockPeople.filter(person => person.status);
};

export const getInactivePersons = (): Person[] => {
  return mockPeople.filter(person => !person.status);
};
