import { 
  Plan, 
  Subscription, 
  SubscriptionUser, 
  UserRole, 
  RolePermission, 
  UsageData,
  Invoice
} from "@mug/models/plan";

// Mock plans
export const mockPlans: Plan[] = [
  {
    id: "plan-free",
    name: "Free",
    description: "Para pequenos projetos e experimentação",
    price: 0,
    billingCycle: "monthly",
    isPopular: false,
    features: [
      {
        id: "feature-1",
        name: "1 usuário",
        included: true
      },
      {
        id: "feature-2",
        name: "2 fluxos",
        included: true
      },
      {
        id: "feature-3",
        name: "500 registros",
        included: true
      },
      {
        id: "feature-4",
        name: "1 GB de armazenamento",
        included: true
      },
      {
        id: "feature-5",
        name: "1 landing page",
        included: true
      },
      {
        id: "feature-6",
        name: "1 formulário",
        included: true
      },
      {
        id: "feature-7",
        name: "Automações básicas",
        included: true
      },
      {
        id: "feature-8",
        name: "<PERSON><PERSON><PERSON> personalizado",
        included: false
      },
      {
        id: "feature-9",
        name: "<PERSON>",
        included: false
      },
      {
        id: "feature-10",
        name: "White Label",
        included: false
      }
    ],
    limits: {
      users: 1,
      flows: 2,
      records: 500,
      storage: 1,
      landingPages: 1,
      forms: 1,
      automations: 3,
      customDomain: false,
      api: false,
      whiteLabel: false
    }
  },
  {
    id: "plan-starter",
    name: "Starter",
    description: "Para pequenas empresas e times iniciantes",
    price: 49,
    billingCycle: "monthly",
    isPopular: true,
    features: [
      {
        id: "feature-1",
        name: "3 usuários",
        included: true
      },
      {
        id: "feature-2",
        name: "5 fluxos",
        included: true
      },
      {
        id: "feature-3",
        name: "5.000 registros",
        included: true
      },
      {
        id: "feature-4",
        name: "10 GB de armazenamento",
        included: true
      },
      {
        id: "feature-5",
        name: "5 landing pages",
        included: true
      },
      {
        id: "feature-6",
        name: "5 formulários",
        included: true
      },
      {
        id: "feature-7",
        name: "Automações avançadas",
        included: true
      },
      {
        id: "feature-8",
        name: "Domínio personalizado",
        included: true
      },
      {
        id: "feature-9",
        name: "API",
        included: false
      },
      {
        id: "feature-10",
        name: "White Label",
        included: false
      }
    ],
    limits: {
      users: 3,
      flows: 5,
      records: 5000,
      storage: 10,
      landingPages: 5,
      forms: 5,
      automations: 10,
      customDomain: true,
      api: false,
      whiteLabel: false
    }
  },
  {
    id: "plan-professional",
    name: "Professional",
    description: "Para empresas em crescimento e times profissionais",
    price: 99,
    billingCycle: "monthly",
    isPopular: false,
    features: [
      {
        id: "feature-1",
        name: "10 usuários",
        included: true
      },
      {
        id: "feature-2",
        name: "Fluxos ilimitados",
        included: true
      },
      {
        id: "feature-3",
        name: "25.000 registros",
        included: true
      },
      {
        id: "feature-4",
        name: "50 GB de armazenamento",
        included: true
      },
      {
        id: "feature-5",
        name: "20 landing pages",
        included: true
      },
      {
        id: "feature-6",
        name: "20 formulários",
        included: true
      },
      {
        id: "feature-7",
        name: "Automações avançadas",
        included: true
      },
      {
        id: "feature-8",
        name: "Domínio personalizado",
        included: true
      },
      {
        id: "feature-9",
        name: "API",
        included: true
      },
      {
        id: "feature-10",
        name: "White Label",
        included: false
      }
    ],
    limits: {
      users: 10,
      flows: 999999,
      records: 25000,
      storage: 50,
      landingPages: 20,
      forms: 20,
      automations: 50,
      customDomain: true,
      api: true,
      whiteLabel: false
    }
  },
  {
    id: "plan-business",
    name: "Business",
    description: "Para empresas estabelecidas e times maiores",
    price: 199,
    billingCycle: "monthly",
    isPopular: false,
    features: [
      {
        id: "feature-1",
        name: "25 usuários",
        included: true
      },
      {
        id: "feature-2",
        name: "Fluxos ilimitados",
        included: true
      },
      {
        id: "feature-3",
        name: "100.000 registros",
        included: true
      },
      {
        id: "feature-4",
        name: "200 GB de armazenamento",
        included: true
      },
      {
        id: "feature-5",
        name: "Landing pages ilimitadas",
        included: true
      },
      {
        id: "feature-6",
        name: "Formulários ilimitados",
        included: true
      },
      {
        id: "feature-7",
        name: "Automações avançadas",
        included: true
      },
      {
        id: "feature-8",
        name: "Domínio personalizado",
        included: true
      },
      {
        id: "feature-9",
        name: "API",
        included: true
      },
      {
        id: "feature-10",
        name: "White Label",
        included: true
      }
    ],
    limits: {
      users: 25,
      flows: 999999,
      records: 100000,
      storage: 200,
      landingPages: 999999,
      forms: 999999,
      automations: 999999,
      customDomain: true,
      api: true,
      whiteLabel: true
    }
  },
  {
    id: "plan-enterprise",
    name: "Enterprise",
    description: "Para grandes empresas com necessidades específicas",
    price: 0, // Preço sob consulta
    billingCycle: "custom",
    isPopular: false,
    isEnterprise: true,
    features: [
      {
        id: "feature-1",
        name: "Usuários ilimitados",
        included: true
      },
      {
        id: "feature-2",
        name: "Fluxos ilimitados",
        included: true
      },
      {
        id: "feature-3",
        name: "Registros ilimitados",
        included: true
      },
      {
        id: "feature-4",
        name: "Armazenamento ilimitado",
        included: true
      },
      {
        id: "feature-5",
        name: "Landing pages ilimitadas",
        included: true
      },
      {
        id: "feature-6",
        name: "Formulários ilimitados",
        included: true
      },
      {
        id: "feature-7",
        name: "Automações personalizadas",
        included: true
      },
      {
        id: "feature-8",
        name: "Domínio personalizado",
        included: true
      },
      {
        id: "feature-9",
        name: "API avançada",
        included: true
      },
      {
        id: "feature-10",
        name: "White Label",
        included: true
      },
      {
        id: "feature-11",
        name: "Suporte dedicado",
        included: true
      },
      {
        id: "feature-12",
        name: "SLA garantido",
        included: true
      },
      {
        id: "feature-13",
        name: "Integrações personalizadas",
        included: true
      }
    ],
    limits: {
      users: 999999,
      flows: 999999,
      records: 999999,
      storage: 999999,
      landingPages: 999999,
      forms: 999999,
      automations: 999999,
      customDomain: true,
      api: true,
      whiteLabel: true
    }
  }
];

// Mock role permissions
export const mockRolePermissions: RolePermission[] = [
  {
    role: "owner",
    permissions: {
      manageSubscription: true,
      manageUsers: true,
      manageBilling: true,
      manageFlows: true,
      viewReports: true,
      manageRecords: true,
      manageLandingPages: true,
      manageForms: true,
      manageAutomations: true
    }
  },
  {
    role: "admin",
    permissions: {
      manageSubscription: false,
      manageUsers: true,
      manageBilling: true,
      manageFlows: true,
      viewReports: true,
      manageRecords: true,
      manageLandingPages: true,
      manageForms: true,
      manageAutomations: true
    }
  },
  {
    role: "manager",
    permissions: {
      manageSubscription: false,
      manageUsers: false,
      manageBilling: false,
      manageFlows: true,
      viewReports: true,
      manageRecords: true,
      manageLandingPages: true,
      manageForms: true,
      manageAutomations: true
    }
  },
  {
    role: "member",
    permissions: {
      manageSubscription: false,
      manageUsers: false,
      manageBilling: false,
      manageFlows: false,
      viewReports: true,
      manageRecords: true,
      manageLandingPages: true,
      manageForms: true,
      manageAutomations: false
    }
  },
  {
    role: "viewer",
    permissions: {
      manageSubscription: false,
      manageUsers: false,
      manageBilling: false,
      manageFlows: false,
      viewReports: true,
      manageRecords: false,
      manageLandingPages: false,
      manageForms: false,
      manageAutomations: false
    }
  }
];

// Mock subscription users
export const mockSubscriptionUsers: SubscriptionUser[] = [
  {
    id: "user-1",
    userId: "current-user",
    name: "João Silva",
    email: "<EMAIL>",
    role: "owner",
    status: "active",
    joinedAt: "2023-01-15T10:00:00Z"
  },
  {
    id: "user-2",
    userId: "user-2",
    name: "Maria Santos",
    email: "<EMAIL>",
    role: "admin",
    status: "active",
    joinedAt: "2023-02-10T14:30:00Z"
  },
  {
    id: "user-3",
    userId: "user-3",
    name: "Pedro Oliveira",
    email: "<EMAIL>",
    role: "manager",
    status: "active",
    joinedAt: "2023-03-05T09:15:00Z"
  },
  {
    id: "user-4",
    userId: "user-4",
    name: "Ana Costa",
    email: "<EMAIL>",
    role: "member",
    status: "active",
    joinedAt: "2023-04-20T11:45:00Z"
  },
  {
    id: "user-5",
    userId: "user-5",
    name: "Carlos Ferreira",
    email: "<EMAIL>",
    role: "viewer",
    status: "active",
    joinedAt: "2023-05-12T16:20:00Z"
  },
  {
    id: "user-6",
    userId: "user-6",
    name: "Lúcia Mendes",
    email: "<EMAIL>",
    role: "member",
    status: "invited",
    invitedAt: "2023-06-08T10:30:00Z"
  }
];

// Mock invoices
export const mockInvoices: Invoice[] = [
  {
    id: "invoice-1",
    subscriptionId: "subscription-1",
    amount: 99,
    status: "paid",
    issueDate: "2023-05-01T00:00:00Z",
    dueDate: "2023-05-15T00:00:00Z",
    paidDate: "2023-05-10T14:30:00Z",
    items: [
      {
        description: "Professional Plan - Maio 2023",
        quantity: 1,
        unitPrice: 99,
        amount: 99
      }
    ],
    pdf: "https://example.com/invoices/invoice-1.pdf"
  },
  {
    id: "invoice-2",
    subscriptionId: "subscription-1",
    amount: 99,
    status: "paid",
    issueDate: "2023-06-01T00:00:00Z",
    dueDate: "2023-06-15T00:00:00Z",
    paidDate: "2023-06-08T09:45:00Z",
    items: [
      {
        description: "Professional Plan - Junho 2023",
        quantity: 1,
        unitPrice: 99,
        amount: 99
      }
    ],
    pdf: "https://example.com/invoices/invoice-2.pdf"
  },
  {
    id: "invoice-3",
    subscriptionId: "subscription-1",
    amount: 99,
    status: "paid",
    issueDate: "2023-07-01T00:00:00Z",
    dueDate: "2023-07-15T00:00:00Z",
    paidDate: "2023-07-12T11:20:00Z",
    items: [
      {
        description: "Professional Plan - Julho 2023",
        quantity: 1,
        unitPrice: 99,
        amount: 99
      }
    ],
    pdf: "https://example.com/invoices/invoice-3.pdf"
  },
  {
    id: "invoice-4",
    subscriptionId: "subscription-1",
    amount: 99,
    status: "unpaid",
    issueDate: "2023-08-01T00:00:00Z",
    dueDate: "2023-08-15T00:00:00Z",
    items: [
      {
        description: "Professional Plan - Agosto 2023",
        quantity: 1,
        unitPrice: 99,
        amount: 99
      }
    ],
    pdf: "https://example.com/invoices/invoice-4.pdf"
  }
];

// Mock usage data
export const mockUsageData: UsageData = {
  users: {
    used: 6,
    limit: 10,
    percentage: 60,
    warningLevel: "safe"
  },
  flows: {
    used: 8,
    limit: 999999,
    percentage: 0,
    warningLevel: "safe"
  },
  records: {
    used: 18500,
    limit: 25000,
    percentage: 74,
    warningLevel: "warning"
  },
  storage: {
    used: 32,
    limit: 50,
    percentage: 64,
    warningLevel: "safe"
  },
  landingPages: {
    used: 12,
    limit: 20,
    percentage: 60,
    warningLevel: "safe"
  },
  forms: {
    used: 18,
    limit: 20,
    percentage: 90,
    warningLevel: "critical"
  },
  automations: {
    used: 25,
    limit: 50,
    percentage: 50,
    warningLevel: "safe"
  }
};

// Mock subscription
export const mockSubscription: Subscription = {
  id: "subscription-1",
  userId: "current-user",
  planId: "plan-professional",
  plan: mockPlans.find(plan => plan.id === "plan-professional")!,
  status: "active",
  startDate: "2023-05-01T00:00:00Z",
  endDate: "2024-05-01T00:00:00Z",
  isInTrial: false,
  autoRenew: true,
  paymentMethod: {
    id: "payment-1",
    type: "credit_card",
    lastFour: "4242",
    expiryDate: "12/25",
    isDefault: true
  },
  billingInfo: {
    name: "João Silva",
    email: "<EMAIL>",
    address: "Rua Exemplo, 123",
    city: "São Paulo",
    state: "SP",
    zipCode: "01234-567",
    country: "Brasil",
    companyName: "Empresa Exemplo Ltda",
    taxId: "12.345.678/0001-90"
  },
  usage: mockUsageData,
  invoices: mockInvoices,
  users: mockSubscriptionUsers
};

// Helper functions
export const getPlanById = (id: string): Plan | undefined => {
  return mockPlans.find(plan => plan.id === id);
};

export const getSubscription = (): Subscription => {
  return mockSubscription;
};

export const getRolePermissions = (role: UserRole): RolePermission | undefined => {
  return mockRolePermissions.find(permission => permission.role === role);
};
