import { Dashboard, DashboardWidget, ChartData, TableData, ListData, FunnelData, KanbanData } from "@mug/models/dashboard";
import { v4 as uuidv4 } from "uuid";

// Mock general dashboard
export const generalDashboard: Dashboard = {
  id: "dashboard-general",
  name: "Dashboard Geral",
  description: "Visão geral de todos os fluxos e métricas do sistema",
  isDefault: true,
  widgets: [
    // Total de Leads
    {
      id: "widget-1",
      type: "counter",
      title: "Total de Leads",
      size: "small",
      position: { x: 0, y: 0 },
      config: {
        icon: "user",
        color: "blue",
        format: "number"
      },
      data: {
        value: 1248,
        previousValue: 1156,
        change: 7.96,
        changeType: "positive",
        period: "30d"
      }
    },
    // Taxa de Conversão
    {
      id: "widget-2",
      type: "counter",
      title: "Taxa de Conversão",
      size: "small",
      position: { x: 1, y: 0 },
      config: {
        icon: "percent",
        color: "green",
        format: "percentage"
      },
      data: {
        value: 12.4,
        previousValue: 10.8,
        change: 14.81,
        changeType: "positive",
        period: "30d"
      }
    },
    // Ticket Médio
    {
      id: "widget-3",
      type: "counter",
      title: "Ticket Médio",
      size: "small",
      position: { x: 2, y: 0 },
      config: {
        icon: "dollar-sign",
        color: "yellow",
        format: "currency"
      },
      data: {
        value: 1850,
        previousValue: 1720,
        change: 7.56,
        changeType: "positive",
        period: "30d",
        currency: "BRL"
      }
    },
    // Leads por Origem
    {
      id: "widget-4",
      type: "pie_chart",
      title: "Leads por Origem",
      size: "medium",
      position: { x: 0, y: 1 },
      config: {
        showLegend: true,
        showLabels: false,
        colors: ["#4f46e5", "#0ea5e9", "#10b981", "#f59e0b", "#ef4444"]
      },
      data: {
        labels: ["Site", "Redes Sociais", "Email Marketing", "Indicação", "Outros"],
        datasets: [
          {
            data: [45, 25, 15, 10, 5],
            backgroundColor: ["#4f46e5", "#0ea5e9", "#10b981", "#f59e0b", "#ef4444"]
          }
        ]
      } as ChartData
    },
    // Conversões por Mês
    {
      id: "widget-5",
      type: "bar_chart",
      title: "Conversões por Mês",
      size: "large",
      position: { x: 1, y: 1 },
      config: {
        showLegend: true,
        showGrid: true,
        stacked: false,
        colors: ["#4f46e5", "#ef4444"]
      },
      data: {
        labels: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun"],
        datasets: [
          {
            label: "Leads",
            data: [180, 220, 205, 250, 285, 310],
            backgroundColor: "#4f46e5"
          },
          {
            label: "Conversões",
            data: [20, 25, 22, 30, 35, 42],
            backgroundColor: "#ef4444"
          }
        ]
      } as ChartData
    },
    // Funil de Vendas
    {
      id: "widget-6",
      type: "funnel",
      title: "Funil de Vendas",
      size: "medium",
      position: { x: 0, y: 3 },
      config: {
        showPercentages: true,
        colors: ["#4f46e5", "#0ea5e9", "#10b981", "#f59e0b", "#ef4444"]
      },
      data: {
        stages: [
          { name: "Leads", value: 1248 },
          { name: "Qualificados", value: 520 },
          { name: "Apresentação", value: 280 },
          { name: "Proposta", value: 180 },
          { name: "Fechamento", value: 85 }
        ]
      } as FunnelData
    },
    // Principais Fluxos
    {
      id: "widget-7",
      type: "table",
      title: "Principais Fluxos",
      size: "large",
      position: { x: 1, y: 3 },
      config: {
        showHeader: true,
        pagination: false
      },
      data: {
        headers: ["Fluxo", "Leads", "Conversões", "Taxa", "Ticket Médio"],
        rows: [
          ["Vendas B2B", 450, 65, "14.4%", "R$ 2.850"],
          ["Marketing Digital", 380, 42, "11.1%", "R$ 1.250"],
          ["E-commerce", 320, 48, "15.0%", "R$ 950"],
          ["Suporte", 98, 0, "0%", "R$ 0"]
        ]
      } as TableData
    }
  ],
  createdAt: "2023-01-15T10:00:00Z",
  updatedAt: "2023-06-20T14:30:00Z"
};

// Mock flow dashboards
export const flowDashboards: Dashboard[] = [
  // Vendas B2B Dashboard
  {
    id: "dashboard-flow-1",
    name: "Dashboard de Vendas B2B",
    description: "Métricas e análises do fluxo de Vendas B2B",
    flowId: "flow-1",
    widgets: [
      // Total de Leads B2B
      {
        id: "widget-flow1-1",
        type: "counter",
        title: "Total de Leads B2B",
        size: "small",
        position: { x: 0, y: 0 },
        config: {
          icon: "briefcase",
          color: "blue",
          format: "number"
        },
        data: {
          value: 450,
          previousValue: 420,
          change: 7.14,
          changeType: "positive",
          period: "30d"
        }
      },
      // Taxa de Conversão B2B
      {
        id: "widget-flow1-2",
        type: "counter",
        title: "Taxa de Conversão",
        size: "small",
        position: { x: 1, y: 0 },
        config: {
          icon: "percent",
          color: "green",
          format: "percentage"
        },
        data: {
          value: 14.4,
          previousValue: 12.8,
          change: 12.5,
          changeType: "positive",
          period: "30d"
        }
      },
      // Ticket Médio B2B
      {
        id: "widget-flow1-3",
        type: "counter",
        title: "Ticket Médio",
        size: "small",
        position: { x: 2, y: 0 },
        config: {
          icon: "dollar-sign",
          color: "yellow",
          format: "currency"
        },
        data: {
          value: 2850,
          previousValue: 2650,
          change: 7.55,
          changeType: "positive",
          period: "30d",
          currency: "BRL"
        }
      },
      // Leads B2B por Segmento
      {
        id: "widget-flow1-4",
        type: "pie_chart",
        title: "Leads por Segmento",
        size: "medium",
        position: { x: 0, y: 1 },
        config: {
          showLegend: true,
          showLabels: false,
          colors: ["#4f46e5", "#0ea5e9", "#10b981", "#f59e0b", "#ef4444"]
        },
        data: {
          labels: ["Tecnologia", "Saúde", "Educação", "Finanças", "Outros"],
          datasets: [
            {
              data: [35, 25, 20, 15, 5],
              backgroundColor: ["#4f46e5", "#0ea5e9", "#10b981", "#f59e0b", "#ef4444"]
            }
          ]
        } as ChartData
      },
      // Vendas B2B por Mês
      {
        id: "widget-flow1-5",
        type: "line_chart",
        title: "Vendas por Mês",
        size: "large",
        position: { x: 1, y: 1 },
        config: {
          showLegend: true,
          showGrid: true,
          fill: false,
          tension: 0.4,
          colors: ["#4f46e5", "#ef4444"]
        },
        data: {
          labels: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun"],
          datasets: [
            {
              label: "2023",
              data: [120000, 150000, 135000, 180000, 195000, 210000],
              borderColor: "#4f46e5",
              backgroundColor: "rgba(79, 70, 229, 0.1)"
            },
            {
              label: "2022",
              data: [100000, 125000, 115000, 140000, 160000, 175000],
              borderColor: "#ef4444",
              backgroundColor: "rgba(239, 68, 68, 0.1)"
            }
          ]
        } as ChartData
      },
      // Funil de Vendas B2B
      {
        id: "widget-flow1-6",
        type: "funnel",
        title: "Funil de Vendas B2B",
        size: "medium",
        position: { x: 0, y: 3 },
        config: {
          showPercentages: true,
          colors: ["#4f46e5", "#0ea5e9", "#10b981", "#f59e0b", "#ef4444"]
        },
        data: {
          stages: [
            { name: "Leads", value: 450 },
            { name: "Qualificados", value: 220 },
            { name: "Apresentação", value: 150 },
            { name: "Proposta", value: 95 },
            { name: "Fechamento", value: 65 }
          ]
        } as FunnelData
      },
      // Principais Oportunidades
      {
        id: "widget-flow1-7",
        type: "table",
        title: "Principais Oportunidades",
        size: "large",
        position: { x: 1, y: 3 },
        config: {
          showHeader: true,
          pagination: false
        },
        data: {
          headers: ["Empresa", "Valor", "Estágio", "Probabilidade", "Responsável"],
          rows: [
            ["Empresa ABC", "R$ 85.000", "Proposta", "75%", "João Silva"],
            ["Empresa XYZ", "R$ 65.000", "Apresentação", "50%", "Maria Santos"],
            ["Empresa 123", "R$ 120.000", "Qualificação", "25%", "Pedro Oliveira"],
            ["Empresa 456", "R$ 45.000", "Proposta", "80%", "Ana Costa"]
          ]
        } as TableData
      }
    ],
    createdAt: "2023-02-10T09:15:00Z",
    updatedAt: "2023-06-25T11:45:00Z"
  },
  // Marketing Digital Dashboard
  {
    id: "dashboard-flow-2",
    name: "Dashboard de Marketing Digital",
    description: "Métricas e análises do fluxo de Marketing Digital",
    flowId: "flow-2",
    widgets: [
      // Total de Leads Marketing
      {
        id: "widget-flow2-1",
        type: "counter",
        title: "Total de Leads",
        size: "small",
        position: { x: 0, y: 0 },
        config: {
          icon: "users",
          color: "purple",
          format: "number"
        },
        data: {
          value: 380,
          previousValue: 340,
          change: 11.76,
          changeType: "positive",
          period: "30d"
        }
      },
      // Taxa de Conversão Marketing
      {
        id: "widget-flow2-2",
        type: "counter",
        title: "Taxa de Conversão",
        size: "small",
        position: { x: 1, y: 0 },
        config: {
          icon: "percent",
          color: "green",
          format: "percentage"
        },
        data: {
          value: 11.1,
          previousValue: 10.5,
          change: 5.71,
          changeType: "positive",
          period: "30d"
        }
      },
      // CAC (Custo de Aquisição de Cliente)
      {
        id: "widget-flow2-3",
        type: "counter",
        title: "CAC",
        size: "small",
        position: { x: 2, y: 0 },
        config: {
          icon: "dollar-sign",
          color: "red",
          format: "currency"
        },
        data: {
          value: 120,
          previousValue: 135,
          change: 11.11,
          changeType: "positive",
          period: "30d",
          currency: "BRL"
        }
      },
      // Leads por Canal
      {
        id: "widget-flow2-4",
        type: "bar_chart",
        title: "Leads por Canal",
        size: "medium",
        position: { x: 0, y: 1 },
        config: {
          showLegend: false,
          showGrid: true,
          horizontal: true,
          colors: ["#8b5cf6"]
        },
        data: {
          labels: ["Google Ads", "Facebook", "Instagram", "LinkedIn", "Email"],
          datasets: [
            {
              data: [120, 95, 85, 50, 30],
              backgroundColor: "#8b5cf6"
            }
          ]
        } as ChartData
      },
      // Desempenho de Campanhas
      {
        id: "widget-flow2-5",
        type: "table",
        title: "Desempenho de Campanhas",
        size: "large",
        position: { x: 1, y: 1 },
        config: {
          showHeader: true,
          pagination: false
        },
        data: {
          headers: ["Campanha", "Impressões", "Cliques", "CTR", "Conversões", "CPA"],
          rows: [
            ["Black Friday", "125.000", "6.250", "5.0%", "185", "R$ 95"],
            ["Lançamento Produto", "85.000", "4.250", "5.0%", "120", "R$ 110"],
            ["Remarketing", "45.000", "2.700", "6.0%", "95", "R$ 85"],
            ["Institucional", "30.000", "1.200", "4.0%", "35", "R$ 140"]
          ]
        } as TableData
      },
      // Tráfego por Dispositivo
      {
        id: "widget-flow2-6",
        type: "donut_chart",
        title: "Tráfego por Dispositivo",
        size: "medium",
        position: { x: 0, y: 3 },
        config: {
          showLegend: true,
          showLabels: false,
          colors: ["#8b5cf6", "#ec4899", "#f59e0b"]
        },
        data: {
          labels: ["Mobile", "Desktop", "Tablet"],
          datasets: [
            {
              data: [65, 30, 5],
              backgroundColor: ["#8b5cf6", "#ec4899", "#f59e0b"]
            }
          ]
        } as ChartData
      },
      // Métricas de Engajamento
      {
        id: "widget-flow2-7",
        type: "line_chart",
        title: "Métricas de Engajamento",
        size: "large",
        position: { x: 1, y: 3 },
        config: {
          showLegend: true,
          showGrid: true,
          fill: true,
          tension: 0.4,
          colors: ["#8b5cf6", "#ec4899"]
        },
        data: {
          labels: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun"],
          datasets: [
            {
              label: "Tempo na Página (seg)",
              data: [45, 50, 48, 55, 60, 58],
              borderColor: "#8b5cf6",
              backgroundColor: "rgba(139, 92, 246, 0.1)"
            },
            {
              label: "Taxa de Rejeição (%)",
              data: [65, 60, 58, 55, 50, 48],
              borderColor: "#ec4899",
              backgroundColor: "rgba(236, 72, 153, 0.1)"
            }
          ]
        } as ChartData
      }
    ],
    createdAt: "2023-03-05T14:20:00Z",
    updatedAt: "2023-07-10T09:30:00Z"
  },
  // E-commerce Dashboard
  {
    id: "dashboard-flow-3",
    name: "Dashboard de E-commerce",
    description: "Métricas e análises do fluxo de E-commerce",
    flowId: "flow-3",
    widgets: [
      // Receita Total
      {
        id: "widget-flow3-1",
        type: "counter",
        title: "Receita Total",
        size: "small",
        position: { x: 0, y: 0 },
        config: {
          icon: "dollar-sign",
          color: "green",
          format: "currency"
        },
        data: {
          value: 45600,
          previousValue: 38500,
          change: 18.44,
          changeType: "positive",
          period: "30d",
          currency: "BRL"
        }
      },
      // Número de Pedidos
      {
        id: "widget-flow3-2",
        type: "counter",
        title: "Pedidos",
        size: "small",
        position: { x: 1, y: 0 },
        config: {
          icon: "shopping-cart",
          color: "blue",
          format: "number"
        },
        data: {
          value: 320,
          previousValue: 285,
          change: 12.28,
          changeType: "positive",
          period: "30d"
        }
      },
      // Ticket Médio
      {
        id: "widget-flow3-3",
        type: "counter",
        title: "Ticket Médio",
        size: "small",
        position: { x: 2, y: 0 },
        config: {
          icon: "tag",
          color: "yellow",
          format: "currency"
        },
        data: {
          value: 142.5,
          previousValue: 135.1,
          change: 5.48,
          changeType: "positive",
          period: "30d",
          currency: "BRL"
        }
      },
      // Vendas por Categoria
      {
        id: "widget-flow3-4",
        type: "pie_chart",
        title: "Vendas por Categoria",
        size: "medium",
        position: { x: 0, y: 1 },
        config: {
          showLegend: true,
          showLabels: false,
          colors: ["#0ea5e9", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6"]
        },
        data: {
          labels: ["Eletrônicos", "Moda", "Casa", "Esportes", "Outros"],
          datasets: [
            {
              data: [40, 25, 15, 12, 8],
              backgroundColor: ["#0ea5e9", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6"]
            }
          ]
        } as ChartData
      },
      // Vendas por Período
      {
        id: "widget-flow3-5",
        type: "bar_chart",
        title: "Vendas por Período",
        size: "large",
        position: { x: 1, y: 1 },
        config: {
          showLegend: false,
          showGrid: true,
          stacked: false,
          colors: ["#0ea5e9"]
        },
        data: {
          labels: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun"],
          datasets: [
            {
              label: "Vendas (R$)",
              data: [38500, 42000, 39500, 45000, 48500, 45600],
              backgroundColor: "#0ea5e9"
            }
          ]
        } as ChartData
      },
      // Produtos Mais Vendidos
      {
        id: "widget-flow3-6",
        type: "table",
        title: "Produtos Mais Vendidos",
        size: "medium",
        position: { x: 0, y: 3 },
        config: {
          showHeader: true,
          pagination: false
        },
        data: {
          headers: ["Produto", "Vendas", "Receita", "Avaliação"],
          rows: [
            ["Smartphone XYZ", "45", "R$ 45.000", "4.8/5"],
            ["Notebook ABC", "28", "R$ 42.000", "4.7/5"],
            ["Fone de Ouvido", "120", "R$ 18.000", "4.5/5"],
            ["Smart TV 50\"", "25", "R$ 37.500", "4.6/5"]
          ]
        } as TableData
      },
      // Taxa de Abandono de Carrinho
      {
        id: "widget-flow3-7",
        type: "gauge",
        title: "Taxa de Abandono de Carrinho",
        size: "medium",
        position: { x: 1, y: 3 },
        config: {
          min: 0,
          max: 100,
          thresholds: [
            { value: 30, color: "#10b981" },
            { value: 60, color: "#f59e0b" },
            { value: 100, color: "#ef4444" }
          ]
        },
        data: {
          value: 68,
          previousValue: 72,
          change: 5.56,
          changeType: "positive"
        }
      },
      // Métodos de Pagamento
      {
        id: "widget-flow3-8",
        type: "donut_chart",
        title: "Métodos de Pagamento",
        size: "medium",
        position: { x: 2, y: 3 },
        config: {
          showLegend: true,
          showLabels: false,
          colors: ["#0ea5e9", "#10b981", "#f59e0b", "#ef4444"]
        },
        data: {
          labels: ["Cartão de Crédito", "Boleto", "Pix", "Outros"],
          datasets: [
            {
              data: [65, 15, 18, 2],
              backgroundColor: ["#0ea5e9", "#10b981", "#f59e0b", "#ef4444"]
            }
          ]
        } as ChartData
      }
    ],
    createdAt: "2023-04-12T11:30:00Z",
    updatedAt: "2023-07-15T16:45:00Z"
  }
];

// Helper functions
export const getAllDashboards = (): Dashboard[] => {
  return [generalDashboard, ...flowDashboards];
};

export const getDashboardById = (id: string): Dashboard | undefined => {
  if (id === generalDashboard.id) return generalDashboard;
  return flowDashboards.find(dashboard => dashboard.id === id);
};

export const getDashboardByFlowId = (flowId: string): Dashboard | undefined => {
  return flowDashboards.find(dashboard => dashboard.flowId === flowId);
};

export const getGeneralDashboard = (): Dashboard => {
  return generalDashboard;
};
