import {
  BarChart3,
  Network,
  Users,
  Layout,
  Puzzle,
  <PERSON><PERSON>,
  Bo<PERSON>
} from "lucide-react";

export interface SidebarMenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  href: string;
  badge?: number;
}

export const sidebarMenuItems: SidebarMenuItem[] = [
  {
    id: "dashboards",
    label: "Dashboards",
    icon: BarChart3,
    href: "/dashboard",
  },
  {
    id: "flows",
    label: "Fluxos",
    icon: Network,
    href: "/flows",
  },
  {
    id: "people",
    label: "Pessoas",
    icon: Users,
    href: "/people",
  },
  {
    id: "landing-pages",
    label: "Landing pages e formulários personalizados",
    icon: Layout,
    href: "/landing-pages",
  },
  {
    id: "actions",
    label: "Ações",
    icon: Puzzle,
    href: "/actions",
  },
  {
    id: "automations",
    label: "Automações",
    icon: Zap,
    href: "/automations",
  },
  {
    id: "agents",
    label: "Agent<PERSON>",
    icon: <PERSON><PERSON>,
    href: "/agents",
  },
];
