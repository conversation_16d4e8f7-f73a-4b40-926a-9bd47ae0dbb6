/**
 * Color palette for lane backgrounds
 * These are soft pastel colors with low opacity for a subtle effect
 */
export interface ColorOption {
  id: string;
  name: string;
  value: string;
}

export const laneColorPalette: ColorOption[] = [
  { id: "default", name: "Default", value: "transparent" },
  { id: "blue", name: "Blue", value: "#E6F0FF" },
  { id: "green", name: "Green", value: "#E6F9F0" },
  { id: "purple", name: "Purple", value: "#F0E6FF" },
  { id: "pink", name: "Pink", value: "#FFE6F0" },
  { id: "yellow", name: "Yellow", value: "#FFFDE6" },
  { id: "orange", name: "Orange", value: "#FFF0E6" },
  { id: "teal", name: "Teal", value: "#E6FFFA" },
  { id: "cyan", name: "<PERSON><PERSON>", value: "#E6FAFF" },
  { id: "lime", name: "<PERSON><PERSON>", value: "#F0FFE6" },
];
