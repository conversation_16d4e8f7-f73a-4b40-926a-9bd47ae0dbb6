import { v4 as uuidv4 } from 'uuid';
import { Agent, AgentModelType, AgentTemplate } from '@mug/models/agent';

// Mock actions that agents can use
export const mockActions = [
  {
    id: "action-1",
    name: "Send Email",
    description: "Send an email to a contact",
    category: "communication"
  },
  {
    id: "action-2",
    name: "Create Task",
    description: "Create a new task in the system",
    category: "task-management"
  },
  {
    id: "action-3",
    name: "Schedule Meeting",
    description: "Schedule a meeting with contacts",
    category: "calendar"
  },
  {
    id: "action-4",
    name: "Update Contact",
    description: "Update contact information",
    category: "contact-management"
  },
  {
    id: "action-5",
    name: "Generate Report",
    description: "Generate a report based on data",
    category: "reporting"
  },
  {
    id: "action-6",
    name: "Send Message",
    description: "Send a message through a messaging channel",
    category: "communication"
  },
  {
    id: "action-7",
    name: "Create Note",
    description: "Create a note about a contact or deal",
    category: "note-taking"
  },
  {
    id: "action-8",
    name: "Update Deal",
    description: "Update information about a deal",
    category: "sales"
  }
];

// Mock agent templates
export const agentTemplates: Record<AgentTemplate, Partial<Agent>> = {
  "empty": {
    name: "",
    description: "",
    actions: [],
    rules: [],
    model: AgentModelType.GPT35
  },
  "customer-service": {
    name: "Assistente de Atendimento ao Cliente",
    description: "Agente para responder dúvidas comuns de clientes e encaminhar problemas complexos para humanos.",
    actions: ["action-1", "action-6", "action-7"],
    rules: [
      "Sempre cumprimente o cliente pelo nome",
      "Mantenha um tom amigável e profissional",
      "Encaminhe para um humano se o cliente parecer frustrado",
      "Nunca prometa algo que não pode cumprir",
      "Verifique se o cliente está satisfeito antes de encerrar"
    ],
    model: AgentModelType.CLAUDE3_SONNET
  },
  "sales-assistant": {
    name: "Assistente de Vendas",
    description: "Agente para qualificar leads, responder perguntas sobre produtos e agendar demonstrações.",
    actions: ["action-1", "action-3", "action-4", "action-8"],
    rules: [
      "Qualifique o lead antes de sugerir produtos",
      "Destaque os benefícios, não apenas as características",
      "Agende uma demonstração se o lead mostrar interesse",
      "Faça perguntas abertas para entender as necessidades",
      "Mantenha o foco nas soluções, não nos problemas"
    ],
    model: AgentModelType.GPT4
  },
  "data-analyst": {
    name: "Analista de Dados",
    description: "Agente para analisar dados, gerar relatórios e identificar tendências.",
    actions: ["action-5", "action-7"],
    rules: [
      "Verifique a qualidade dos dados antes de analisar",
      "Apresente os dados de forma clara e objetiva",
      "Destaque insights acionáveis",
      "Inclua visualizações quando apropriado",
      "Explique a metodologia utilizada"
    ],
    model: AgentModelType.CLAUDE3_OPUS
  },
  "content-creator": {
    name: "Criador de Conteúdo",
    description: "Agente para criar e sugerir conteúdo para marketing e redes sociais.",
    actions: ["action-1", "action-7"],
    rules: [
      "Adapte o tom ao público-alvo",
      "Mantenha o conteúdo alinhado com a marca",
      "Inclua chamadas para ação claras",
      "Otimize para SEO quando apropriado",
      "Verifique a gramática e ortografia"
    ],
    model: AgentModelType.GEMINI_PRO
  }
};

// Mock agents
export const mockAgents: Agent[] = [
  {
    id: "agent-1",
    name: "Assistente de Suporte Técnico",
    description: "Agente para resolver problemas técnicos comuns e coletar informações para a equipe de suporte.",
    actions: ["action-1", "action-2", "action-6", "action-7"],
    rules: [
      "Peça informações sobre o sistema operacional e versão do software",
      "Sugira soluções para problemas comuns",
      "Colete logs e informações de erro",
      "Escale para um técnico humano se não conseguir resolver",
      "Faça follow-up após a resolução"
    ],
    model: AgentModelType.GPT35,
    createdAt: "2023-05-10T14:30:00Z",
    updatedAt: "2023-11-15T09:45:00Z",
    status: true
  },
  {
    id: "agent-2",
    name: "Assistente de Onboarding",
    description: "Agente para guiar novos usuários no processo de onboarding e responder dúvidas iniciais.",
    actions: ["action-1", "action-3", "action-6", "action-7"],
    rules: [
      "Cumprimente novos usuários de forma amigável",
      "Explique as principais funcionalidades do sistema",
      "Ofereça tutoriais e recursos de aprendizado",
      "Responda dúvidas sobre como começar",
      "Agende uma sessão com um especialista se necessário"
    ],
    model: AgentModelType.CLAUDE3_SONNET,
    createdAt: "2023-06-20T10:15:00Z",
    updatedAt: "2023-12-05T16:30:00Z",
    status: true
  },
  {
    id: "agent-3",
    name: "Qualificador de Leads",
    description: "Agente para qualificar leads iniciais e coletar informações relevantes para a equipe de vendas.",
    actions: ["action-1", "action-4", "action-6", "action-8"],
    rules: [
      "Identifique a necessidade principal do lead",
      "Colete informações sobre orçamento e prazo",
      "Avalie o poder de decisão do contato",
      "Categorize o lead por potencial e urgência",
      "Agende uma reunião com um vendedor para leads qualificados"
    ],
    model: AgentModelType.GPT4,
    createdAt: "2023-07-12T09:00:00Z",
    updatedAt: "2024-01-18T11:20:00Z",
    status: true
  },
  {
    id: "agent-4",
    name: "Assistente de Agendamento",
    description: "Agente para gerenciar agendamentos, lembretes e confirmações de reuniões.",
    actions: ["action-1", "action-3", "action-6"],
    rules: [
      "Verifique a disponibilidade antes de agendar",
      "Envie confirmações e lembretes",
      "Ofereça opções de reagendamento quando necessário",
      "Colete informações sobre o propósito da reunião",
      "Atualize o calendário automaticamente"
    ],
    model: AgentModelType.CLAUDE3_HAIKU,
    createdAt: "2023-08-05T13:45:00Z",
    updatedAt: "2024-02-10T15:30:00Z",
    status: true
  },
  {
    id: "agent-5",
    name: "Analista de Feedback",
    description: "Agente para coletar, categorizar e analisar feedback de clientes.",
    actions: ["action-1", "action-5", "action-7"],
    rules: [
      "Categorize o feedback por tipo e sentimento",
      "Identifique tendências e padrões",
      "Destaque problemas recorrentes",
      "Sugira melhorias com base no feedback",
      "Agradeça aos clientes pelo feedback fornecido"
    ],
    model: AgentModelType.MISTRAL_LARGE,
    createdAt: "2023-09-18T11:30:00Z",
    updatedAt: "2024-03-05T09:15:00Z",
    status: false
  },
  {
    id: "agent-6",
    name: "Assistente de Conteúdo",
    description: "Agente para sugerir e criar conteúdo para blogs, redes sociais e newsletters.",
    actions: ["action-1", "action-7"],
    rules: [
      "Adapte o conteúdo ao público-alvo",
      "Siga as diretrizes de marca",
      "Otimize para SEO quando relevante",
      "Inclua chamadas para ação claras",
      "Mantenha um calendário de conteúdo consistente"
    ],
    model: AgentModelType.GEMINI_PRO,
    createdAt: "2023-10-25T14:00:00Z",
    updatedAt: "2024-04-12T10:45:00Z",
    status: true
  }
];

// Helper functions
export const getAllAgents = (): Agent[] => {
  return mockAgents;
};

export const getAgentById = (id: string): Agent | undefined => {
  return mockAgents.find(agent => agent.id === id);
};

export const getActiveAgents = (): Agent[] => {
  return mockAgents.filter(agent => agent.status);
};

export const getAgentTemplate = (template: AgentTemplate): Partial<Agent> => {
  return agentTemplates[template] || agentTemplates.empty;
};

export const getActionById = (id: string) => {
  return mockActions.find(action => action.id === id);
};

export const getAllActions = () => {
  return mockActions;
};
