import { Action } from "@mug/models/action";

// Mock actions
export const mockActions: Action[] = [
  {
    id: "action-1",
    name: "Enviar E-mail de Boas-vindas",
    description: "Envia um e-mail de boas-vindas para novos usuários",
    type: "email",
    category: "communication",
    config: {
      template: "welcome-email",
      subject: "Bem-vindo à nossa plataforma!",
      from: "<EMAIL>"
    },
    status: true,
    createdAt: "2023-05-10T14:30:00Z",
    updatedAt: "2023-11-15T09:45:00Z"
  },
  {
    id: "action-2",
    name: "Notificar Equipe de Vendas",
    description: "Envia uma notificação para a equipe de vendas quando um lead é qualificado",
    type: "notification",
    category: "notification",
    config: {
      recipients: ["sales-team"],
      message: "Novo lead qualificado: {{lead.name}}",
      channel: "slack"
    },
    status: true,
    createdAt: "2023-06-20T10:15:00Z",
    updatedAt: "2023-12-05T16:30:00Z"
  },
  {
    id: "action-3",
    name: "Atualizar Status do Lead",
    description: "Atualiza o status de um lead no CRM",
    type: "database",
    category: "data_processing",
    config: {
      entity: "lead",
      field: "status",
      value: "qualified"
    },
    status: true,
    createdAt: "2023-07-12T09:00:00Z",
    updatedAt: "2024-01-18T11:20:00Z"
  },
  {
    id: "action-4",
    name: "Integração com Stripe",
    description: "Cria um cliente no Stripe quando um novo cliente é criado no sistema",
    type: "integration",
    category: "integration",
    config: {
      service: "stripe",
      endpoint: "/v1/customers",
      method: "POST"
    },
    status: true,
    createdAt: "2023-08-05T13:45:00Z",
    updatedAt: "2024-02-10T15:30:00Z"
  },
  {
    id: "action-5",
    name: "Gerar Relatório de Vendas",
    description: "Gera um relatório de vendas e envia por e-mail",
    type: "custom",
    category: "reporting",
    config: {
      reportType: "sales",
      period: "monthly",
      recipients: ["<EMAIL>"]
    },
    status: false,
    createdAt: "2023-09-18T11:30:00Z",
    updatedAt: "2024-03-05T09:15:00Z"
  },
  {
    id: "action-6",
    name: "Webhook para Sistema Externo",
    description: "Envia dados para um sistema externo via webhook",
    type: "webhook",
    category: "integration",
    config: {
      url: "https://api.external-system.com/webhook",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer {{api_key}}"
      }
    },
    status: true,
    createdAt: "2023-10-25T14:00:00Z",
    updatedAt: "2024-04-12T10:45:00Z"
  },
  {
    id: "action-7",
    name: "Criar Tarefa de Acompanhamento",
    description: "Cria uma tarefa de acompanhamento para o responsável pelo lead",
    type: "database",
    category: "automation",
    config: {
      entity: "task",
      data: {
        title: "Acompanhamento de Lead",
        description: "Entrar em contato com {{lead.name}}",
        dueDate: "{{now+3d}}",
        assignee: "{{lead.owner}}"
      }
    },
    status: true,
    createdAt: "2023-11-15T09:45:00Z",
    updatedAt: "2024-01-20T11:45:00Z"
  },
  {
    id: "action-8",
    name: "Chamada de API Externa",
    description: "Realiza uma chamada para uma API externa",
    type: "api_call",
    category: "integration",
    config: {
      url: "https://api.example.com/v1/data",
      method: "GET",
      headers: {
        "Authorization": "Bearer {{api_key}}"
      }
    },
    status: true,
    createdAt: "2023-12-05T15:30:00Z",
    updatedAt: "2024-02-18T09:30:00Z"
  },
  {
    id: "action-9",
    name: "Gerar Arquivo PDF",
    description: "Gera um arquivo PDF com dados do sistema",
    type: "file",
    category: "utility",
    config: {
      template: "invoice-template",
      filename: "invoice-{{order.id}}.pdf",
      data: {
        orderId: "{{order.id}}",
        customerName: "{{order.customer.name}}",
        items: "{{order.items}}"
      }
    },
    status: false,
    createdAt: "2024-01-10T10:00:00Z",
    updatedAt: "2024-03-15T14:20:00Z"
  }
];

// Helper functions
export const getAllActions = (): Action[] => {
  return mockActions;
};

export const getActionById = (id: string): Action | undefined => {
  return mockActions.find(action => action.id === id);
};

export const getActiveActions = (): Action[] => {
  return mockActions.filter(action => action.status);
};

export const getActionsByCategory = (category: string): Action[] => {
  return mockActions.filter(action => action.category === category);
};
