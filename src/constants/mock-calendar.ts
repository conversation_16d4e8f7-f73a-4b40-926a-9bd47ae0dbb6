import { CalendarEvent, EventType, EventStatus, EventPriority, Attendee } from "@mug/models/calendar";
import { addDays, addHours, startOfDay, endOfDay, format, subDays } from "date-fns";

// Helper function to create a date string
const createDateString = (date: Date): string => {
  return date.toISOString();
};

// Helper function to create an event
const createEvent = (
  id: string,
  title: string,
  start: Date,
  end: Date,
  type: EventType,
  status: EventStatus,
  options: Partial<CalendarEvent> = {}
): CalendarEvent => {
  return {
    id,
    title,
    start: createDateString(start),
    end: createDateString(end),
    type,
    status,
    ...options,
  };
};

// Current date for reference
const now = new Date();
const today = startOfDay(now);

// Mock attendees
const mockAttendees: Attendee[] = [
  {
    id: "attendee-1",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "accepted",
  },
  {
    id: "attendee-2",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "accepted",
  },
  {
    id: "attendee-3",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "tentative",
  },
  {
    id: "attendee-4",
    name: "Ana Costa",
    email: "<EMAIL>",
    status: "declined",
  },
  {
    id: "attendee-5",
    name: "Carlos Ferreira",
    email: "<EMAIL>",
    status: "pending",
  },
];

// Mock calendar events
export const mockEvents: CalendarEvent[] = [
  // Today's events
  createEvent(
    "event-1",
    "Reunião com Cliente ABC",
    addHours(today, 10),
    addHours(today, 11),
    "meeting",
    "confirmed",
    {
      description: "Discussão sobre proposta comercial",
      location: "Sala de Reuniões 1",
      priority: "high",
      attendees: [mockAttendees[0], mockAttendees[1]],
      flowId: "flow-1",
      createdAt: createDateString(subDays(today, 5)),
      updatedAt: createDateString(subDays(today, 2)),
    }
  ),
  createEvent(
    "event-2",
    "Ligação de Follow-up",
    addHours(today, 14),
    addHours(today, 14.5),
    "call",
    "scheduled",
    {
      description: "Acompanhamento da proposta enviada",
      priority: "medium",
      flowId: "flow-1",
      recordId: "record-123",
      createdAt: createDateString(subDays(today, 3)),
      updatedAt: createDateString(subDays(today, 3)),
    }
  ),
  createEvent(
    "event-3",
    "Prazo: Envio de Proposta",
    startOfDay(today),
    endOfDay(today),
    "deadline",
    "completed",
    {
      description: "Enviar proposta final para o cliente XYZ",
      priority: "high",
      allDay: true,
      flowId: "flow-2",
      recordId: "record-456",
      createdAt: createDateString(subDays(today, 7)),
      updatedAt: createDateString(subDays(today, 1)),
    }
  ),

  // Tomorrow's events
  createEvent(
    "event-4",
    "Apresentação de Produto",
    addHours(addDays(today, 1), 11),
    addHours(addDays(today, 1), 12),
    "meeting",
    "confirmed",
    {
      description: "Demonstração do novo produto para cliente potencial",
      location: "Sala de Conferência",
      priority: "high",
      attendees: [mockAttendees[0], mockAttendees[1], mockAttendees[2]],
      flowId: "flow-1",
      recordId: "record-789",
      createdAt: createDateString(subDays(today, 10)),
      updatedAt: createDateString(subDays(today, 4)),
    }
  ),
  createEvent(
    "event-5",
    "Almoço com Parceiro",
    addHours(addDays(today, 1), 13),
    addHours(addDays(today, 1), 14.5),
    "meeting",
    "confirmed",
    {
      description: "Discussão sobre possíveis parcerias",
      location: "Restaurante Central",
      priority: "medium",
      attendees: [mockAttendees[1]],
      createdAt: createDateString(subDays(today, 6)),
      updatedAt: createDateString(subDays(today, 6)),
    }
  ),

  // Day after tomorrow
  createEvent(
    "event-6",
    "Treinamento da Equipe",
    addHours(addDays(today, 2), 9),
    addHours(addDays(today, 2), 17),
    "appointment",
    "scheduled",
    {
      description: "Treinamento sobre novos processos",
      location: "Sala de Treinamento",
      priority: "medium",
      attendees: [mockAttendees[0], mockAttendees[1], mockAttendees[2], mockAttendees[4]],
      createdAt: createDateString(subDays(today, 15)),
      updatedAt: createDateString(subDays(today, 8)),
    }
  ),

  // Next week
  createEvent(
    "event-7",
    "Reunião de Planejamento",
    addHours(addDays(today, 5), 10),
    addHours(addDays(today, 5), 12),
    "meeting",
    "scheduled",
    {
      description: "Planejamento estratégico para o próximo trimestre",
      location: "Sala de Reuniões 2",
      priority: "high",
      attendees: mockAttendees,
      createdAt: createDateString(subDays(today, 20)),
      updatedAt: createDateString(subDays(today, 20)),
    }
  ),
  createEvent(
    "event-8",
    "Webinar: Tendências de Mercado",
    addHours(addDays(today, 6), 15),
    addHours(addDays(today, 6), 16.5),
    "appointment",
    "scheduled",
    {
      description: "Webinar sobre tendências de mercado para 2024",
      url: "https://example.com/webinar",
      priority: "low",
      createdAt: createDateString(subDays(today, 12)),
      updatedAt: createDateString(subDays(today, 12)),
    }
  ),
  createEvent(
    "event-9",
    "Prazo: Relatório Mensal",
    startOfDay(addDays(today, 7)),
    endOfDay(addDays(today, 7)),
    "deadline",
    "pending",
    {
      description: "Entrega do relatório mensal de atividades",
      priority: "high",
      allDay: true,
      createdAt: createDateString(subDays(today, 25)),
      updatedAt: createDateString(subDays(today, 25)),
    }
  ),

  // This month
  createEvent(
    "event-10",
    "Conferência Anual",
    startOfDay(addDays(today, 14)),
    endOfDay(addDays(today, 16)),
    "appointment",
    "confirmed",
    {
      description: "Participação na conferência anual do setor",
      location: "Centro de Convenções",
      priority: "medium",
      allDay: true,
      createdAt: createDateString(subDays(today, 60)),
      updatedAt: createDateString(subDays(today, 30)),
    }
  ),
  createEvent(
    "event-11",
    "Revisão de Desempenho",
    addHours(addDays(today, 20), 14),
    addHours(addDays(today, 20), 15),
    "meeting",
    "scheduled",
    {
      description: "Revisão de desempenho trimestral",
      location: "Sala de Reuniões 3",
      priority: "medium",
      attendees: [mockAttendees[0], mockAttendees[4]],
      createdAt: createDateString(subDays(today, 45)),
      updatedAt: createDateString(subDays(today, 45)),
    }
  ),
];

// Helper functions
export const getAllEvents = (): CalendarEvent[] => {
  return mockEvents;
};

export const getEventById = (id: string): CalendarEvent | undefined => {
  return mockEvents.find(event => event.id === id);
};

export const getEventsByDateRange = (start: Date, end: Date): CalendarEvent[] => {
  return mockEvents.filter(event => {
    const eventStart = new Date(event.start);
    const eventEnd = new Date(event.end);
    return eventStart >= start && eventEnd <= end;
  });
};

export const getEventsByFlowId = (flowId: string): CalendarEvent[] => {
  return mockEvents.filter(event => event.flowId === flowId);
};

export const getEventsByRecordId = (recordId: string): CalendarEvent[] => {
  return mockEvents.filter(event => event.recordId === recordId);
};
