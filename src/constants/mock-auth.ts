import {
  AuthSession,
  MagicLinkToken,
  PasswordResetToken,
  UserInvitation
} from "@mug/models/auth";
import { User } from "@mug/models/user";
import { Language, Theme } from "@mug/models/core";
import { v4 as uuidv4 } from "uuid";

// Mock users
export const mockUsers: User[] = [
  {
    id: "user-1",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://ui-avatars.com/api/?name=JS&background=random",
    role: "admin",
    phone: "+55 11 98765-4321",
    timezone: "America/Sao_Paulo",
    language: Language.PORTUGUESE,
    theme: Theme.SYSTEM,
    createdAt: "2023-01-15T10:00:00Z",
    updatedAt: "2023-07-20T15:30:00Z",
    lastLogin: "2023-08-01T08:45:00Z",
    isEmailVerified: true,
    isPhoneVerified: false,
    twoFactorEnabled: true,
    twoFactorMethod: "app",
    googleConnected: true,
    googleProfile: {
      id: "google-123",
      email: "<EMAIL>",
      name: "<PERSON>",
      picture: "https://ui-avatars.com/api/?name=JS&background=random"
    },
    notificationPreferences: {
      email: true,
      push: true,
      inApp: true,
      marketing: false,
      digest: "daily"
    },
    securitySettings: {
      loginNotifications: true,
      unusualActivityAlerts: true,
      sessionTimeout: 30
    }
  },
  {
    id: "user-2",
    name: "Maria Santos",
    email: "<EMAIL>",
    avatar: "https://ui-avatars.com/api/?name=MS&background=random",
    role: "manager",
    phone: "+55 11 98765-4322",
    timezone: "America/Sao_Paulo",
    language: Language.PORTUGUESE,
    theme: Theme.LIGHT,
    createdAt: "2023-02-10T14:30:00Z",
    updatedAt: "2023-07-15T11:20:00Z",
    lastLogin: "2023-07-30T09:15:00Z",
    isEmailVerified: true,
    isPhoneVerified: true,
    twoFactorEnabled: false,
    googleConnected: false,
    notificationPreferences: {
      email: true,
      push: false,
      inApp: true,
      marketing: true,
      digest: "weekly"
    },
    securitySettings: {
      loginNotifications: false,
      unusualActivityAlerts: true,
      sessionTimeout: 60
    }
  },
  {
    id: "user-3",
    name: "Carlos Oliveira",
    email: "<EMAIL>",
    avatar: "https://ui-avatars.com/api/?name=CO&background=random",
    role: "member",
    timezone: "America/Sao_Paulo",
    language: Language.PORTUGUESE,
    theme: Theme.DARK,
    createdAt: "2023-03-05T09:15:00Z",
    updatedAt: "2023-06-20T16:45:00Z",
    lastLogin: "2023-07-28T14:30:00Z",
    isEmailVerified: true,
    isPhoneVerified: false,
    twoFactorEnabled: false,
    googleConnected: true,
    googleProfile: {
      id: "google-456",
      email: "<EMAIL>",
      name: "Carlos Oliveira",
      picture: "https://ui-avatars.com/api/?name=CO&background=random"
    },
    notificationPreferences: {
      email: true,
      push: true,
      inApp: true,
      marketing: false,
      digest: "never"
    },
    securitySettings: {
      loginNotifications: true,
      unusualActivityAlerts: false,
      sessionTimeout: 15
    }
  }
];

// Mock sessions
export const mockSessions: AuthSession[] = [
  {
    id: "session-1",
    userId: "user-1",
    token: "mock-token-1",
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
    createdAt: new Date().toISOString(),
    lastActiveAt: new Date().toISOString(),
    ip: "***********",
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
    device: "Desktop",
    requiresTwoFactor: true,
    twoFactorVerified: true
  }
];

// Mock magic link tokens
export const mockMagicLinkTokens: MagicLinkToken[] = [];

// Mock password reset tokens
export const mockPasswordResetTokens: PasswordResetToken[] = [];

// Mock user invitations
export const mockInvitations: UserInvitation[] = [
  {
    id: "invitation-1",
    email: "<EMAIL>",
    name: "Ana Costa",
    role: "member",
    token: "mock-invitation-token-1",
    invitedBy: "user-1",
    invitedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    expiresAt: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(), // 28 days from now
    accepted: false
  }
];

// Mock failed login attempts
export interface FailedLoginAttempt {
  email: string;
  ip: string;
  timestamp: string;
  count: number;
  blockedUntil?: string;
}

export const mockFailedLoginAttempts: FailedLoginAttempt[] = [];

// Helper functions
export const findUserByEmail = (email: string): User | undefined => {
  return mockUsers.find(user => user.email.toLowerCase() === email.toLowerCase());
};

export const findUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const findSessionByToken = (token: string): AuthSession | undefined => {
  return mockSessions.find(session => session.token === token);
};

export const findMagicLinkToken = (token: string): MagicLinkToken | undefined => {
  return mockMagicLinkTokens.find(t => t.token === token && !t.used && new Date(t.expiresAt) > new Date());
};

export const findPasswordResetToken = (token: string): PasswordResetToken | undefined => {
  return mockPasswordResetTokens.find(t => t.token === token && !t.used && new Date(t.expiresAt) > new Date());
};

export const findInvitationByToken = (token: string): UserInvitation | undefined => {
  return mockInvitations.find(
    invitation =>
      invitation.token === token &&
      !invitation.accepted &&
      new Date(invitation.expiresAt) > new Date()
  );
};

export const findInvitationByEmail = (email: string): UserInvitation | undefined => {
  return mockInvitations.find(
    invitation =>
      invitation.email.toLowerCase() === email.toLowerCase() &&
      !invitation.accepted &&
      new Date(invitation.expiresAt) > new Date()
  );
};

export const createSession = (userId: string, requiresTwoFactor: boolean = false): AuthSession => {
  const session: AuthSession = {
    id: `session-${uuidv4()}`,
    userId,
    token: `token-${uuidv4()}`,
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
    createdAt: new Date().toISOString(),
    lastActiveAt: new Date().toISOString(),
    requiresTwoFactor,
    twoFactorVerified: !requiresTwoFactor
  };

  mockSessions.push(session);
  return session;
};

export const createMagicLinkToken = (email: string): MagicLinkToken => {
  const token: MagicLinkToken = {
    token: `magic-${uuidv4()}`,
    email,
    expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(), // 15 minutes from now
    used: false
  };

  mockMagicLinkTokens.push(token);
  return token;
};

export const createPasswordResetToken = (userId: string): PasswordResetToken => {
  const token: PasswordResetToken = {
    token: `reset-${uuidv4()}`,
    userId,
    expiresAt: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
    used: false
  };

  mockPasswordResetTokens.push(token);
  return token;
};

export const createInvitation = (email: string, name: string | undefined, role: string, invitedBy: string): UserInvitation => {
  const invitation: UserInvitation = {
    id: `invitation-${uuidv4()}`,
    email,
    name,
    role,
    token: `invite-${uuidv4()}`,
    invitedBy,
    invitedAt: new Date().toISOString(),
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
    accepted: false
  };

  mockInvitations.push(invitation);
  return invitation;
};

export const recordFailedLoginAttempt = (email: string, ip: string): FailedLoginAttempt => {
  const now = new Date();
  let attempt = mockFailedLoginAttempts.find(a => a.email.toLowerCase() === email.toLowerCase() && a.ip === ip);

  if (attempt) {
    attempt.count += 1;
    attempt.timestamp = now.toISOString();

    // Block after 5 failed attempts
    if (attempt.count >= 5) {
      attempt.blockedUntil = new Date(now.getTime() + 30 * 60 * 1000).toISOString(); // 30 minutes from now
    }
  } else {
    attempt = {
      email,
      ip,
      timestamp: now.toISOString(),
      count: 1
    };
    mockFailedLoginAttempts.push(attempt);
  }

  return attempt;
};

export const isLoginBlocked = (email: string, ip: string): boolean => {
  const attempt = mockFailedLoginAttempts.find(a => a.email.toLowerCase() === email.toLowerCase() && a.ip === ip);

  if (!attempt || !attempt.blockedUntil) {
    return false;
  }

  return new Date(attempt.blockedUntil) > new Date();
};

export const resetFailedLoginAttempts = (email: string, ip: string): void => {
  const index = mockFailedLoginAttempts.findIndex(a => a.email.toLowerCase() === email.toLowerCase() && a.ip === ip);

  if (index !== -1) {
    mockFailedLoginAttempts.splice(index, 1);
  }
};

// Password validation
export const isPasswordStrong = (password: string): boolean => {
  // At least 8 characters
  if (password.length < 8) {
    return false;
  }

  // At least one uppercase letter
  if (!/[A-Z]/.test(password)) {
    return false;
  }

  // At least one lowercase letter
  if (!/[a-z]/.test(password)) {
    return false;
  }

  // At least one number
  if (!/[0-9]/.test(password)) {
    return false;
  }

  // At least one special character
  if (!/[^A-Za-z0-9]/.test(password)) {
    return false;
  }

  return true;
};

// Mock password hashing (in a real app, use bcrypt or similar)
export const hashPassword = (password: string): string => {
  return `hashed-${password}`;
};

export const verifyPassword = (password: string, hashedPassword: string): boolean => {
  return hashedPassword === `hashed-${password}`;
};

// Mock 2FA verification
export const verifyTwoFactorCode = (code: string, userId: string, method: string): boolean => {
  // In a real app, this would verify against TOTP, SMS code, or email code
  // For mock purposes, any 6-digit code is valid
  return /^\d{6}$/.test(code);
};
