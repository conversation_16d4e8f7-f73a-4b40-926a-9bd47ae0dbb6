export interface Timezone {
  value: string;
  label: string;
  offset: string;
}

export const timezones: Timezone[] = [
  {
    value: "America/Sao_Paulo",
    label: "São Paulo (GMT-3)",
    offset: "-03:00"
  },
  {
    value: "America/New_York",
    label: "New York (GMT-5/GMT-4)",
    offset: "-05:00/-04:00"
  },
  {
    value: "America/Chicago",
    label: "Chicago (GMT-6/GMT-5)",
    offset: "-06:00/-05:00"
  },
  {
    value: "America/Denver",
    label: "Denver (GMT-7/GMT-6)",
    offset: "-07:00/-06:00"
  },
  {
    value: "America/Los_Angeles",
    label: "Los Angeles (GMT-8/GMT-7)",
    offset: "-08:00/-07:00"
  },
  {
    value: "America/Anchorage",
    label: "Anchorage (GMT-9/GMT-8)",
    offset: "-09:00/-08:00"
  },
  {
    value: "Pacific/Honolulu",
    label: "Honolulu (GMT-10)",
    offset: "-10:00"
  },
  {
    value: "America/Argentina/Buenos_Aires",
    label: "Buenos Aires (GMT-3)",
    offset: "-03:00"
  },
  {
    value: "America/Mexico_City",
    label: "Mexico City (GMT-6/GMT-5)",
    offset: "-06:00/-05:00"
  },
  {
    value: "America/Bogota",
    label: "Bogota (GMT-5)",
    offset: "-05:00"
  },
  {
    value: "America/Santiago",
    label: "Santiago (GMT-4/GMT-3)",
    offset: "-04:00/-03:00"
  },
  {
    value: "Europe/London",
    label: "London (GMT/GMT+1)",
    offset: "+00:00/+01:00"
  },
  {
    value: "Europe/Paris",
    label: "Paris (GMT+1/GMT+2)",
    offset: "+01:00/+02:00"
  },
  {
    value: "Europe/Berlin",
    label: "Berlin (GMT+1/GMT+2)",
    offset: "+01:00/+02:00"
  },
  {
    value: "Europe/Madrid",
    label: "Madrid (GMT+1/GMT+2)",
    offset: "+01:00/+02:00"
  },
  {
    value: "Europe/Rome",
    label: "Rome (GMT+1/GMT+2)",
    offset: "+01:00/+02:00"
  },
  {
    value: "Europe/Moscow",
    label: "Moscow (GMT+3)",
    offset: "+03:00"
  },
  {
    value: "Asia/Dubai",
    label: "Dubai (GMT+4)",
    offset: "+04:00"
  },
  {
    value: "Asia/Kolkata",
    label: "Mumbai (GMT+5:30)",
    offset: "+05:30"
  },
  {
    value: "Asia/Shanghai",
    label: "Shanghai (GMT+8)",
    offset: "+08:00"
  },
  {
    value: "Asia/Tokyo",
    label: "Tokyo (GMT+9)",
    offset: "+09:00"
  },
  {
    value: "Australia/Sydney",
    label: "Sydney (GMT+10/GMT+11)",
    offset: "+10:00/+11:00"
  },
  {
    value: "Pacific/Auckland",
    label: "Auckland (GMT+12/GMT+13)",
    offset: "+12:00/+13:00"
  }
];
