import { 
  ChannelType, 
  ChannelStatus, 
  ConversationStatus, 
  PriorityLevel,
  MessageType,
  MessageStatus,
  CallDirection,
  CallStatus,
  OperatorStatus,
  Channel,
  Contact,
  Conversation,
  Message,
  Attachment,
  Call,
  MessageTemplate,
  Tag,
  OperatorSettings,
  InboxMetrics
} from "@mug/models/inbox";
import { v4 as uuidv4 } from "uuid";
import { 
  mockChannels, 
  mockContacts, 
  mockConversations, 
  mockMessages, 
  mockCalls, 
  mockMessageTemplates, 
  mockTags 
} from "./mock-inbox-data";

// Mock operator settings
export const mockOperatorSettings: OperatorSettings[] = [
  {
    userId: "user-1",
    status: OperatorStatus.AVAILABLE,
    notifications: {
      email: true,
      desktop: true,
      mobile: true
    },
    channels: ["channel-1", "channel-2", "channel-3", "channel-5", "channel-6"],
    signature: "<PERSON>\nAtendimento ao Cliente\nEmpresa ABC"
  },
  {
    userId: "user-2",
    status: OperatorStatus.BUSY,
    autoReply: {
      enabled: true,
      message: "Estou em atendimento no momento. Retornarei em breve."
    },
    notifications: {
      email: true,
      desktop: true,
      mobile: false
    },
    channels: ["channel-2", "channel-4", "channel-5"]
  },
  {
    userId: "user-3",
    status: OperatorStatus.AWAY,
    autoReply: {
      enabled: true,
      message: "Estou ausente no momento. Retornarei em breve."
    },
    notifications: {
      email: true,
      desktop: false,
      mobile: true
    },
    channels: ["channel-1", "channel-3", "channel-5"]
  }
];

// Mock metrics
export const mockMetrics: InboxMetrics = {
  period: {
    start: "2023-07-01T00:00:00Z",
    end: "2023-07-31T23:59:59Z"
  },
  conversations: {
    total: 120,
    new: 35,
    resolved: 85,
    averageResolutionTime: 240 // 4 hours
  },
  messages: {
    total: 850,
    sent: 420,
    received: 430,
    byChannel: {
      [ChannelType.WHATSAPP]: 350,
      [ChannelType.EMAIL]: 200,
      [ChannelType.CHAT]: 150,
      [ChannelType.TELEGRAM]: 50,
      [ChannelType.SMS]: 20,
      [ChannelType.FACEBOOK]: 30,
      [ChannelType.INSTAGRAM]: 25,
      [ChannelType.TWITTER]: 10,
      [ChannelType.LINKEDIN]: 5,
      [ChannelType.GOOGLE_BUSINESS]: 0,
      [ChannelType.INTERNAL]: 10,
      [ChannelType.VOIP]: 0
    }
  },
  calls: {
    total: 75,
    inbound: 45,
    outbound: 30,
    missed: 10,
    averageDuration: 420 // 7 minutes
  },
  performance: {
    averageResponseTime: 15, // 15 minutes
    averageFirstResponseTime: 10, // 10 minutes
    averageResolutionTime: 240 // 4 hours
  },
  operators: [
    {
      id: "user-1",
      name: "João Silva",
      conversationsHandled: 50,
      messagesHandled: 320,
      callsHandled: 40,
      averageResponseTime: 12,
      averageResolutionTime: 210
    },
    {
      id: "user-2",
      name: "Ana Costa",
      conversationsHandled: 40,
      messagesHandled: 280,
      callsHandled: 25,
      averageResponseTime: 18,
      averageResolutionTime: 270
    },
    {
      id: "user-3",
      name: "Carlos Oliveira",
      conversationsHandled: 30,
      messagesHandled: 250,
      callsHandled: 10,
      averageResponseTime: 20,
      averageResolutionTime: 300
    }
  ]
};

// Helper functions

// Find channel by ID
export const findChannelById = (id: string): Channel | undefined => {
  return mockChannels.find(channel => channel.id === id);
};

// Find channel by type
export const findChannelByType = (type: ChannelType): Channel | undefined => {
  return mockChannels.find(channel => channel.type === type);
};

// Find contact by ID
export const findContactById = (id: string): Contact | undefined => {
  return mockContacts.find(contact => contact.id === id);
};

// Find contact by email or phone
export const findContactByIdentifier = (identifier: string): Contact | undefined => {
  return mockContacts.find(
    contact => 
      contact.email === identifier || 
      contact.phone === identifier ||
      contact.channels.some(channel => channel.value === identifier)
  );
};

// Find conversation by ID
export const findConversationById = (id: string): Conversation | undefined => {
  return mockConversations.find(conversation => conversation.id === id);
};

// Find conversations by contact ID
export const findConversationsByContactId = (contactId: string): Conversation[] => {
  return mockConversations.filter(conversation => conversation.contactId === contactId);
};

// Find conversations by channel ID
export const findConversationsByChannelId = (channelId: string): Conversation[] => {
  return mockConversations.filter(conversation => conversation.channelId === channelId);
};

// Find conversations by assigned user
export const findConversationsByAssignedUser = (userId: string): Conversation[] => {
  return mockConversations.filter(conversation => conversation.assignedTo === userId);
};

// Find messages by conversation ID
export const findMessagesByConversationId = (conversationId: string): Message[] => {
  return mockMessages.filter(message => message.conversationId === conversationId);
};

// Find calls by conversation ID
export const findCallsByConversationId = (conversationId: string): Call[] => {
  return mockCalls.filter(call => call.conversationId === conversationId);
};

// Find calls by contact ID
export const findCallsByContactId = (contactId: string): Call[] => {
  return mockCalls.filter(call => call.contactId === contactId);
};

// Find template by ID
export const findTemplateById = (id: string): MessageTemplate | undefined => {
  return mockMessageTemplates.find(template => template.id === id);
};

// Find templates by channel type
export const findTemplatesByChannelType = (channelType: ChannelType): MessageTemplate[] => {
  return mockMessageTemplates.filter(template => template.channelTypes.includes(channelType));
};

// Find tag by ID
export const findTagById = (id: string): Tag | undefined => {
  return mockTags.find(tag => tag.id === id);
};

// Find operator settings by user ID
export const findOperatorSettingsByUserId = (userId: string): OperatorSettings | undefined => {
  return mockOperatorSettings.find(settings => settings.userId === userId);
};

// Create a new conversation
export const createConversation = (
  channelId: string,
  contactId: string,
  subject?: string,
  assignedTo?: string
): Conversation => {
  const channel = findChannelById(channelId);
  if (!channel) {
    throw new Error(`Channel with ID ${channelId} not found`);
  }
  
  const contact = findContactById(contactId);
  if (!contact) {
    throw new Error(`Contact with ID ${contactId} not found`);
  }
  
  const conversation: Conversation = {
    id: `conv-${uuidv4()}`,
    channelId,
    channelType: channel.type,
    contactId,
    subject,
    status: ConversationStatus.NEW,
    priority: PriorityLevel.MEDIUM,
    assignedTo,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastMessageAt: new Date().toISOString(),
    unreadCount: 0
  };
  
  mockConversations.push(conversation);
  return conversation;
};

// Create a new message
export const createMessage = (
  conversationId: string,
  content: string,
  senderId: string,
  senderType: "user" | "contact" | "system",
  type: MessageType = MessageType.TEXT,
  attachments?: Attachment[]
): Message => {
  const conversation = findConversationById(conversationId);
  if (!conversation) {
    throw new Error(`Conversation with ID ${conversationId} not found`);
  }
  
  const message: Message = {
    id: `msg-${uuidv4()}`,
    conversationId,
    channelId: conversation.channelId,
    channelType: conversation.channelType,
    type,
    content,
    senderId,
    senderType,
    status: MessageStatus.SENT,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    attachments
  };
  
  // Update conversation
  conversation.lastMessageAt = message.createdAt;
  conversation.lastMessagePreview = content.substring(0, 100);
  conversation.updatedAt = message.createdAt;
  
  if (senderType === "contact") {
    conversation.unreadCount += 1;
  }
  
  mockMessages.push(message);
  return message;
};

// Create a new call
export const createCall = (
  contactId: string,
  direction: CallDirection,
  phoneNumber: string,
  assignedTo?: string
): Call => {
  const contact = findContactById(contactId);
  if (!contact) {
    throw new Error(`Contact with ID ${contactId} not found`);
  }
  
  const voipChannel = findChannelByType(ChannelType.VOIP);
  if (!voipChannel) {
    throw new Error("VoIP channel not found");
  }
  
  // Find or create a conversation for this contact on the VoIP channel
  let conversation = mockConversations.find(
    conv => conv.contactId === contactId && conv.channelType === ChannelType.VOIP
  );
  
  if (!conversation) {
    conversation = createConversation(voipChannel.id, contactId, undefined, assignedTo);
  }
  
  const call: Call = {
    id: `call-${uuidv4()}`,
    conversationId: conversation.id,
    channelId: voipChannel.id,
    contactId,
    direction,
    status: direction === CallDirection.INBOUND ? CallStatus.RINGING : CallStatus.IN_PROGRESS,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    phoneNumber,
    assignedTo
  };
  
  mockCalls.push(call);
  return call;
};

// Update call status
export const updateCallStatus = (
  callId: string,
  status: CallStatus,
  duration?: number,
  notes?: string
): Call => {
  const call = mockCalls.find(c => c.id === callId);
  if (!call) {
    throw new Error(`Call with ID ${callId} not found`);
  }
  
  call.status = status;
  call.updatedAt = new Date().toISOString();
  
  if (status === CallStatus.COMPLETED) {
    call.endTime = new Date().toISOString();
    call.duration = duration || 0;
    call.recordingUrl = `https://example.com/recordings/${callId}.mp3`;
    
    if (notes) {
      call.notes = notes;
    }
    
    // Create a message in the conversation about the call
    const callDurationMinutes = Math.floor((duration || 0) / 60);
    createMessage(
      call.conversationId,
      `Chamada ${call.direction === CallDirection.INBOUND ? "recebida" : "realizada"}: ${callDurationMinutes} minutos`,
      call.assignedTo || "system",
      call.assignedTo ? "user" : "system",
      MessageType.SYSTEM
    );
  }
  
  return call;
};

// Create a new internal conversation (group)
export const createInternalConversation = (
  subject: string,
  participants: string[]
): Conversation => {
  const internalChannel = findChannelByType(ChannelType.INTERNAL);
  if (!internalChannel) {
    throw new Error("Internal channel not found");
  }
  
  // Use the first contact as a placeholder (not actually used for internal conversations)
  const placeholderContactId = mockContacts[0].id;
  
  const conversation: Conversation = {
    id: `conv-${uuidv4()}`,
    channelId: internalChannel.id,
    channelType: ChannelType.INTERNAL,
    contactId: placeholderContactId,
    subject,
    status: ConversationStatus.OPEN,
    priority: PriorityLevel.MEDIUM,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastMessageAt: new Date().toISOString(),
    unreadCount: 0,
    isGroup: true,
    participants
  };
  
  mockConversations.push(conversation);
  return conversation;
};

// Get metrics for a specific period
export const getMetricsForPeriod = (
  startDate: string,
  endDate: string
): InboxMetrics => {
  // In a real implementation, this would calculate metrics based on actual data
  // For mock purposes, we'll just return the mock metrics with updated period
  return {
    ...mockMetrics,
    period: {
      start: startDate,
      end: endDate
    }
  };
};

// Get metrics for a specific operator
export const getMetricsForOperator = (
  userId: string,
  startDate: string,
  endDate: string
): any => {
  // Find the operator in the mock metrics
  const operatorMetrics = mockMetrics.operators.find(op => op.id === userId);
  
  if (!operatorMetrics) {
    throw new Error(`Operator with ID ${userId} not found in metrics`);
  }
  
  // In a real implementation, this would calculate metrics based on actual data
  // For mock purposes, we'll just return the mock operator metrics
  return {
    period: {
      start: startDate,
      end: endDate
    },
    operator: operatorMetrics
  };
};
