import { 
  ChannelType, 
  ChannelStatus, 
  ConversationStatus, 
  PriorityLevel,
  MessageType,
  MessageStatus,
  CallDirection,
  CallStatus,
  OperatorStatus,
  Channel,
  Contact,
  Conversation,
  Message,
  Attachment,
  Call,
  MessageTemplate,
  Tag,
  OperatorSettings
} from "@mug/models/inbox";
import { v4 as uuidv4 } from "uuid";

// Mock channels
export const mockChannels: Channel[] = [
  {
    id: "channel-1",
    type: ChannelType.WHATSAPP,
    name: "WhatsApp Business",
    status: ChannelStatus.ACTIVE,
    createdAt: "2023-01-15T10:00:00Z",
    updatedAt: "2023-07-20T15:30:00Z",
    createdBy: "user-1",
    icon: "whatsapp",
    color: "#25D366",
    config: {
      phoneNumber: "+5511987654321",
      businessName: "Minha Empresa",
      webhookUrl: "https://api.example.com/webhooks/whatsapp"
    },
    isDefault: true,
    businessHours: {
      timezone: "America/Sao_Paulo",
      enabled: true,
      schedule: [
        { day: 1, start: "09:00", end: "18:00" },
        { day: 2, start: "09:00", end: "18:00" },
        { day: 3, start: "09:00", end: "18:00" },
        { day: 4, start: "09:00", end: "18:00" },
        { day: 5, start: "09:00", end: "18:00" }
      ],
      outOfOfficeMessage: "Estamos fora do horário de atendimento. Retornaremos em breve."
    }
  },
  {
    id: "channel-2",
    type: ChannelType.EMAIL,
    name: "Email Suporte",
    status: ChannelStatus.ACTIVE,
    createdAt: "2023-01-20T11:30:00Z",
    updatedAt: "2023-07-15T14:20:00Z",
    createdBy: "user-1",
    icon: "mail",
    color: "#4285F4",
    config: {
      email: "<EMAIL>",
      imapServer: "imap.example.com",
      smtpServer: "smtp.example.com"
    }
  },
  {
    id: "channel-3",
    type: ChannelType.CHAT,
    name: "Chat do Site",
    status: ChannelStatus.ACTIVE,
    createdAt: "2023-02-05T09:15:00Z",
    updatedAt: "2023-06-20T16:45:00Z",
    createdBy: "user-1",
    icon: "message-square",
    color: "#FF5722",
    config: {
      widgetId: "widget-123",
      welcomeMessage: "Olá! Como podemos ajudar?"
    }
  },
  {
    id: "channel-4",
    type: ChannelType.TELEGRAM,
    name: "Telegram",
    status: ChannelStatus.ACTIVE,
    createdAt: "2023-03-10T13:45:00Z",
    updatedAt: "2023-07-05T10:30:00Z",
    createdBy: "user-2",
    icon: "send",
    color: "#0088CC",
    config: {
      botToken: "bot-token-123",
      username: "@minhaempresa_bot"
    }
  },
  {
    id: "channel-5",
    type: ChannelType.INTERNAL,
    name: "Comunicação Interna",
    status: ChannelStatus.ACTIVE,
    createdAt: "2023-01-10T08:30:00Z",
    updatedAt: "2023-01-10T08:30:00Z",
    createdBy: "user-1",
    icon: "users",
    color: "#6C757D",
    config: {}
  },
  {
    id: "channel-6",
    type: ChannelType.VOIP,
    name: "Telefonia",
    status: ChannelStatus.ACTIVE,
    createdAt: "2023-04-15T11:20:00Z",
    updatedAt: "2023-07-10T09:15:00Z",
    createdBy: "user-1",
    icon: "phone",
    color: "#007BFF",
    config: {
      provider: "twilio",
      phoneNumber: "+5511912345678",
      sipEndpoint: "sip:<EMAIL>"
    }
  }
];

// Mock contacts
export const mockContacts: Contact[] = [
  {
    id: "contact-1",
    name: "João Silva",
    email: "<EMAIL>",
    phone: "+5511987654321",
    avatar: "https://ui-avatars.com/api/?name=JS&background=random",
    company: "Empresa ABC",
    title: "Gerente de Projetos",
    createdAt: "2023-01-15T10:00:00Z",
    updatedAt: "2023-07-20T15:30:00Z",
    channels: [
      { type: ChannelType.WHATSAPP, value: "+5511987654321", isDefault: true },
      { type: ChannelType.EMAIL, value: "<EMAIL>" }
    ],
    tags: ["cliente", "premium"],
    customFields: {
      lastPurchase: "2023-06-15",
      preferredContact: "whatsapp"
    },
    crmId: "crm-customer-123"
  },
  {
    id: "contact-2",
    name: "Maria Santos",
    email: "<EMAIL>",
    phone: "+5511976543210",
    avatar: "https://ui-avatars.com/api/?name=MS&background=random",
    company: "Empresa XYZ",
    title: "Diretora de Marketing",
    createdAt: "2023-02-10T14:30:00Z",
    updatedAt: "2023-07-15T11:20:00Z",
    channels: [
      { type: ChannelType.WHATSAPP, value: "+5511976543210" },
      { type: ChannelType.EMAIL, value: "<EMAIL>", isDefault: true },
      { type: ChannelType.TELEGRAM, value: "@mariasantos" }
    ],
    tags: ["cliente", "marketing"],
    crmId: "crm-customer-456"
  },
  {
    id: "contact-3",
    name: "Carlos Oliveira",
    email: "<EMAIL>",
    phone: "+5511965432109",
    avatar: "https://ui-avatars.com/api/?name=CO&background=random",
    company: "Empresa 123",
    title: "Desenvolvedor",
    createdAt: "2023-03-05T09:15:00Z",
    updatedAt: "2023-06-20T16:45:00Z",
    channels: [
      { type: ChannelType.WHATSAPP, value: "+5511965432109", isDefault: true },
      { type: ChannelType.EMAIL, value: "<EMAIL>" },
      { type: ChannelType.CHAT, value: "user-789" }
    ],
    tags: ["suporte", "técnico"],
    customFields: {
      product: "Software XYZ",
      subscription: "Premium"
    },
    crmId: "crm-customer-789"
  }
];

// Mock conversations
export const mockConversations: Conversation[] = [
  {
    id: "conv-1",
    channelId: "channel-1",
    channelType: ChannelType.WHATSAPP,
    contactId: "contact-1",
    subject: "Dúvida sobre produto",
    status: ConversationStatus.OPEN,
    priority: PriorityLevel.MEDIUM,
    assignedTo: "user-1",
    createdAt: "2023-07-20T10:30:00Z",
    updatedAt: "2023-07-20T15:45:00Z",
    lastMessageAt: "2023-07-20T15:45:00Z",
    lastMessagePreview: "Poderia me enviar mais informações sobre o produto?",
    unreadCount: 2,
    tags: ["suporte", "produto"]
  },
  {
    id: "conv-2",
    channelId: "channel-2",
    channelType: ChannelType.EMAIL,
    contactId: "contact-2",
    subject: "Proposta de parceria",
    status: ConversationStatus.PENDING,
    priority: PriorityLevel.HIGH,
    assignedTo: "user-2",
    createdAt: "2023-07-19T09:15:00Z",
    updatedAt: "2023-07-20T11:30:00Z",
    lastMessageAt: "2023-07-20T11:30:00Z",
    lastMessagePreview: "Aguardo seu retorno sobre a proposta enviada.",
    unreadCount: 0,
    tags: ["parceria", "negócios"]
  },
  {
    id: "conv-3",
    channelId: "channel-3",
    channelType: ChannelType.CHAT,
    contactId: "contact-3",
    subject: "Problema técnico",
    status: ConversationStatus.NEW,
    priority: PriorityLevel.URGENT,
    createdAt: "2023-07-20T16:00:00Z",
    updatedAt: "2023-07-20T16:00:00Z",
    lastMessageAt: "2023-07-20T16:00:00Z",
    lastMessagePreview: "Estou tendo problemas para acessar minha conta.",
    unreadCount: 1,
    tags: ["suporte", "técnico", "urgente"]
  },
  {
    id: "conv-4",
    channelId: "channel-5",
    channelType: ChannelType.INTERNAL,
    contactId: "contact-1", // Placeholder, not actually used for internal
    subject: "Alinhamento de equipe",
    status: ConversationStatus.OPEN,
    priority: PriorityLevel.MEDIUM,
    createdAt: "2023-07-18T14:30:00Z",
    updatedAt: "2023-07-20T10:15:00Z",
    lastMessageAt: "2023-07-20T10:15:00Z",
    lastMessagePreview: "Precisamos alinhar os próximos passos do projeto.",
    unreadCount: 3,
    isGroup: true,
    participants: ["user-1", "user-2", "user-3"]
  },
  {
    id: "conv-5",
    channelId: "channel-6",
    channelType: ChannelType.VOIP,
    contactId: "contact-2",
    status: ConversationStatus.RESOLVED,
    priority: PriorityLevel.MEDIUM,
    assignedTo: "user-1",
    createdAt: "2023-07-19T15:20:00Z",
    updatedAt: "2023-07-19T15:35:00Z",
    lastMessageAt: "2023-07-19T15:35:00Z",
    lastMessagePreview: "Chamada finalizada: 15 minutos",
    unreadCount: 0,
    tags: ["suporte", "telefone"]
  }
];

// Mock messages (first part)
export const mockMessages: Message[] = [
  // Conversation 1 (WhatsApp)
  {
    id: "msg-1",
    conversationId: "conv-1",
    channelId: "channel-1",
    channelType: ChannelType.WHATSAPP,
    type: MessageType.TEXT,
    content: "Olá, gostaria de saber mais informações sobre o produto XYZ.",
    senderId: "contact-1",
    senderType: "contact",
    status: MessageStatus.READ,
    createdAt: "2023-07-20T10:30:00Z",
    updatedAt: "2023-07-20T10:30:00Z",
    deliveredAt: "2023-07-20T10:30:05Z",
    readAt: "2023-07-20T10:32:00Z"
  },
  {
    id: "msg-2",
    conversationId: "conv-1",
    channelId: "channel-1",
    channelType: ChannelType.WHATSAPP,
    type: MessageType.TEXT,
    content: "Olá João! Claro, o produto XYZ é nossa solução premium para gerenciamento de projetos. Posso te enviar um PDF com mais detalhes.",
    senderId: "user-1",
    senderType: "user",
    status: MessageStatus.DELIVERED,
    createdAt: "2023-07-20T10:35:00Z",
    updatedAt: "2023-07-20T10:35:00Z",
    deliveredAt: "2023-07-20T10:35:10Z",
    readAt: "2023-07-20T10:40:00Z"
  },
  {
    id: "msg-3",
    conversationId: "conv-1",
    channelId: "channel-1",
    channelType: ChannelType.WHATSAPP,
    type: MessageType.FILE,
    content: "Aqui está o catálogo completo do produto.",
    senderId: "user-1",
    senderType: "user",
    status: MessageStatus.DELIVERED,
    createdAt: "2023-07-20T10:38:00Z",
    updatedAt: "2023-07-20T10:38:00Z",
    deliveredAt: "2023-07-20T10:38:15Z",
    readAt: "2023-07-20T10:42:00Z",
    attachments: [
      {
        id: "attach-1",
        messageId: "msg-3",
        type: "file",
        url: "https://example.com/files/product-xyz.pdf",
        name: "product-xyz.pdf",
        size: 2500000,
        mimeType: "application/pdf"
      }
    ]
  },
  {
    id: "msg-4",
    conversationId: "conv-1",
    channelId: "channel-1",
    channelType: ChannelType.WHATSAPP,
    type: MessageType.TEXT,
    content: "Obrigado! Vou dar uma olhada. Poderia me enviar mais informações sobre os preços?",
    senderId: "contact-1",
    senderType: "contact",
    status: MessageStatus.READ,
    createdAt: "2023-07-20T15:45:00Z",
    updatedAt: "2023-07-20T15:45:00Z",
    deliveredAt: "2023-07-20T15:45:05Z",
    readAt: "2023-07-20T15:50:00Z"
  },
  {
    id: "msg-5",
    conversationId: "conv-1",
    channelId: "channel-1",
    channelType: ChannelType.WHATSAPP,
    type: MessageType.NOTE,
    content: "Cliente interessado em preços. Verificar se podemos oferecer desconto para primeiro contrato.",
    senderId: "user-1",
    senderType: "user",
    status: MessageStatus.SENT,
    createdAt: "2023-07-20T15:52:00Z",
    updatedAt: "2023-07-20T15:52:00Z",
    isPrivate: true
  },
  
  // Conversation 2 (Email)
  {
    id: "msg-6",
    conversationId: "conv-2",
    channelId: "channel-2",
    channelType: ChannelType.EMAIL,
    type: MessageType.TEXT,
    content: "Prezados,\n\nEstou entrando em contato para propor uma parceria entre nossas empresas. Acredito que podemos criar uma sinergia interessante no mercado.\n\nSegue em anexo uma apresentação com mais detalhes.\n\nAtenciosamente,\nMaria Santos\nDiretora de Marketing\nEmpresa XYZ",
    senderId: "contact-2",
    senderType: "contact",
    status: MessageStatus.READ,
    createdAt: "2023-07-19T09:15:00Z",
    updatedAt: "2023-07-19T09:15:00Z",
    readAt: "2023-07-19T09:30:00Z",
    attachments: [
      {
        id: "attach-2",
        messageId: "msg-6",
        type: "file",
        url: "https://example.com/files/partnership-proposal.pptx",
        name: "partnership-proposal.pptx",
        size: 5800000,
        mimeType: "application/vnd.openxmlformats-officedocument.presentationml.presentation"
      }
    ]
  },
  {
    id: "msg-7",
    conversationId: "conv-2",
    channelId: "channel-2",
    channelType: ChannelType.EMAIL,
    type: MessageType.TEXT,
    content: "Cara Maria,\n\nObrigado pelo contato e pela proposta de parceria. Achei a apresentação muito interessante e gostaria de discutir mais a fundo.\n\nPodemos agendar uma reunião na próxima semana?\n\nAtenciosamente,\nAna Costa\nGerente de Parcerias",
    senderId: "user-2",
    senderType: "user",
    status: MessageStatus.DELIVERED,
    createdAt: "2023-07-19T14:20:00Z",
    updatedAt: "2023-07-19T14:20:00Z",
    deliveredAt: "2023-07-19T14:20:30Z",
    readAt: "2023-07-19T15:00:00Z"
  },
  {
    id: "msg-8",
    conversationId: "conv-2",
    channelId: "channel-2",
    channelType: ChannelType.EMAIL,
    type: MessageType.TEXT,
    content: "Olá Ana,\n\nFico feliz com o interesse. Podemos sim agendar uma reunião. Que tal na terça-feira, às 14h?\n\nAguardo seu retorno.\n\nAtenciosamente,\nMaria Santos",
    senderId: "contact-2",
    senderType: "contact",
    status: MessageStatus.READ,
    createdAt: "2023-07-20T11:30:00Z",
    updatedAt: "2023-07-20T11:30:00Z",
    readAt: "2023-07-20T11:45:00Z"
  },
  
  // Conversation 3 (Chat)
  {
    id: "msg-9",
    conversationId: "conv-3",
    channelId: "channel-3",
    channelType: ChannelType.CHAT,
    type: MessageType.TEXT,
    content: "Olá, estou tendo problemas para acessar minha conta. Aparece uma mensagem de erro quando tento fazer login.",
    senderId: "contact-3",
    senderType: "contact",
    status: MessageStatus.DELIVERED,
    createdAt: "2023-07-20T16:00:00Z",
    updatedAt: "2023-07-20T16:00:00Z"
  },
  
  // Conversation 4 (Internal)
  {
    id: "msg-10",
    conversationId: "conv-4",
    channelId: "channel-5",
    channelType: ChannelType.INTERNAL,
    type: MessageType.TEXT,
    content: "Pessoal, precisamos alinhar os próximos passos do projeto XYZ. Podemos fazer uma reunião amanhã?",
    senderId: "user-1",
    senderType: "user",
    status: MessageStatus.DELIVERED,
    createdAt: "2023-07-18T14:30:00Z",
    updatedAt: "2023-07-18T14:30:00Z",
    readAt: "2023-07-18T14:35:00Z"
  },
  {
    id: "msg-11",
    conversationId: "conv-4",
    channelId: "channel-5",
    channelType: ChannelType.INTERNAL,
    type: MessageType.TEXT,
    content: "Por mim está ok. Que horas?",
    senderId: "user-2",
    senderType: "user",
    status: MessageStatus.DELIVERED,
    createdAt: "2023-07-18T14:40:00Z",
    updatedAt: "2023-07-18T14:40:00Z",
    readAt: "2023-07-18T14:45:00Z"
  },
  {
    id: "msg-12",
    conversationId: "conv-4",
    channelId: "channel-5",
    channelType: ChannelType.INTERNAL,
    type: MessageType.TEXT,
    content: "Podemos fazer às 10h? @user-3 você consegue participar?",
    senderId: "user-1",
    senderType: "user",
    status: MessageStatus.DELIVERED,
    createdAt: "2023-07-18T15:00:00Z",
    updatedAt: "2023-07-18T15:00:00Z",
    readAt: "2023-07-18T15:10:00Z",
    mentions: ["user-3"]
  },
  {
    id: "msg-13",
    conversationId: "conv-4",
    channelId: "channel-5",
    channelType: ChannelType.INTERNAL,
    type: MessageType.TEXT,
    content: "10h está bom para mim também.",
    senderId: "user-3",
    senderType: "user",
    status: MessageStatus.DELIVERED,
    createdAt: "2023-07-18T15:15:00Z",
    updatedAt: "2023-07-18T15:15:00Z",
    readAt: "2023-07-18T15:20:00Z"
  },
  {
    id: "msg-14",
    conversationId: "conv-4",
    channelId: "channel-5",
    channelType: ChannelType.INTERNAL,
    type: MessageType.TEXT,
    content: "Pessoal, temos uma atualização importante sobre o cliente do projeto XYZ. Ele solicitou algumas alterações no escopo.",
    senderId: "user-2",
    senderType: "user",
    status: MessageStatus.DELIVERED,
    createdAt: "2023-07-20T10:15:00Z",
    updatedAt: "2023-07-20T10:15:00Z"
  }
];

// Mock calls
export const mockCalls: Call[] = [
  {
    id: "call-1",
    conversationId: "conv-5",
    channelId: "channel-6",
    contactId: "contact-2",
    direction: CallDirection.INBOUND,
    status: CallStatus.COMPLETED,
    startTime: "2023-07-19T15:20:00Z",
    endTime: "2023-07-19T15:35:00Z",
    duration: 900, // 15 minutes
    recordingUrl: "https://example.com/recordings/call-1.mp3",
    notes: "Cliente solicitou informações sobre novos produtos. Enviei catálogo por email após a chamada.",
    assignedTo: "user-1",
    createdAt: "2023-07-19T15:20:00Z",
    updatedAt: "2023-07-19T15:35:00Z",
    phoneNumber: "+5511976543210"
  },
  {
    id: "call-2",
    conversationId: "conv-1", // Added to existing WhatsApp conversation
    channelId: "channel-6",
    contactId: "contact-1",
    direction: CallDirection.OUTBOUND,
    status: CallStatus.COMPLETED,
    startTime: "2023-07-18T11:10:00Z",
    endTime: "2023-07-18T11:25:00Z",
    duration: 900, // 15 minutes
    recordingUrl: "https://example.com/recordings/call-2.mp3",
    notes: "Liguei para o cliente para explicar detalhes do produto. Cliente muito interessado.",
    assignedTo: "user-1",
    createdAt: "2023-07-18T11:10:00Z",
    updatedAt: "2023-07-18T11:25:00Z",
    phoneNumber: "+5511987654321"
  },
  {
    id: "call-3",
    conversationId: "conv-3", // Added to existing Chat conversation
    channelId: "channel-6",
    contactId: "contact-3",
    direction: CallDirection.INBOUND,
    status: CallStatus.MISSED,
    startTime: "2023-07-19T09:45:00Z",
    createdAt: "2023-07-19T09:45:00Z",
    updatedAt: "2023-07-19T09:45:00Z",
    phoneNumber: "+5511965432109"
  }
];

// Mock message templates
export const mockMessageTemplates: MessageTemplate[] = [
  {
    id: "template-1",
    name: "Saudação",
    content: "Olá {{name}}, como posso ajudar?",
    channelTypes: [ChannelType.WHATSAPP, ChannelType.CHAT, ChannelType.EMAIL],
    tags: ["saudação", "atendimento"],
    createdBy: "user-1",
    createdAt: "2023-01-15T10:00:00Z",
    updatedAt: "2023-01-15T10:00:00Z",
    isGlobal: true,
    variables: ["name"]
  },
  {
    id: "template-2",
    name: "Agradecimento",
    content: "Obrigado por entrar em contato, {{name}}! Estamos à disposição para qualquer dúvida adicional.",
    channelTypes: [ChannelType.WHATSAPP, ChannelType.CHAT, ChannelType.EMAIL],
    tags: ["agradecimento", "encerramento"],
    createdBy: "user-1",
    createdAt: "2023-01-15T10:05:00Z",
    updatedAt: "2023-01-15T10:05:00Z",
    isGlobal: true,
    variables: ["name"]
  },
  {
    id: "template-3",
    name: "Informações de Produto",
    content: "O produto {{product}} custa R$ {{price}} e está disponível para entrega em {{delivery_time}} dias úteis. Posso ajudar com mais alguma informação?",
    channelTypes: [ChannelType.WHATSAPP, ChannelType.CHAT, ChannelType.EMAIL],
    tags: ["produto", "preço"],
    createdBy: "user-2",
    createdAt: "2023-02-10T14:30:00Z",
    updatedAt: "2023-02-10T14:30:00Z",
    isGlobal: false,
    variables: ["product", "price", "delivery_time"]
  }
];

// Mock tags
export const mockTags: Tag[] = [
  {
    id: "tag-1",
    name: "Suporte",
    color: "#FF5722",
    createdAt: "2023-01-15T10:00:00Z",
    updatedAt: "2023-01-15T10:00:00Z",
    createdBy: "user-1"
  },
  {
    id: "tag-2",
    name: "Vendas",
    color: "#4CAF50",
    createdAt: "2023-01-15T10:05:00Z",
    updatedAt: "2023-01-15T10:05:00Z",
    createdBy: "user-1"
  },
  {
    id: "tag-3",
    name: "Urgente",
    color: "#F44336",
    createdAt: "2023-01-15T10:10:00Z",
    updatedAt: "2023-01-15T10:10:00Z",
    createdBy: "user-1"
  },
  {
    id: "tag-4",
    name: "Produto",
    color: "#2196F3",
    createdAt: "2023-02-10T14:30:00Z",
    updatedAt: "2023-02-10T14:30:00Z",
    createdBy: "user-2"
  },
  {
    id: "tag-5",
    name: "Feedback",
    color: "#9C27B0",
    createdAt: "2023-02-10T14:35:00Z",
    updatedAt: "2023-02-10T14:35:00Z",
    createdBy: "user-2"
  }
];
