import { v4 as uuidv4 } from 'uuid';

export type Priority = 'low' | 'medium' | 'high';

export type LaneRestriction = "none" | "only-left" | "only-right";

export type LaneModel =
  | "backlog"
  | "new"
  | "nurture"
  | "potential"
  | "opportunity"
  | "done"
  | "lost"
  | "won"
  | "pending"
  | "attention"
  | "default";

export interface User {
  id: string;
  name: string;
  avatar: string;
}

export interface MockItem {
  id: string;
  title: string;
  description?: string;
  priority?: Priority;
  dueDate?: string;
  createdAt?: string;
  updatedAt?: string;
  assignee?: User;
  collaborators?: User[];
  tags?: string[];
  progress?: number;
  comments?: number;
  attachments?: number;
}

export interface MockLane {
  id: string;
  title: string;
  description?: string;
  items: MockItem[];
  wipLimit?: number;
  allowDrop?: "none" | "only-left" | "only-right" | "new" | "all";
  allowCreate?: boolean;
  restriction?: LaneRestriction;
  model?: LaneModel;
  // Background color for the lane header
  backgroundColor?: string;
  // Conversion models (landing pages or forms) associated with this lane
  conversionModelIds?: string[]; // IDs of associated conversion models
  // Actions to be triggered when an item is created or moved to this lane
  actionIds?: string[]; // IDs of associated actions
}

// Mocked users for assignment
export const mockUsers: User[] = [
  { id: "user-1", name: "João Silva", avatar: "https://ui-avatars.com/api/?name=JS&background=random" },
  { id: "user-2", name: "Maria Oliveira", avatar: "https://ui-avatars.com/api/?name=MO&background=random" },
  { id: "user-3", name: "Pedro Santos", avatar: "https://ui-avatars.com/api/?name=PS&background=random" },
  { id: "user-4", name: "Ana Costa", avatar: "https://ui-avatars.com/api/?name=AC&background=random" },
  { id: "user-5", name: "Carlos Pereira", avatar: "https://ui-avatars.com/api/?name=CP&background=random" },
];

export const getMockLanesForFlow = (flowId: string): MockLane[] => {
  // Diferentes lanes para diferentes fluxos
  switch (flowId) {
    case "flow-1": // Fluxo de Vendas
      return [
        {
          id: "lane-1",
          title: "Leads",
          items: [
            {
              id: uuidv4(),
              title: "Empresa ABC",
              description: "Contato inicial via site",
              priority: "high",
              dueDate: "2023-12-15",
              createdAt: "2023-11-28",
              updatedAt: "2023-12-01",
              assignee: mockUsers[0],
              collaborators: [mockUsers[1], mockUsers[2]],
              tags: ["tech", "enterprise"],
              progress: 25,
              comments: 3,
              attachments: 2
            },
            {
              id: uuidv4(),
              title: "Empresa XYZ",
              description: "Indicação de cliente",
              priority: "medium",
              dueDate: "2023-12-20",
              createdAt: "2023-11-30",
              updatedAt: "2023-12-02",
              assignee: mockUsers[1],
              tags: ["retail"],
              progress: 10,
              comments: 1
            },
            {
              id: uuidv4(),
              title: "Empresa 123",
              description: "Contato via LinkedIn",
              priority: "low",
              createdAt: "2023-12-01",
              updatedAt: "2023-12-01",
              assignee: mockUsers[2],
              tags: ["finance"],
              progress: 5
            }
          ]
        },
        {
          id: "lane-2",
          title: "Qualificação",
          items: [
            {
              id: uuidv4(),
              title: "Empresa DEF",
              description: "Reunião inicial agendada",
              priority: "high",
              dueDate: "2023-12-10",
              createdAt: "2023-11-20",
              updatedAt: "2023-12-01",
              assignee: mockUsers[3],
              collaborators: [mockUsers[0]],
              tags: ["healthcare", "enterprise"],
              progress: 50,
              comments: 5,
              attachments: 1
            },
            {
              id: uuidv4(),
              title: "Empresa GHI",
              description: "Aguardando retorno",
              priority: "medium",
              dueDate: "2023-12-18",
              createdAt: "2023-11-25",
              updatedAt: "2023-11-30",
              assignee: mockUsers[4],
              tags: ["education"],
              progress: 30,
              comments: 2
            }
          ]
        },
        {
          id: "lane-3",
          title: "Proposta",
          items: [
            { id: uuidv4(), title: "Empresa JKL", description: "Proposta enviada" }
          ]
        },
        {
          id: "lane-4",
          title: "Negociação",
          items: [
            { id: uuidv4(), title: "Empresa MNO", description: "Em negociação de valores" }
          ]
        },
        {
          id: "lane-5",
          title: "Fechado",
          items: [
            { id: uuidv4(), title: "Empresa PQR", description: "Contrato assinado" },
            { id: uuidv4(), title: "Empresa STU", description: "Implementação iniciada" }
          ]
        }
      ];

    case "flow-2": // Fluxo de Marketing
      return [
        {
          id: "lane-1",
          title: "Ideias",
          items: [
            { id: uuidv4(), title: "Campanha Redes Sociais", description: "Promoção de verão" },
            { id: uuidv4(), title: "Email Marketing", description: "Lançamento de produto" }
          ]
        },
        {
          id: "lane-2",
          title: "Planejamento",
          items: [
            { id: uuidv4(), title: "Webinar", description: "Definição de tópicos e palestrantes" },
            { id: uuidv4(), title: "Blog Posts", description: "Calendário editorial Q3" }
          ]
        },
        {
          id: "lane-3",
          title: "Em Produção",
          items: [
            { id: uuidv4(), title: "Vídeos YouTube", description: "Série de tutoriais" }
          ]
        },
        {
          id: "lane-4",
          title: "Revisão",
          items: [
            { id: uuidv4(), title: "Infográficos", description: "Dados de mercado 2023" }
          ]
        },
        {
          id: "lane-5",
          title: "Publicado",
          items: [
            { id: uuidv4(), title: "Newsletter", description: "Edição de Junho" },
            { id: uuidv4(), title: "Case Study", description: "Cliente Enterprise" }
          ]
        }
      ];

    case "flow-3": // Fluxo de Onboarding
      return [
        {
          id: "lane-1",
          title: "Novo Cliente",
          items: [
            { id: uuidv4(), title: "Cliente A", description: "Contrato assinado em 01/06" },
            { id: uuidv4(), title: "Cliente B", description: "Contrato assinado em 15/06" }
          ]
        },
        {
          id: "lane-2",
          title: "Configuração",
          items: [
            { id: uuidv4(), title: "Cliente C", description: "Configuração de ambiente" }
          ]
        },
        {
          id: "lane-3",
          title: "Treinamento",
          items: [
            { id: uuidv4(), title: "Cliente D", description: "Treinamento agendado" }
          ]
        },
        {
          id: "lane-4",
          title: "Acompanhamento",
          items: [
            { id: uuidv4(), title: "Cliente E", description: "Primeira semana de uso" }
          ]
        },
        {
          id: "lane-5",
          title: "Concluído",
          items: [
            { id: uuidv4(), title: "Cliente F", description: "Onboarding finalizado" },
            { id: uuidv4(), title: "Cliente G", description: "Feedback coletado" }
          ]
        }
      ];

    default:
      // Lane padrão para outros fluxos
      return [
        {
          id: "lane-1",
          title: "Backlog",
          items: [
            { id: uuidv4(), title: "Item 1", description: "Descrição do item 1" },
            { id: uuidv4(), title: "Item 2", description: "Descrição do item 2" }
          ]
        },
        {
          id: "lane-2",
          title: "Em Progresso",
          items: [
            { id: uuidv4(), title: "Item 3", description: "Descrição do item 3" }
          ]
        },
        {
          id: "lane-3",
          title: "Concluído",
          items: [
            { id: uuidv4(), title: "Item 4", description: "Descrição do item 4" }
          ]
        }
      ];
  }
};
