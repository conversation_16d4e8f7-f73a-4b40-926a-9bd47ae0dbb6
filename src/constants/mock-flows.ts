import { Flow, FlowMode, FlowStats } from "@mug/models/flow";

export const mockFlows: Flow[] = [
  {
    id: "flow-1",
    name: "Fluxo de Vendas",
    description: "Fluxo para gerenciamento do processo de vendas",
    mode: FlowMode.CONVERT,
    stats: {
      totalItems: 24,
      completedItems: 18,
      conversionRate: 75,
      avgCycleTime: 5
    },
    tags: ["vendas", "comercial"],
    updatedAt: "2023-06-15T14:30:00Z",
    viewMode: "kanban"
  },
  {
    id: "flow-2",
    name: "Fluxo de Marketing",
    description: "Fluxo para gerenciamento de campanhas de marketing",
    mode: FlowMode.ATTRACT,
    stats: {
      totalItems: 32,
      completedItems: 28,
      conversionRate: 87,
      avgCycleTime: 3
    },
    tags: ["marketing", "campanhas"],
    updatedAt: "2023-07-20T10:15:00Z",
    viewMode: "list"
  },
  {
    id: "flow-3",
    name: "Fluxo de Onboarding",
    description: "Fluxo para onboarding de novos clientes",
    mode: FlowMode.ENGAGE,
    stats: {
      totalItems: 15,
      completedItems: 12,
      conversionRate: 80,
      avgCycleTime: 7
    },
    tags: ["onboarding", "clientes"],
    updatedAt: "2023-08-05T09:45:00Z",
    viewMode: "kanban"
  },
  {
    id: "flow-4",
    name: "Fluxo de Suporte",
    description: "Fluxo para gerenciamento de tickets de suporte",
    mode: FlowMode.DELIGHT,
    stats: {
      totalItems: 48,
      completedItems: 42,
      conversionRate: 87,
      avgCycleTime: 2
    },
    tags: ["suporte", "atendimento"],
    updatedAt: "2023-09-10T16:20:00Z",
    viewMode: "list"
  },
  {
    id: "flow-5",
    name: "Fluxo de Análise de Dados",
    description: "Fluxo para análise de dados de performance",
    mode: FlowMode.ANALYZE,
    stats: {
      totalItems: 12,
      completedItems: 8,
      conversionRate: 66,
      avgCycleTime: 10
    },
    tags: ["análise", "dados", "performance"],
    updatedAt: "2023-10-25T11:30:00Z",
    viewMode: "kanban"
  },
  {
    id: "flow-6",
    name: "Fluxo de Fechamento",
    description: "Fluxo para fechamento de negócios",
    mode: FlowMode.CLOSE,
    isArchived: true,
    stats: {
      totalItems: 18,
      completedItems: 15,
      conversionRate: 83,
      avgCycleTime: 4
    },
    tags: ["fechamento", "negócios"],
    updatedAt: "2023-11-15T14:00:00Z",
    viewMode: "list"
  }
];
