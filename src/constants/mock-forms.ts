import { Form, FormField, FormTemplate } from "@mug/models/form";
import { v4 as uuidv4 } from "uuid";

// Mock form templates
export const formTemplates: Record<FormTemplate, Partial<Form>> = {
  "blank": {
    name: "Formulário em Branco",
    description: "Comece do zero com um formulário em branco",
    fields: [],
    settings: {
      submitButtonText: "Enviar",
      successMessage: "Formulário enviado com sucesso!",
      errorMessage: "Ocorreu um erro ao enviar o formulário. Por favor, tente novamente.",
      captcha: false,
      storeSubmissions: true,
      sendNotifications: false
    },
    conversionMode: "lead"
  },
  "contact": {
    name: "Formulário de Contato",
    description: "Template para formulário de contato",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome",
        placeholder: "Digite seu nome",
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: true,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "phone",
        label: "Telefone",
        placeholder: "Digite seu telefone",
        required: false,
        order: 2
      },
      {
        id: uuidv4(),
        type: "textarea",
        label: "Mensagem",
        placeholder: "Digite sua mensagem",
        required: true,
        order: 3
      }
    ],
    settings: {
      submitButtonText: "Enviar Mensagem",
      successMessage: "Sua mensagem foi enviada com sucesso! Em breve entraremos em contato.",
      errorMessage: "Ocorreu um erro ao enviar sua mensagem. Por favor, tente novamente.",
      captcha: true,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "lead"
  },
  "registration": {
    name: "Formulário de Inscrição",
    description: "Template para formulário de inscrição em eventos",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome Completo",
        placeholder: "Digite seu nome completo",
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: true,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "phone",
        label: "Telefone",
        placeholder: "Digite seu telefone",
        required: true,
        order: 2
      },
      {
        id: uuidv4(),
        type: "text",
        label: "Empresa",
        placeholder: "Digite o nome da sua empresa",
        required: false,
        order: 3
      },
      {
        id: uuidv4(),
        type: "text",
        label: "Cargo",
        placeholder: "Digite seu cargo",
        required: false,
        order: 4
      },
      {
        id: uuidv4(),
        type: "select",
        label: "Como ficou sabendo do evento?",
        required: false,
        options: [
          { label: "Redes Sociais", value: "social_media" },
          { label: "E-mail Marketing", value: "email" },
          { label: "Indicação", value: "referral" },
          { label: "Pesquisa na Internet", value: "search" },
          { label: "Outro", value: "other" }
        ],
        order: 5
      }
    ],
    settings: {
      submitButtonText: "Confirmar Inscrição",
      successMessage: "Sua inscrição foi confirmada com sucesso! Em breve você receberá um e-mail com mais informações.",
      errorMessage: "Ocorreu um erro ao processar sua inscrição. Por favor, tente novamente.",
      captcha: true,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "subscribe"
  },
  "survey": {
    name: "Formulário de Pesquisa",
    description: "Template para formulário de pesquisa de satisfação",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome",
        placeholder: "Digite seu nome",
        required: false,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: false,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "radio",
        label: "Como você avalia nosso produto/serviço?",
        required: true,
        options: [
          { label: "Excelente", value: "excellent" },
          { label: "Bom", value: "good" },
          { label: "Regular", value: "regular" },
          { label: "Ruim", value: "bad" },
          { label: "Péssimo", value: "terrible" }
        ],
        order: 2
      },
      {
        id: uuidv4(),
        type: "checkbox",
        label: "Quais aspectos você mais gosta?",
        required: false,
        options: [
          { label: "Qualidade", value: "quality" },
          { label: "Preço", value: "price" },
          { label: "Atendimento", value: "service" },
          { label: "Prazo de Entrega", value: "delivery" },
          { label: "Suporte", value: "support" }
        ],
        order: 3
      },
      {
        id: uuidv4(),
        type: "textarea",
        label: "Sugestões de Melhoria",
        placeholder: "Digite suas sugestões",
        required: false,
        order: 4
      }
    ],
    settings: {
      submitButtonText: "Enviar Respostas",
      successMessage: "Suas respostas foram enviadas com sucesso! Agradecemos sua participação.",
      errorMessage: "Ocorreu um erro ao enviar suas respostas. Por favor, tente novamente.",
      captcha: false,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "lead"
  },
  "feedback": {
    name: "Formulário de Feedback",
    description: "Template para formulário de feedback",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome",
        placeholder: "Digite seu nome",
        required: false,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: true,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "select",
        label: "Tipo de Feedback",
        required: true,
        options: [
          { label: "Elogio", value: "praise" },
          { label: "Sugestão", value: "suggestion" },
          { label: "Crítica", value: "criticism" },
          { label: "Dúvida", value: "question" },
          { label: "Outro", value: "other" }
        ],
        order: 2
      },
      {
        id: uuidv4(),
        type: "textarea",
        label: "Mensagem",
        placeholder: "Digite sua mensagem",
        required: true,
        order: 3
      }
    ],
    settings: {
      submitButtonText: "Enviar Feedback",
      successMessage: "Seu feedback foi enviado com sucesso! Agradecemos sua contribuição.",
      errorMessage: "Ocorreu um erro ao enviar seu feedback. Por favor, tente novamente.",
      captcha: true,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "lead"
  },
  "order": {
    name: "Formulário de Pedido",
    description: "Template para formulário de pedido de produtos",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome Completo",
        placeholder: "Digite seu nome completo",
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: true,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "phone",
        label: "Telefone",
        placeholder: "Digite seu telefone",
        required: true,
        order: 2
      },
      {
        id: uuidv4(),
        type: "text",
        label: "Endereço",
        placeholder: "Digite seu endereço completo",
        required: true,
        order: 3
      },
      {
        id: uuidv4(),
        type: "select",
        label: "Produto",
        required: true,
        options: [
          { label: "Produto A - R$ 99,00", value: "product_a" },
          { label: "Produto B - R$ 199,00", value: "product_b" },
          { label: "Produto C - R$ 299,00", value: "product_c" }
        ],
        order: 4
      },
      {
        id: uuidv4(),
        type: "number",
        label: "Quantidade",
        placeholder: "Digite a quantidade",
        required: true,
        validation: {
          min: 1,
          max: 10
        },
        order: 5
      },
      {
        id: uuidv4(),
        type: "textarea",
        label: "Observações",
        placeholder: "Digite suas observações",
        required: false,
        order: 6
      }
    ],
    settings: {
      submitButtonText: "Finalizar Pedido",
      successMessage: "Seu pedido foi realizado com sucesso! Em breve você receberá um e-mail com os detalhes.",
      errorMessage: "Ocorreu um erro ao processar seu pedido. Por favor, tente novamente.",
      captcha: true,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "product"
  }
};

// Mock forms
export const mockForms: Form[] = [
  {
    id: "form-1",
    name: "Formulário de Contato - Software XYZ",
    description: "Formulário para contato sobre o Software XYZ",
    slug: "contato-software-xyz",
    template: "contact",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome",
        placeholder: "Digite seu nome",
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: true,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "phone",
        label: "Telefone",
        placeholder: "Digite seu telefone",
        required: false,
        order: 2
      },
      {
        id: uuidv4(),
        type: "select",
        label: "Plano de Interesse",
        required: true,
        options: [
          { label: "Básico", value: "basic" },
          { label: "Profissional", value: "professional" },
          { label: "Enterprise", value: "enterprise" }
        ],
        order: 3
      },
      {
        id: uuidv4(),
        type: "textarea",
        label: "Mensagem",
        placeholder: "Digite sua mensagem",
        required: true,
        order: 4
      }
    ],
    settings: {
      submitButtonText: "Solicitar Demonstração",
      successMessage: "Sua solicitação foi enviada com sucesso! Em breve entraremos em contato para agendar uma demonstração.",
      errorMessage: "Ocorreu um erro ao enviar sua solicitação. Por favor, tente novamente.",
      captcha: true,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "product",
    status: true,
    laneId: "lane-1",
    createdAt: "2023-05-10T14:30:00Z",
    updatedAt: "2023-11-15T09:45:00Z",
    publishedAt: "2023-05-15T10:00:00Z"
  },
  {
    id: "form-2",
    name: "Download de E-book - Marketing Digital",
    description: "Formulário para download do e-book de marketing digital",
    slug: "download-ebook-marketing",
    template: "contact",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome",
        placeholder: "Digite seu nome",
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: true,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "text",
        label: "Empresa",
        placeholder: "Digite o nome da sua empresa",
        required: false,
        order: 2
      },
      {
        id: uuidv4(),
        type: "select",
        label: "Área de Atuação",
        required: false,
        options: [
          { label: "Marketing", value: "marketing" },
          { label: "Vendas", value: "sales" },
          { label: "TI", value: "it" },
          { label: "RH", value: "hr" },
          { label: "Outro", value: "other" }
        ],
        order: 3
      }
    ],
    settings: {
      submitButtonText: "Baixar E-book",
      successMessage: "Seu download está pronto! Verifique seu e-mail para acessar o e-book.",
      errorMessage: "Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente.",
      redirectUrl: "/obrigado-ebook",
      captcha: true,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "lead",
    status: true,
    laneId: "lane-2",
    createdAt: "2023-06-20T10:15:00Z",
    updatedAt: "2023-12-05T16:30:00Z",
    publishedAt: "2023-06-25T09:00:00Z"
  },
  {
    id: "form-3",
    name: "Inscrição - Webinar Tendências 2024",
    description: "Formulário de inscrição para o webinar sobre tendências de mercado",
    slug: "inscricao-webinar-tendencias",
    template: "registration",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome Completo",
        placeholder: "Digite seu nome completo",
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: true,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "phone",
        label: "Telefone",
        placeholder: "Digite seu telefone",
        required: true,
        order: 2
      },
      {
        id: uuidv4(),
        type: "text",
        label: "Empresa",
        placeholder: "Digite o nome da sua empresa",
        required: false,
        order: 3
      },
      {
        id: uuidv4(),
        type: "text",
        label: "Cargo",
        placeholder: "Digite seu cargo",
        required: false,
        order: 4
      },
      {
        id: uuidv4(),
        type: "select",
        label: "Como ficou sabendo do webinar?",
        required: false,
        options: [
          { label: "Redes Sociais", value: "social_media" },
          { label: "E-mail Marketing", value: "email" },
          { label: "Indicação", value: "referral" },
          { label: "Pesquisa na Internet", value: "search" },
          { label: "Outro", value: "other" }
        ],
        order: 5
      }
    ],
    settings: {
      submitButtonText: "Confirmar Inscrição",
      successMessage: "Sua inscrição foi confirmada com sucesso! Em breve você receberá um e-mail com o link de acesso ao webinar.",
      errorMessage: "Ocorreu um erro ao processar sua inscrição. Por favor, tente novamente.",
      redirectUrl: "/obrigado-inscricao",
      captcha: true,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "subscribe",
    status: true,
    laneId: "lane-3",
    createdAt: "2023-07-12T09:00:00Z",
    updatedAt: "2024-01-18T11:20:00Z",
    publishedAt: "2023-07-15T14:00:00Z"
  },
  {
    id: "form-4",
    name: "Formulário de Contato - Empresa ABC",
    description: "Formulário de contato geral da Empresa ABC",
    slug: "contato-empresa-abc",
    template: "contact",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome",
        placeholder: "Digite seu nome",
        required: true,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: true,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "phone",
        label: "Telefone",
        placeholder: "Digite seu telefone",
        required: false,
        order: 2
      },
      {
        id: uuidv4(),
        type: "select",
        label: "Assunto",
        required: true,
        options: [
          { label: "Informações sobre Produtos", value: "products" },
          { label: "Suporte Técnico", value: "support" },
          { label: "Comercial", value: "sales" },
          { label: "Financeiro", value: "financial" },
          { label: "Outro", value: "other" }
        ],
        order: 3
      },
      {
        id: uuidv4(),
        type: "textarea",
        label: "Mensagem",
        placeholder: "Digite sua mensagem",
        required: true,
        order: 4
      }
    ],
    settings: {
      submitButtonText: "Enviar Mensagem",
      successMessage: "Sua mensagem foi enviada com sucesso! Em breve entraremos em contato.",
      errorMessage: "Ocorreu um erro ao enviar sua mensagem. Por favor, tente novamente.",
      captcha: true,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "lead",
    status: true,
    laneId: "lane-4",
    createdAt: "2023-08-05T13:45:00Z",
    updatedAt: "2024-02-10T15:30:00Z",
    publishedAt: "2023-08-10T10:00:00Z"
  },
  {
    id: "form-5",
    name: "Pesquisa de Satisfação",
    description: "Formulário para pesquisa de satisfação de clientes",
    slug: "pesquisa-satisfacao",
    template: "survey",
    fields: [
      {
        id: uuidv4(),
        type: "text",
        label: "Nome",
        placeholder: "Digite seu nome",
        required: false,
        order: 0
      },
      {
        id: uuidv4(),
        type: "email",
        label: "E-mail",
        placeholder: "Digite seu e-mail",
        required: false,
        validation: {
          type: "email"
        },
        order: 1
      },
      {
        id: uuidv4(),
        type: "radio",
        label: "Como você avalia nosso produto/serviço?",
        required: true,
        options: [
          { label: "Excelente", value: "excellent" },
          { label: "Bom", value: "good" },
          { label: "Regular", value: "regular" },
          { label: "Ruim", value: "bad" },
          { label: "Péssimo", value: "terrible" }
        ],
        order: 2
      },
      {
        id: uuidv4(),
        type: "checkbox",
        label: "Quais aspectos você mais gosta?",
        required: false,
        options: [
          { label: "Qualidade", value: "quality" },
          { label: "Preço", value: "price" },
          { label: "Atendimento", value: "service" },
          { label: "Prazo de Entrega", value: "delivery" },
          { label: "Suporte", value: "support" }
        ],
        order: 3
      },
      {
        id: uuidv4(),
        type: "textarea",
        label: "Sugestões de Melhoria",
        placeholder: "Digite suas sugestões",
        required: false,
        order: 4
      }
    ],
    settings: {
      submitButtonText: "Enviar Respostas",
      successMessage: "Suas respostas foram enviadas com sucesso! Agradecemos sua participação.",
      errorMessage: "Ocorreu um erro ao enviar suas respostas. Por favor, tente novamente.",
      captcha: false,
      storeSubmissions: true,
      sendNotifications: true,
      notificationEmails: ["<EMAIL>"]
    },
    conversionMode: "lead",
    status: true,
    createdAt: "2023-09-18T11:30:00Z",
    updatedAt: "2024-03-05T09:15:00Z",
    publishedAt: "2023-09-20T14:00:00Z"
  }
];

// Helper functions
export const getAllForms = (): Form[] => {
  return mockForms;
};

export const getFormById = (id: string): Form | undefined => {
  return mockForms.find(form => form.id === id);
};

export const getFormBySlug = (slug: string): Form | undefined => {
  return mockForms.find(form => form.slug === slug);
};

export const getActiveForms = (): Form[] => {
  return mockForms.filter(form => form.status);
};

export const getFormTemplate = (template: FormTemplate): Partial<Form> => {
  return formTemplates[template] || formTemplates.blank;
};
