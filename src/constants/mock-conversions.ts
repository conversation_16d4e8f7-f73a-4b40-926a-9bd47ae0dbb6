// Tipos de conversão
export type ConversionType = 'landing-page' | 'form';

// Interface para modelos de conversão
export interface ConversionModel {
  id: string;
  name: string;
  description: string;
  type: ConversionType;
}

// Dados fictícios para modelos de landing pages
export const mockLandingPages: ConversionModel[] = [
  {
    id: 'lp-1',
    name: 'Landing Page de Produto',
    description: 'Página de destino para apresentação de produto',
    type: 'landing-page'
  },
  {
    id: 'lp-2',
    name: 'Landing Page de Serviço',
    description: 'Página de destino para oferta de serviço',
    type: 'landing-page'
  },
  {
    id: 'lp-3',
    name: 'Landing Page de Evento',
    description: 'Página de destino para inscrição em evento',
    type: 'landing-page'
  }
];

// Dados fictícios para modelos de formulários
export const mockForms: ConversionModel[] = [
  {
    id: 'form-1',
    name: 'Formulário de Contato',
    description: 'Formulário para solicitação de contato',
    type: 'form'
  },
  {
    id: 'form-2',
    name: 'Formulário de Orçamento',
    description: 'Formulário para solicitação de orçamento',
    type: 'form'
  },
  {
    id: 'form-3',
    name: 'Formulário de Inscrição',
    description: 'Formulário para inscrição em newsletter',
    type: 'form'
  }
];

// Função para obter todos os modelos de conversão
export const getAllConversionModels = (): ConversionModel[] => {
  return [...mockLandingPages, ...mockForms];
};

// Função para obter um modelo de conversão por ID
export const getConversionModelById = (id: string): ConversionModel | undefined => {
  return getAllConversionModels().find(model => model.id === id);
};
