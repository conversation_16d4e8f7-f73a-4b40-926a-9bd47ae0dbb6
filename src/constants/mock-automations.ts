import { Automation } from "@mug/models/automation";

// Mock automations
export const mockAutomations: Automation[] = [
  {
    id: "automation-1",
    name: "Notificação de Novo Lead",
    description: "Envia uma notificação quando um novo lead é criado no sistema",
    trigger: {
      id: "trigger-1",
      type: "record_created",
      config: {
        model: "lead"
      }
    },
    actions: [
      {
        id: "action-1",
        type: "send_notification",
        config: {
          recipients: ["user-1", "user-2"],
          message: "Um novo lead foi criado: {{record.name}}"
        }
      },
      {
        id: "action-2",
        type: "send_email",
        config: {
          template: "new-lead-notification",
          to: "{{assignee.email}}"
        }
      }
    ],
    status: true,
    createdAt: "2023-06-15T10:30:00Z",
    updatedAt: "2023-12-10T14:45:00Z"
  },
  {
    id: "automation-2",
    name: "Atualização de Status de Oportunidade",
    description: "Atualiza o status de uma oportunidade quando determinadas condições são atendidas",
    trigger: {
      id: "trigger-2",
      type: "field_changed",
      config: {
        model: "opportunity",
        field: "value"
      }
    },
    actions: [
      {
        id: "action-3",
        type: "update_record",
        config: {
          model: "opportunity",
          fields: {
            status: "qualified",
            priority: "high"
          }
        }
      }
    ],
    conditions: [
      {
        id: "condition-1",
        field: "value",
        operator: "greater_than",
        value: 10000
      }
    ],
    status: true,
    createdAt: "2023-07-20T09:15:00Z",
    updatedAt: "2024-01-05T11:30:00Z"
  },
  {
    id: "automation-3",
    name: "Atribuição Automática de Tarefas",
    description: "Atribui tarefas automaticamente com base na carga de trabalho da equipe",
    trigger: {
      id: "trigger-3",
      type: "record_created",
      config: {
        model: "task"
      }
    },
    actions: [
      {
        id: "action-4",
        type: "assign_task",
        config: {
          assignee: "{{least_busy_user}}",
          notify: true
        }
      }
    ],
    status: false,
    createdAt: "2023-08-05T13:45:00Z",
    updatedAt: "2023-11-20T16:20:00Z"
  },
  {
    id: "automation-4",
    name: "Lembrete de Acompanhamento",
    description: "Envia um lembrete para acompanhamento de clientes após 7 dias sem interação",
    trigger: {
      id: "trigger-4",
      type: "scheduled",
      config: {
        frequency: "daily",
        time: "09:00"
      }
    },
    actions: [
      {
        id: "action-5",
        type: "send_email",
        config: {
          template: "follow-up-reminder",
          to: "{{owner.email}}"
        }
      }
    ],
    conditions: [
      {
        id: "condition-2",
        field: "last_interaction",
        operator: "greater_than",
        value: "7d"
      },
      {
        id: "condition-3",
        field: "status",
        operator: "not_equals",
        value: "closed"
      }
    ],
    status: true,
    createdAt: "2023-09-12T11:00:00Z",
    updatedAt: "2024-02-18T09:30:00Z"
  },
  {
    id: "automation-5",
    name: "Integração com Webhook Externo",
    description: "Envia dados para um sistema externo quando um formulário é submetido",
    trigger: {
      id: "trigger-5",
      type: "form_submitted",
      config: {
        formId: "contact-form"
      }
    },
    actions: [
      {
        id: "action-6",
        type: "execute_webhook",
        config: {
          url: "https://api.external-system.com/webhook",
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": "Bearer {{api_key}}"
          }
        }
      }
    ],
    status: true,
    createdAt: "2023-10-25T14:20:00Z",
    updatedAt: "2024-03-10T10:15:00Z"
  },
  {
    id: "automation-6",
    name: "Execução de Agente para Qualificação",
    description: "Executa um agente para qualificar leads automaticamente",
    trigger: {
      id: "trigger-6",
      type: "record_created",
      config: {
        model: "lead"
      }
    },
    actions: [
      {
        id: "action-7",
        type: "run_agent",
        config: {
          agentId: "agent-3",
          input: {
            leadId: "{{record.id}}",
            leadName: "{{record.name}}",
            leadEmail: "{{record.email}}"
          }
        }
      }
    ],
    status: true,
    createdAt: "2023-11-15T09:45:00Z",
    updatedAt: "2024-04-05T13:30:00Z"
  },
  {
    id: "automation-7",
    name: "Atualização de Dados de Cliente",
    description: "Atualiza os dados do cliente quando houver alterações em sistemas externos",
    trigger: {
      id: "trigger-7",
      type: "webhook",
      config: {
        endpoint: "/webhooks/customer-update"
      }
    },
    actions: [
      {
        id: "action-8",
        type: "update_record",
        config: {
          model: "customer",
          lookup: {
            field: "external_id",
            value: "{{webhook.customer_id}}"
          },
          fields: {
            name: "{{webhook.name}}",
            email: "{{webhook.email}}",
            phone: "{{webhook.phone}}"
          }
        }
      }
    ],
    status: false,
    createdAt: "2023-12-05T15:30:00Z",
    updatedAt: "2024-01-20T11:45:00Z"
  }
];

// Helper functions
export const getAllAutomations = (): Automation[] => {
  return mockAutomations;
};

export const getAutomationById = (id: string): Automation | undefined => {
  return mockAutomations.find(automation => automation.id === id);
};

export const getActiveAutomations = (): Automation[] => {
  return mockAutomations.filter(automation => automation.status);
};
