import { NextRequest, NextResponse } from "next/server";
import { locales, Locale } from "@mug/intl/config";

function getLocale(request: NextRequest) {
  const localeCookie = request.cookies.get("NEXT_LOCALE")?.value;
  if (localeCookie && locales.includes(localeCookie as Locale)) {
    return localeCookie;
  }

  const acceptLanguage = request.headers.get("Accept-Language");
  if (acceptLanguage) {
    const preferredLocale = acceptLanguage
      .split(",")
      .map((lang) => lang.split(";")[0].trim())
      .find((lang) => {
        return locales.some(
          (locale) =>
            locale === lang ||
            locale.startsWith(lang) ||
            lang.startsWith(locale)
        );
      });

    if (preferredLocale) {
      const matchedLocale = locales.find(
        (locale) =>
          locale === preferredLocale ||
          locale.startsWith(preferredLocale) ||
          preferredLocale.startsWith(locale)
      );
      if (matchedLocale) return matchedLocale;
    }
  }

  return "pt-BR";
}

export function middleware(request: NextRequest) {
  const locale = getLocale(request);

  const response = NextResponse.next();

  if (!request.cookies.has("NEXT_LOCALE")) {
    response.cookies.set("NEXT_LOCALE", locale);
  }

  return response;
}

export const config = {
  matcher: ["/((?!api|_next|.*\\..*).*)"],
};
