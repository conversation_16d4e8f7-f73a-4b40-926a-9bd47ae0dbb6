# Flows - Inteligent proccess flows for everyone

## Authentication Setup

### Google OAuth Setup

To enable Google OAuth login, follow these steps:

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth client ID"
5. Select "Web application" as the application type
6. Add your application name
7. Add authorized JavaScript origins:
   - `http://localhost:3000` (for development)
   - Your production domain (for production)
8. Add authorized redirect URIs:
   - `http://localhost:3000/auth/google/callback` (for development)
   - `https://your-domain.com/auth/google/callback` (for production)
   - Add any custom subdomains you plan to use (e.g., `https://app.your-domain.com/auth/google/callback`)
9. Click "Create"
10. Copy the Client ID and Client Secret
11. Create a `.env.local` file in the root of your project (copy from `.env.local.example`)
12. Add your Google OAuth credentials:
    ```
    NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-client-id
    GOOGLE_CLIENT_SECRET=your-client-secret
    # The redirect URI is optional and defaults to the current domain + /auth/google/callback
    # NEXT_PUBLIC_GOOGLE_REDIRECT_URI=https://your-custom-domain.com/auth/google/callback
    ```

### Troubleshooting Google OAuth

If you encounter a "redirect_uri_mismatch" error:

1. Make sure the redirect URI in your code exactly matches what you configured in Google Cloud Console
2. Check for any trailing slashes or differences in the protocol (http vs https)
3. Verify that you've added the correct redirect URI to the authorized redirect URIs list in Google Cloud Console
4. If you're using multiple environments (development, staging, production), make sure each environment has its own redirect URI configured

## Development

### Environment Setup

1. Copy `.env.local.example` to `.env.local`
2. Fill in the required environment variables
3. Install dependencies:
   ```bash
   npm install
   ```
4. Start the development server:
   ```bash
   npm run dev
   ```

## Authentication Methods

The application supports multiple authentication methods:

1. **Username/Password**: Traditional login with username and password
2. **Google OAuth**: Login with Google account
3. **Magic Link**: Passwordless login via email link
4. **Two-Factor Authentication**: Additional security layer after login

## API Integration

The authentication system is designed to work with a backend API. The API endpoints used are:

- `/auth/login`: Username/password login
- `/auth/google-login`: Login with Google (sends Google user data)
- `/auth/magic-link`: Request magic link
- `/auth/magic-link/verify`: Verify magic link
- `/auth/two-factor/verify`: Verify two-factor authentication
- `/auth/password-reset`: Request password reset
- `/auth/password-reset/complete`: Complete password reset
- `/auth/invitation/accept`: Accept invitation
- `/auth/logout`: Logout

### Google Login Flow

The Google login flow works as follows:

1. User clicks "Login with Google" button
2. User is redirected to Google's OAuth consent screen
3. After user grants permission, Google redirects back to our callback URL with an authorization code
4. The frontend exchanges this code for Google user data (ID, email, name, etc.)
5. The frontend sends this data to our backend endpoint `/auth/google-login`
6. The backend creates or updates the user account and returns authentication tokens
7. The frontend stores these tokens and completes the login process

The Google login request payload has the following structure:

```typescript
interface GoogleLoginRequestDto {
  googleId: string;   // Google's unique user ID
  email: string;      // User's email address
  name: string;       // User's full name
  picture?: string;   // URL to user's profile picture (optional)
  locale?: string;    // User's locale preference (optional)
}
```
