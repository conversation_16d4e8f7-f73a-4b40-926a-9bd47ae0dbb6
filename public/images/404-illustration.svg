<?xml version="1.0" encoding="UTF-8"?>
<svg width="400px" height="400px" viewBox="0 0 400 400" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>404 Illustration</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#6366F1" offset="0%"></stop>
            <stop stop-color="#4F46E5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#E0E7FF" offset="0%"></stop>
            <stop stop-color="#C7D2FE" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="404-Illustration" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="Background" fill="url(#linearGradient-2)" cx="200" cy="200" r="200"></circle>
        <g id="404-Text" transform="translate(60.000000, 120.000000)">
            <path d="M66,160 L66,120 L26,120 L26,100 L76,0 L86,0 L86,100 L106,100 L106,120 L86,120 L86,160 L66,160 Z M66,100 L66,33 L33,100 L66,100 Z" id="4" fill="url(#linearGradient-1)"></path>
            <path d="M146,160 C126.666667,160 112,153.333333 102,140 C92,126.666667 87,108.333333 87,85 C87,61.6666667 92,43.3333333 102,30 C112,16.6666667 126.666667,10 146,10 C165.333333,10 180,16.6666667 190,30 C200,43.3333333 205,61.6666667 205,85 C205,108.333333 200,126.666667 190,140 C180,153.333333 165.333333,160 146,160 Z M146,140 C157.333333,140 165.666667,135 171,125 C176.333333,115 179,101.666667 179,85 C179,68.3333333 176.333333,55 171,45 C165.666667,35 157.333333,30 146,30 C134.666667,30 126.333333,35 121,45 C115.666667,55 113,68.3333333 113,85 C113,101.666667 115.666667,115 121,125 C126.333333,135 134.666667,140 146,140 Z" id="0" fill="url(#linearGradient-1)"></path>
            <path d="M246,160 L246,120 L206,120 L206,100 L256,0 L266,0 L266,100 L286,100 L286,120 L266,120 L266,160 L246,160 Z M246,100 L246,33 L213,100 L246,100 Z" id="4" fill="url(#linearGradient-1)"></path>
        </g>
        <g id="Magnifying-Glass" transform="translate(240.000000, 240.000000)">
            <circle id="Glass" stroke="#4F46E5" stroke-width="8" fill="#FFFFFF" cx="40" cy="40" r="40"></circle>
            <line x1="68" y1="68" x2="100" y2="100" id="Handle" stroke="#4F46E5" stroke-width="8" stroke-linecap="round"></line>
        </g>
        <g id="Document" transform="translate(100.000000, 240.000000)">
            <rect id="Paper" fill="#FFFFFF" x="0" y="0" width="80" height="100" rx="4"></rect>
            <line x1="20" y1="20" x2="60" y2="20" id="Line-1" stroke="#C7D2FE" stroke-width="4" stroke-linecap="round"></line>
            <line x1="20" y1="40" x2="60" y2="40" id="Line-2" stroke="#C7D2FE" stroke-width="4" stroke-linecap="round"></line>
            <line x1="20" y1="60" x2="60" y2="60" id="Line-3" stroke="#C7D2FE" stroke-width="4" stroke-linecap="round"></line>
            <line x1="20" y1="80" x2="40" y2="80" id="Line-4" stroke="#C7D2FE" stroke-width="4" stroke-linecap="round"></line>
        </g>
    </g>
</svg>
