{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "", "css": "src/app/globals.css", "baseColor": "neutral", "cssVariables": true, "prefix": ""}, "aliases": {"components": "@mug/components", "contexts": "@mug/contexts", "features": "@mug/features", "hooks": "@mug/hooks", "lib": "@mug/lib", "models": "@mug/models", "pages": "@mug/pages", "services": "@mug/services", "templates": "@mug/templates", "ui": "@mug/components/ui", "utils": "@mug/lib/utils"}, "iconLibrary": "lucide"}